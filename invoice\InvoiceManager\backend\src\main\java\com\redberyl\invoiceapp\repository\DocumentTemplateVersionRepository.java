package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentTemplateVersionRepository extends JpaRepository<DocumentTemplateVersion, Long> {
    List<DocumentTemplateVersion> findByTemplateId(Long templateId);
    Optional<DocumentTemplateVersion> findByTemplateIdAndVersionNumber(Long templateId, Integer versionNumber);
    List<DocumentTemplateVersion> findByTemplateIdAndIsActiveTrue(Long templateId);
}
