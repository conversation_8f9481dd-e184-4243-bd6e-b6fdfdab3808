package com.redberyl.invoiceapp.util;

import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class for validating invoice data
 */
@Component
public class InvoiceValidator {

    private static InvoiceRepository invoiceRepository;

    @Autowired
    public InvoiceValidator(InvoiceRepository invoiceRepository) {
        InvoiceValidator.invoiceRepository = invoiceRepository;
    }

    /**
     * Validate and fix invoice data
     * @param invoiceDto The invoice DTO to validate
     * @return The validated and fixed invoice DTO
     */
    public static InvoiceDto validateAndFix(InvoiceDto invoiceDto) {
        if (invoiceDto == null) {
            throw new NullConstraintViolationException("invoiceDto", "Invoice data cannot be null");
        }

        // Log candidate data before validation
        System.out.println("InvoiceValidator - Before validation - Candidate ID: " + invoiceDto.getCandidateId());
        System.out.println("InvoiceValidator - Before validation - Candidate: " + invoiceDto.getCandidate());

        // Validate required fields
        if (invoiceDto.getClientId() == null) {
            throw new NullConstraintViolationException("clientId", "Client ID is required");
        }

        if (invoiceDto.getInvoiceTypeId() == null) {
            throw new NullConstraintViolationException("invoiceTypeId", "Invoice Type ID is required");
        }

        // Validate invoice number
        if (invoiceDto.getInvoiceNumber() == null || invoiceDto.getInvoiceNumber().trim().isEmpty()) {
            // Generate a sequential invoice number with proper formatting (INV-001, INV-002, etc.)
            try {
                List<Invoice> invoices = invoiceRepository.findAll();
                int nextNumber = 1;

                if (!invoices.isEmpty()) {
                    // Find the highest invoice number
                    List<Integer> invoiceNumbers = invoices.stream()
                        .map(Invoice::getInvoiceNumber)
                        .filter(num -> num != null && num.startsWith("INV-"))
                        .map(num -> {
                            try {
                                String numPart = num.substring(4); // Extract the number part after "INV-"
                                return Integer.parseInt(numPart);
                            } catch (NumberFormatException e) {
                                return 0;
                            }
                        })
                        .collect(Collectors.toList());

                    if (!invoiceNumbers.isEmpty()) {
                        nextNumber = Collections.max(invoiceNumbers) + 1;
                    }
                }

                // Format with leading zeros (e.g., INV-001)
                invoiceDto.setInvoiceNumber(String.format("INV-%03d", nextNumber));
                System.out.println("Generated sequential invoice number: " + invoiceDto.getInvoiceNumber());
            } catch (Exception e) {
                // Fallback to timestamp-based number if there's an error
                invoiceDto.setInvoiceNumber("INV-" + System.currentTimeMillis());
                System.out.println("Error generating sequential invoice number, using timestamp: " + invoiceDto.getInvoiceNumber());
            }
        } else if (!invoiceDto.getInvoiceNumber().startsWith("INV-")) {
            // Ensure the invoice number has the correct prefix
            invoiceDto.setInvoiceNumber("INV-" + invoiceDto.getInvoiceNumber());
        }

        // Validate billing amount
        try {
            BigDecimal billingAmount = invoiceDto.getBillingAmount();
            if (billingAmount == null || billingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                invoiceDto.setBillingAmount(new BigDecimal("100.00"));
            }
        } catch (Exception e) {
            invoiceDto.setBillingAmount(new BigDecimal("100.00"));
        }

        // Calculate and validate tax amount (18% GST)
        try {
            BigDecimal billingAmount = invoiceDto.getBillingAmount();
            if (billingAmount != null && billingAmount.compareTo(BigDecimal.ZERO) > 0) {
                // Calculate 18% of billing amount for GST
                BigDecimal taxAmount = billingAmount.multiply(new BigDecimal("0.18")).setScale(2, BigDecimal.ROUND_HALF_UP);
                invoiceDto.setTaxAmount(taxAmount);
            } else {
                invoiceDto.setTaxAmount(new BigDecimal("18.00"));
            }
        } catch (Exception e) {
            invoiceDto.setTaxAmount(new BigDecimal("18.00"));
        }

        // Calculate and validate total amount (billing amount + tax amount)
        try {
            BigDecimal billingAmount = invoiceDto.getBillingAmount();
            BigDecimal taxAmount = invoiceDto.getTaxAmount();

            if (billingAmount != null && taxAmount != null) {
                BigDecimal totalAmount = billingAmount.add(taxAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
                invoiceDto.setTotalAmount(totalAmount);
            } else {
                invoiceDto.setTotalAmount(new BigDecimal("118.00"));
            }
        } catch (Exception e) {
            invoiceDto.setTotalAmount(new BigDecimal("118.00"));
        }

        // Validate invoice date
        try {
            LocalDate invoiceDate = invoiceDto.getInvoiceDate();
            if (invoiceDate == null) {
                invoiceDto.setInvoiceDate(LocalDate.now());
            }
        } catch (Exception e) {
            invoiceDto.setInvoiceDate(LocalDate.now());
        }

        // Validate due date
        try {
            LocalDate dueDate = invoiceDto.getDueDate();
            if (dueDate == null) {
                invoiceDto.setDueDate(LocalDate.now().plusDays(30));
            }
        } catch (Exception e) {
            invoiceDto.setDueDate(LocalDate.now().plusDays(30));
        }

        // Set default values for boolean fields
        if (invoiceDto.getIsRecurring() == null) {
            invoiceDto.setIsRecurring(false);
        }

        if (invoiceDto.getPublishedToFinance() == null) {
            invoiceDto.setPublishedToFinance(false);
        }

        // Log candidate data after validation
        System.out.println("InvoiceValidator - After validation - Candidate ID: " + invoiceDto.getCandidateId());
        System.out.println("InvoiceValidator - After validation - Candidate: " + invoiceDto.getCandidate());

        return invoiceDto;
    }
}
