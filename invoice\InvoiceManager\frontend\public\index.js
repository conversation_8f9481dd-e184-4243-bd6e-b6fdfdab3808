// This file helps with client-side routing
// It ensures that all routes are redirected to index.html
// so that React Router can handle them

// If you're seeing a 404 error, this script will help
console.log('Client-side routing helper loaded');

// In case of a 404 error, redirect to the root
// But exclude certain paths that should not be redirected
const excludedPaths = ['/', '/bdms', '/clients', '/invoices', '/payments', '/crm', '/documents', '/candidates'];
const isExcluded = excludedPaths.some(path => window.location.pathname === path || window.location.pathname.startsWith(path + '/'));

if (!isExcluded && !window.location.pathname.startsWith('/static/')) {
  console.log('Redirecting from', window.location.pathname, 'to root for client-side routing');
  window.history.replaceState(null, document.title, '/');
  window.location.reload();
}
