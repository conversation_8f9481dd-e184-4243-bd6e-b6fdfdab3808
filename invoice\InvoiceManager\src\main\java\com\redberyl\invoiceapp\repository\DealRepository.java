package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Deal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DealRepository extends JpaRepository<Deal, Long> {
    @Query("SELECT d FROM Deal d LEFT JOIN FETCH d.lead LEFT JOIN FETCH d.client WHERE d.id = :id")
    Optional<Deal> findByIdWithLeadAndClient(@Param("id") Long id);

    List<Deal> findByLeadId(Long leadId);

    List<Deal> findByClientId(Long clientId);

    List<Deal> findByStatus(String status);
}
