# OneDrive Integration Setup Guide

This guide explains how to set up and use the OneDrive integration for saving invoice PDFs.

## 🚀 Features

- **Seamless Authentication**: OAuth 2.0 flow with Microsoft Graph API
- **Automatic PDF Generation**: Generate PDFs from invoice data
- **Smart Upload**: Upload PDFs directly to your OneDrive folder
- **Error Handling**: Comprehensive error handling and user feedback
- **Multiple Integration Points**: Available in invoice list, details dialog, and bulk operations

## 📋 Prerequisites

1. **Microsoft Azure App Registration** (Already configured)
2. **OneDrive Business Account** with access to the specified SharePoint path
3. **Backend and Frontend Applications** running

## 🔧 Configuration

### Backend Configuration

The backend is already configured with the following settings in `application.properties`:

```properties
# OneDrive Integration Configuration
onedrive.client.id=********-ad2a-4ac0-8806-e2705653949a
onedrive.tenant.id=********-a340-4380-88ed-a8989a932425
onedrive.client.secret=****************************************
onedrive.redirect.uri=http://localhost:8091/api/onedrive/callback
onedrive.scope=https://graph.microsoft.com/Files.ReadWrite.All
onedrive.base.path=/personal/prathamesh_kadam_redberyltech_com/Documents/RedBeryl
```

### Frontend Configuration

The frontend automatically uses the backend API endpoints. No additional configuration required.

## 🎯 Usage

### 1. Individual Invoice Upload

**From Invoice List:**
- Click the OneDrive button (cloud icon) next to any invoice in the table
- The system will authenticate if needed and upload the PDF

**From Invoice Details Dialog:**
- Open any invoice details
- Click the "Save in OneDrive" button in the footer
- PDF will be generated and uploaded automatically

### 2. Bulk Operations

**From Invoice List Header:**
- Select multiple invoices using checkboxes
- Click "Save in OneDrive" button in the header
- All selected invoices will be uploaded

### 3. Authentication Flow

1. **First Time Use:**
   - Click any OneDrive button
   - A popup window will open for Microsoft authentication
   - Sign in with your Microsoft account
   - Grant permissions to the application
   - The popup will close automatically upon success

2. **Subsequent Uses:**
   - The system remembers your authentication
   - PDFs upload directly without additional prompts

## 📁 File Organization

PDFs are saved to your OneDrive in the following structure:

```
OneDrive/
└── personal/
    └── prathamesh_kadam_redberyltech_com/
        └── Documents/
            └── RedBeryl/
                └── Invoices/
                    ├── Invoice_RB25-26004_2025-01-12_14-30-45.pdf
                    ├── Invoice_RB25-26001_2025-01-12_14-31-20.pdf
                    └── ...
```

**File Naming Convention:**
- Format: `Invoice_{InvoiceNumber}_{Timestamp}.pdf`
- Example: `Invoice_RB25-26004_2025-01-12_14-30-45.pdf`

## 🧪 Testing

### Test Page

Access the OneDrive test page at: `http://localhost:3060/onedrive-test`

This page provides:
- Authentication status check
- Authorization URL generation test
- Authentication flow test
- PDF upload test
- OneDrive button demos

### Manual Testing

1. **Start the applications:**
   ```bash
   # Backend
   cd backend
   mvn spring-boot:run

   # Frontend
   cd frontend
   npm run dev
   ```

2. **Test authentication:**
   - Go to `http://localhost:3060/onedrive-test`
   - Click "Connect to OneDrive"
   - Complete the authentication flow

3. **Test PDF upload:**
   - Go to the Invoices page
   - Click the OneDrive button on any invoice
   - Check your OneDrive for the uploaded file

## 🔍 Troubleshooting

### Common Issues

1. **Authentication Popup Blocked**
   - Enable popups for `localhost:3060`
   - Try again after enabling popups

2. **Upload Fails**
   - Check browser console for errors
   - Verify authentication status
   - Ensure OneDrive has sufficient storage

3. **PDF Generation Fails**
   - Check if invoice data is complete
   - Verify backend PDF generation service is running
   - Check browser console for errors

4. **Permission Denied**
   - Verify the Microsoft app has correct permissions
   - Check if the user has access to the SharePoint path
   - Re-authenticate if needed

### Debug Information

**Backend Logs:**
- Check Spring Boot console for OneDrive API errors
- Look for authentication and upload logs

**Frontend Console:**
- Open browser developer tools
- Check for JavaScript errors
- Monitor network requests to `/api/onedrive/*`

**Test Endpoints:**
- `GET /api/onedrive/auth-url` - Get authorization URL
- `GET /api/onedrive/check-auth` - Check authentication status
- `POST /api/onedrive/upload-pdf` - Upload PDF file

## 🔐 Security Notes

1. **Access Tokens**: Stored in browser localStorage, cleared on logout
2. **Client Secret**: Stored securely in backend configuration
3. **Permissions**: Limited to Files.ReadWrite.All scope
4. **HTTPS**: Use HTTPS in production for secure token transmission

## 📚 API Reference

### Backend Endpoints

- `GET /api/onedrive/auth-url` - Get Microsoft OAuth authorization URL
- `GET /api/onedrive/callback` - Handle OAuth callback
- `POST /api/onedrive/upload-pdf` - Upload PDF file
- `POST /api/onedrive/upload-invoice-pdf` - Upload invoice PDF with base64 content
- `GET /api/onedrive/check-auth` - Verify authentication status

### Frontend Services

- `oneDriveService.authenticate()` - Initiate authentication flow
- `oneDriveService.uploadPdf()` - Upload PDF blob
- `oneDriveService.checkAuthentication()` - Check auth status

## 🎉 Success!

Your OneDrive integration is now ready! Users can seamlessly save invoice PDFs to OneDrive with just one click.

For support or questions, check the browser console and backend logs for detailed error information.
