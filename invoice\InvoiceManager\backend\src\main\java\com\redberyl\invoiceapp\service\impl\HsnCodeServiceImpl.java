package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.HsnCodeDto;
import com.redberyl.invoiceapp.entity.HsnCode;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import com.redberyl.invoiceapp.service.HsnCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class HsnCodeServiceImpl implements HsnCodeService {

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Override
    public List<HsnCodeDto> getAllHsnCodes() {
        List<HsnCode> hsnCodes = hsnCodeRepository.findAll();
        if (hsnCodes.isEmpty()) {
            // Create a sample HSN code if none exist
            try {
                HsnCode sampleHsnCode = HsnCode.builder()
                        .code("9983")
                        .description("IT Services")
                        .gstRate(new java.math.BigDecimal("18.0"))
                        .build();
                HsnCode savedHsnCode = hsnCodeRepository.save(sampleHsnCode);
                return List.of(convertToDto(savedHsnCode));
            } catch (Exception e) {
                // If we can't create a sample, throw the original exception
                throw new NoContentException("No HSN codes found");
            }
        }
        return hsnCodes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public HsnCodeDto getHsnCodeById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "HSN code ID cannot be null");
        }

        HsnCode hsnCode = hsnCodeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("HSN Code not found with id: " + id));
        return convertToDto(hsnCode);
    }

    @Override
    public HsnCodeDto getHsnCodeByCode(String code) {
        if (!StringUtils.hasText(code)) {
            throw new NullConstraintViolationException("code", "HSN code cannot be empty");
        }

        HsnCode hsnCode = hsnCodeRepository.findByCode(code)
                .orElseThrow(() -> new ResourceNotFoundException("HSN Code not found with code: " + code));
        return convertToDto(hsnCode);
    }

    private void validateHsnCodeDto(HsnCodeDto hsnCodeDto) {
        if (hsnCodeDto == null) {
            throw new NullConstraintViolationException("hsnCodeDto", "HSN code data cannot be null");
        }

        if (!StringUtils.hasText(hsnCodeDto.getCode())) {
            throw new NullConstraintViolationException("code", "HSN code cannot be empty");
        }

        // Check for duplicate code
        if (hsnCodeDto.getId() == null && hsnCodeRepository.findByCode(hsnCodeDto.getCode()).isPresent()) {
            throw new UniqueConstraintViolationException("code", "HSN code already exists: " + hsnCodeDto.getCode());
        }
    }

    @Override
    @Transactional
    public HsnCodeDto createHsnCode(HsnCodeDto hsnCodeDto) {
        validateHsnCodeDto(hsnCodeDto);

        try {
            HsnCode hsnCode = convertToEntity(hsnCodeDto);
            HsnCode savedHsnCode = hsnCodeRepository.save(hsnCode);
            return convertToDto(savedHsnCode);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("code", "HSN code already exists");
            } else {
                throw new CustomException("Error creating HSN code: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating HSN code", e);
        }
    }

    @Override
    @Transactional
    public HsnCodeDto updateHsnCode(Long id, HsnCodeDto hsnCodeDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "HSN code ID cannot be null");
        }

        HsnCode existingHsnCode = hsnCodeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("HSN Code not found with id: " + id));

        // Check for duplicate code if code is being changed
        if (StringUtils.hasText(hsnCodeDto.getCode()) &&
                !hsnCodeDto.getCode().equals(existingHsnCode.getCode()) &&
                hsnCodeRepository.findByCode(hsnCodeDto.getCode()).isPresent()) {
            throw new UniqueConstraintViolationException("code", "HSN code already exists: " + hsnCodeDto.getCode());
        }

        try {
            if (StringUtils.hasText(hsnCodeDto.getCode())) {
                existingHsnCode.setCode(hsnCodeDto.getCode());
            }

            if (StringUtils.hasText(hsnCodeDto.getDescription())) {
                existingHsnCode.setDescription(hsnCodeDto.getDescription());
            }

            if (hsnCodeDto.getGstRate() != null) {
                existingHsnCode.setGstRate(hsnCodeDto.getGstRate());
            }

            HsnCode updatedHsnCode = hsnCodeRepository.save(existingHsnCode);
            return convertToDto(updatedHsnCode);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("code", "HSN code already exists");
            } else {
                throw new CustomException("Error updating HSN code: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating HSN code", e);
        }
    }

    @Override
    @Transactional
    public void deleteHsnCode(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "HSN code ID cannot be null");
        }

        if (!hsnCodeRepository.existsById(id)) {
            throw new ResourceNotFoundException("HSN Code not found with id: " + id);
        }

        try {
            hsnCodeRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete HSN code because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting HSN code: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting HSN code", e);
        }
    }

    private HsnCodeDto convertToDto(HsnCode hsnCode) {
        return HsnCodeDto.builder()
                .id(hsnCode.getId())
                .code(hsnCode.getCode())
                .description(hsnCode.getDescription())
                .gstRate(hsnCode.getGstRate())
                .build();
    }

    private HsnCode convertToEntity(HsnCodeDto hsnCodeDto) {
        return HsnCode.builder()
                .id(hsnCodeDto.getId())
                .code(hsnCodeDto.getCode())
                .description(hsnCodeDto.getDescription())
                .gstRate(hsnCodeDto.getGstRate())
                .build();
    }
}
