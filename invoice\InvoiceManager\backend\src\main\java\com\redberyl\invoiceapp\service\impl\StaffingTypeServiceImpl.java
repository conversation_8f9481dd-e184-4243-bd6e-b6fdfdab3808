package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.StaffingTypeDto;
import com.redberyl.invoiceapp.entity.StaffingType;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.StaffingTypeRepository;
import com.redberyl.invoiceapp.service.StaffingTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StaffingTypeServiceImpl implements StaffingTypeService {

    @Autowired
    private StaffingTypeRepository staffingTypeRepository;

    @Override
    public List<StaffingTypeDto> getAllStaffingTypes() {
        List<StaffingType> staffingTypes = staffingTypeRepository.findAll();
        if (staffingTypes.isEmpty()) {
            throw new NoContentException("No staffing types found");
        }
        return staffingTypes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public StaffingTypeDto getStaffingTypeById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Staffing Type ID cannot be null");
        }

        StaffingType staffingType = staffingTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Staffing Type not found with id: " + id));
        return convertToDto(staffingType);
    }

    @Override
    public StaffingTypeDto getStaffingTypeByName(String name) {
        if (!StringUtils.hasText(name)) {
            throw new NullConstraintViolationException("name", "Staffing Type name cannot be empty");
        }

        StaffingType staffingType = staffingTypeRepository.findByName(name)
                .orElseThrow(() -> new ResourceNotFoundException("Staffing Type not found with name: " + name));
        return convertToDto(staffingType);
    }

    private void validateStaffingTypeDto(StaffingTypeDto staffingTypeDto) {
        if (staffingTypeDto == null) {
            throw new NullConstraintViolationException("staffingTypeDto", "Staffing Type data cannot be null");
        }

        if (!StringUtils.hasText(staffingTypeDto.getName())) {
            throw new NullConstraintViolationException("name", "Staffing Type name cannot be empty");
        }

        // Check for duplicate name if it's a new staffing type
        if (staffingTypeDto.getId() == null &&
                staffingTypeRepository.findByName(staffingTypeDto.getName()).isPresent()) {
            throw new UniqueConstraintViolationException("name",
                    "Staffing Type with name " + staffingTypeDto.getName() + " already exists");
        }
    }

    @Override
    @Transactional
    public StaffingTypeDto createStaffingType(StaffingTypeDto staffingTypeDto) {
        validateStaffingTypeDto(staffingTypeDto);

        try {
            StaffingType staffingType = convertToEntity(staffingTypeDto);
            StaffingType savedStaffingType = staffingTypeRepository.save(staffingType);
            return convertToDto(savedStaffingType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Staffing Type with this name already exists");
            } else {
                throw new CustomException("Error creating Staffing Type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating Staffing Type", e);
        }
    }

    @Override
    @Transactional
    public StaffingTypeDto updateStaffingType(Long id, StaffingTypeDto staffingTypeDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Staffing Type ID cannot be null");
        }

        if (staffingTypeDto == null) {
            throw new NullConstraintViolationException("staffingTypeDto", "Staffing Type data cannot be null");
        }

        StaffingType existingStaffingType = staffingTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Staffing Type not found with id: " + id));

        // Check for duplicate name if it's being changed
        if (StringUtils.hasText(staffingTypeDto.getName()) &&
                !staffingTypeDto.getName().equals(existingStaffingType.getName()) &&
                staffingTypeRepository.findByName(staffingTypeDto.getName()).isPresent()) {
            throw new UniqueConstraintViolationException("name",
                    "Staffing Type with name " + staffingTypeDto.getName() + " already exists");
        }

        try {
            if (StringUtils.hasText(staffingTypeDto.getName())) {
                existingStaffingType.setName(staffingTypeDto.getName());
            }

            // Update description (can be null)
            existingStaffingType.setDescription(staffingTypeDto.getDescription());

            StaffingType updatedStaffingType = staffingTypeRepository.save(existingStaffingType);
            return convertToDto(updatedStaffingType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Staffing Type with this name already exists");
            } else {
                throw new CustomException("Error updating Staffing Type: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | UniqueConstraintViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating Staffing Type", e);
        }
    }

    @Override
    @Transactional
    public void deleteStaffingType(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Staffing Type ID cannot be null");
        }

        if (!staffingTypeRepository.existsById(id)) {
            throw new ResourceNotFoundException("Staffing Type not found with id: " + id);
        }

        try {
            staffingTypeRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete Staffing Type because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting Staffing Type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting Staffing Type", e);
        }
    }

    private StaffingTypeDto convertToDto(StaffingType staffingType) {
        return StaffingTypeDto.builder()
                .id(staffingType.getId())
                .name(staffingType.getName())
                .description(staffingType.getDescription())
                .build();
    }

    private StaffingType convertToEntity(StaffingTypeDto staffingTypeDto) {
        return StaffingType.builder()
                .id(staffingTypeDto.getId())
                .name(staffingTypeDto.getName())
                .description(staffingTypeDto.getDescription())
                .build();
    }
}
