-- Check if file_path column exists in document_templates table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'document_templates'
        AND column_name = 'file_path'
    ) THEN
        -- Add file_path column if it doesn't exist
        ALTER TABLE document_templates ADD COLUMN file_path TEXT;
    END IF;
END $$;

-- Check if file_path column exists in generated_documents table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'generated_documents'
        AND column_name = 'file_path'
    ) THEN
        -- Add file_path column if it doesn't exist
        ALTER TABLE generated_documents ADD COLUMN file_path TEXT;
    END IF;
END $$;

-- Check if file_name column exists in generated_documents table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'generated_documents'
        AND column_name = 'file_name'
    ) THEN
        -- Add file_name column if it doesn't exist
        ALTER TABLE generated_documents ADD COLUMN file_name VARCHAR(255);
    END IF;
END $$;
