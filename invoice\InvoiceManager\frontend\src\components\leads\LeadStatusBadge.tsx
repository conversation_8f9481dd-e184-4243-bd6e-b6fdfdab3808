import React from "react";
import { Badge } from "@/components/ui/badge";

interface LeadStatusBadgeProps {
  status: string;
}

const LeadStatusBadge: React.FC<LeadStatusBadgeProps> = ({ status }) => {
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "new":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "contacted":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "qualified":
        return "bg-green-100 text-green-800 border-green-200";
      case "not interested":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Badge 
      variant="outline" 
      className={`${getStatusBadgeClass(status)} px-2.5 py-0.5 rounded-full text-xs font-medium`}
    >
      {status}
    </Badge>
  );
};

export default LeadStatusBadge;
