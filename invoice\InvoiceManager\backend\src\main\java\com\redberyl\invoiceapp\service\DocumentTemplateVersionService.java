package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;

import java.util.List;
import java.util.Optional;

public interface DocumentTemplateVersionService {
    List<DocumentTemplateVersionDto> getAllDocumentTemplateVersions();
    DocumentTemplateVersionDto getDocumentTemplateVersionById(Long id);
    List<DocumentTemplateVersionDto> getDocumentTemplateVersionsByTemplateId(Long templateId);
    Optional<DocumentTemplateVersionDto> getDocumentTemplateVersionByTemplateIdAndVersionNumber(Long templateId, Integer versionNumber);
    List<DocumentTemplateVersionDto> getActiveDocumentTemplateVersionsByTemplateId(Long templateId);
    DocumentTemplateVersionDto createDocumentTemplateVersion(DocumentTemplateVersionDto documentTemplateVersionDto);
    DocumentTemplateVersionDto updateDocumentTemplateVersion(Long id, DocumentTemplateVersionDto documentTemplateVersionDto);
    DocumentTemplateVersionDto activateDocumentTemplateVersion(Long id);
    DocumentTemplateVersionDto deactivateDocumentTemplateVersion(Long id);
    void deleteDocumentTemplateVersion(Long id);
}
