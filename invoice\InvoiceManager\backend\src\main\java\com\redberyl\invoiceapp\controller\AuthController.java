package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.auth.LoginRequestDto;
import com.redberyl.invoiceapp.dto.auth.LoginResponseDto;
import com.redberyl.invoiceapp.dto.auth.MessageResponseDto;
import com.redberyl.invoiceapp.dto.auth.SignupRequestDto;
import com.redberyl.invoiceapp.entity.auth.ERole;
import com.redberyl.invoiceapp.entity.auth.Role;
import com.redberyl.invoiceapp.entity.auth.User;
import com.redberyl.invoiceapp.repository.RoleRepository;
import com.redberyl.invoiceapp.repository.UserRepository;
import com.redberyl.invoiceapp.security.jwt.JwtUtils;
import com.redberyl.invoiceapp.security.services.UserDetailsImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpStatus;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@CrossOrigin(originPatterns = "*",
        allowedHeaders = "*",
        methods = {
            RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.PATCH,
            RequestMethod.DELETE, RequestMethod.OPTIONS, RequestMethod.HEAD
        },
        allowCredentials = "true",
        maxAge = 3600)
@RestController
@RequestMapping("")

@Tag(name = "Authentication", description = "Authentication API")
public class AuthController {
    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    PasswordEncoder encoder;

    @Autowired
    JwtUtils jwtUtils;

    @PostMapping("/auth/login")
    @Operation(summary = "Login", description = "Login")
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequestDto loginRequest) {
        System.out.println("Login request received for username: " + loginRequest.getUsername());

        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        String jwt = jwtUtils.generateJwtToken(authentication);

        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        List<String> roles = userDetails.getAuthorities().stream()
                .map(item -> item.getAuthority())
                .collect(Collectors.toList());

        // Create response with accessToken (renamed from token)
        LoginResponseDto response = LoginResponseDto.builder()
                .accessToken(jwt)
                .tokenType("Bearer")
                .id(userDetails.getId())
                .username(userDetails.getUsername())
                .email(userDetails.getEmail())
                .roles(roles.toArray(new String[0]))
                .build();

        System.out.println("Login successful for user: " + userDetails.getUsername());
        System.out.println("Generated token: " + jwt);

        return ResponseEntity.ok(response);
    }

    @PostMapping("/auth/signin")
    @Operation(summary = "Sign in (Deprecated)", description = "Alias for /login endpoint")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequestDto loginRequest) {
        return login(loginRequest);
    }

    @GetMapping("/test")
    @Operation(summary = "Test endpoint", description = "Test if CORS is working")
    public ResponseEntity<?> testEndpoint() {
        return ResponseEntity.ok(new MessageResponseDto("CORS is working!"));
    }

    @GetMapping("/test-roles")
    @Operation(summary = "Test roles", description = "Test if roles are properly set up in the database")
    public ResponseEntity<?> testRoles() {
        try {
            StringBuilder result = new StringBuilder("Available roles: ");

            // Check if ROLE_USER exists
            roleRepository.findByName(ERole.ROLE_USER)
                    .ifPresentOrElse(
                            role -> result.append("ROLE_USER found, "),
                            () -> result.append("ROLE_USER not found, "));

            // Check if ROLE_ADMIN exists
            roleRepository.findByName(ERole.ROLE_ADMIN)
                    .ifPresentOrElse(
                            role -> result.append("ROLE_ADMIN found, "),
                            () -> result.append("ROLE_ADMIN not found, "));

            // Check if ROLE_MODERATOR exists
            roleRepository.findByName(ERole.ROLE_MODERATOR)
                    .ifPresentOrElse(
                            role -> result.append("ROLE_MODERATOR found"),
                            () -> result.append("ROLE_MODERATOR not found"));

            return ResponseEntity.ok(new MessageResponseDto(result.toString()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponseDto("Error checking roles: " + e.getMessage()));
        }
    }

    @PostMapping("/auth/register")
    @Operation(summary = "Register", description = "Register")
    @CrossOrigin(originPatterns = "*", allowedHeaders = "*", methods = {RequestMethod.POST, RequestMethod.OPTIONS}, allowCredentials = "true")
    public ResponseEntity<?> register(@Valid @RequestBody SignupRequestDto signUpRequest) {
        try {
            // Log the incoming request (without password)
            System.out.println("Signup request received for username: " + signUpRequest.getUsername() +
                    ", email: " + signUpRequest.getEmail() +
                    ", roles: " + (signUpRequest.getRoles() != null ? signUpRequest.getRoles() : "null"));

            // Print the entire request for debugging
            System.out.println("Full signup request: " + signUpRequest);

            if (userRepository.existsByUsername(signUpRequest.getUsername())) {
                return ResponseEntity
                        .badRequest()
                        .body(new MessageResponseDto("Error: Username is already taken!"));
            }

            if (userRepository.existsByEmail(signUpRequest.getEmail())) {
                return ResponseEntity
                        .badRequest()
                        .body(new MessageResponseDto("Error: Email is already in use!"));
            }

            // Create new user's account
            User user = new User();
            user.setUsername(signUpRequest.getUsername());
            user.setEmail(signUpRequest.getEmail());
            user.setPassword(encoder.encode(signUpRequest.getPassword()));

            Set<String> strRoles = signUpRequest.getRoles();
            Set<Role> roles = new HashSet<>();

            try {
                if (strRoles == null || strRoles.isEmpty()) {
                    System.out.println("No roles specified, defaulting to ROLE_USER");
                    Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                            .orElseThrow(() -> new RuntimeException("Error: Role ROLE_USER is not found."));
                    roles.add(userRole);
                } else {
                    System.out.println("Processing roles: " + strRoles);

                    for (String role : strRoles) {
                        try {
                            System.out.println("Processing role: " + role);

                            // Check if the role already includes the ROLE_ prefix
                            if (role.startsWith("ROLE_")) {
                                // Try to match directly to an ERole enum
                                try {
                                    ERole eRole = ERole.valueOf(role);
                                    // Create a final copy of the role for use in the lambda
                                    final String roleName = role;
                                    Role foundRole = roleRepository.findByName(eRole)
                                            .orElseThrow(() -> new RuntimeException(
                                                    "Error: Role " + roleName + " is not found."));
                                    roles.add(foundRole);
                                    System.out.println("Added role with prefix: " + role);
                                    continue;
                                } catch (IllegalArgumentException e) {
                                    System.out.println(
                                            "Could not parse role with prefix: " + role + ", trying without prefix");
                                    // If we can't parse it directly, try without the prefix
                                    role = role.substring(5); // Remove "ROLE_"
                                }
                            }

                            // Process without ROLE_ prefix
                            switch (role.toLowerCase()) {
                                case "admin":
                                    Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                                            .orElseThrow(
                                                    () -> new RuntimeException("Error: Role ROLE_ADMIN is not found."));
                                    roles.add(adminRole);
                                    System.out.println("Added ROLE_ADMIN");
                                    break;
                                case "mod":
                                case "moderator":
                                    Role modRole = roleRepository.findByName(ERole.ROLE_MODERATOR)
                                            .orElseThrow(() -> new RuntimeException(
                                                    "Error: Role ROLE_MODERATOR is not found."));
                                    roles.add(modRole);
                                    System.out.println("Added ROLE_MODERATOR");
                                    break;
                                case "user":
                                    Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                                            .orElseThrow(
                                                    () -> new RuntimeException("Error: Role ROLE_USER is not found."));
                                    roles.add(userRole);
                                    System.out.println("Added ROLE_USER");
                                    break;
                                default:
                                    System.out.println("Unknown role: " + role + ", defaulting to ROLE_USER");
                                    Role defaultUserRole = roleRepository.findByName(ERole.ROLE_USER)
                                            .orElseThrow(
                                                    () -> new RuntimeException("Error: Role ROLE_USER is not found."));
                                    roles.add(defaultUserRole);
                            }
                        } catch (Exception e) {
                            System.err.println("Error processing role: " + role + " - " + e.getMessage());
                            e.printStackTrace();
                            // Default to USER role if there's an error
                            try {
                                Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                                        .orElseThrow(() -> new RuntimeException("Error: Role ROLE_USER is not found."));
                                roles.add(userRole);
                                System.out.println("Added default ROLE_USER due to error");
                            } catch (Exception ex) {
                                System.err
                                        .println("Critical error: Cannot find default USER role - " + ex.getMessage());
                                ex.printStackTrace();
                            }
                        }
                    }
                }

                if (roles.isEmpty()) {
                    // Fallback to ensure user has at least one role
                    Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                            .orElseThrow(() -> new RuntimeException("Error: Role ROLE_USER is not found."));
                    roles.add(userRole);
                }
            } catch (Exception e) {
                System.err.println("Error setting up roles: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("Error setting up user roles: " + e.getMessage());
            }

            user.setRoles(roles);
            userRepository.save(user);

            return ResponseEntity.ok(new MessageResponseDto("User registered successfully!"));
        } catch (Exception e) {
            System.err.println("Error in user registration: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity
                    .internalServerError()
                    .body(new MessageResponseDto("Error: Registration failed - " + e.getMessage()));
        }
    }

    @PostMapping("/auth/signup")
    @Operation(summary = "Sign up (Deprecated)", description = "Alias for /register endpoint")
    public ResponseEntity<?> registerUser(@Valid @RequestBody SignupRequestDto signUpRequest) {
        return register(signUpRequest);
    }

    @PostMapping("/auth/logout")
    @Operation(summary = "Logout", description = "Logout")
    public ResponseEntity<?> logout() {
        // In a stateless JWT authentication system, the server doesn't need to do
        // anything
        // The client should discard the token
        return ResponseEntity.ok(new MessageResponseDto("Logged out successfully"));
    }
}
