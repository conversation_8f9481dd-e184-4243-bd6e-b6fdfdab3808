import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { directApiService } from '@/services/directApiService';

/**
 * Hook to handle tab click events
 * This hook will detect when the user navigates to a specific page
 * and trigger the appropriate API calls
 */
export function useTabClickHandler() {
  const location = useLocation();
  
  useEffect(() => {
    // This effect runs whenever the location changes (user navigates to a new page)
    console.log('Location changed:', location.pathname);
    
    // Check if the user navigated to the projects page
    if (location.pathname === '/clients' || location.pathname === '/projects') {
      console.log('Projects tab detected, fetching projects data...');
      
      // Fetch projects data from the specific endpoint
      fetchProjectsData();
    }
  }, [location.pathname]);
  
  /**
   * Fetch projects data from the specific endpoint
   */
  const fetchProjectsData = async () => {
    try {
      console.log('Fetching projects data from specific endpoint...');
      
      // Make a direct call to the specific endpoint
      const projects = await directApiService.getProjectsFromSpecificEndpoint();
      
      console.log('Projects data fetched successfully:', projects);
    } catch (error) {
      console.error('Error fetching projects data:', error);
    }
  };
  
  return {
    fetchProjectsData
  };
}
