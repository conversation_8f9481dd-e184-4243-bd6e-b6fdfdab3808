<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Generation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1565C0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0D47A1;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #4CAF50;
        }
        .error {
            background: #FFEBEE;
            color: #C62828;
            border: 1px solid #F44336;
        }
        .info {
            background: #E3F2FD;
            color: #1565C0;
            border: 1px solid #2196F3;
        }
        #invoice-preview {
            border: 1px solid #ddd;
            margin: 20px 0;
            max-height: 600px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Invoice PDF Generation Test</h1>
        
        <div>
            <button class="test-button" onclick="testPdfGeneration()">Generate Test PDF</button>
            <button class="test-button" onclick="testOneDriveUpload()">Test OneDrive Upload</button>
            <button class="test-button" onclick="previewInvoice()">Preview Invoice</button>
        </div>

        <div id="status"></div>
        <div id="invoice-preview"></div>
    </div>

    <script>
        // Sample invoice data matching your screenshot
        const sampleInvoice = {
            id: "28",
            client: "saurabh",
            project: "hadapsar",
            candidate: "Prathamesh Kadam",
            invoiceType: "Services",
            staffingType: "Full-time",
            amount: "₹50,000.00",
            tax: "₹9,000.00",
            total: "₹59,000.00",
            issueDate: "2025-01-21",
            dueDate: "2025-02-21",
            status: "Draft",
            recurring: false,
            notes: "",
            employeeName: "Prathamesh Kadam",
            employeeEngagementCode: "ENG-0020",
            joiningDate: "2025-01-21",
            rate: "₹50,000.00",
            billAmount: "₹50,000.00",
            cgst: "9%",
            sgst: "9%",
            igst: "18%",
            netPayable: "₹59,000.00",
            bankName: "HDFC Bank",
            branchName: "MG Road Branch",
            accountName: "Acme Corporation Pvt Ltd",
            accountNo: "***********",
            ifscCode: "HDFC0001234",
            accountType: "Current",
            gstin: "29**********2Z5",
            cin: "U12345KA2020PTC012345",
            panNo: "**********",
            attendanceDays: 20,
            hsnCode: "465757"
        };

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function generateInvoiceHTML(invoice) {
            // Calculate amounts
            const dailyRate = parseFloat(invoice.rate?.replace(/[₹,]/g, '') || '50000');
            const days = invoice.attendanceDays || 20;
            const baseAmount = dailyRate;
            const cgstAmount = baseAmount * 0.09; // 9%
            const sgstAmount = baseAmount * 0.09; // 9%
            const totalAmount = baseAmount + cgstAmount + sgstAmount;

            const amounts = {
                baseAmount: `₹${baseAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
                cgstAmount: `₹${cgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
                sgstAmount: `₹${sgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
                totalAmount: `₹${totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`
            };

            return `
                <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; background: white; color: #333; font-size: 12px; line-height: 1.4;">
                    <!-- Header with Logo -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px;">
                        <div style="flex: 1;">
                            <!-- RedBeryl Logo SVG -->
                            <svg width="320" height="100" viewBox="0 0 800 250" style="margin-bottom: 10px;">
                                <rect width="800" height="250" fill="white"/>
                                <g transform="translate(20, 50)">
                                    <path d="M20 80 C20 40, 50 10, 90 10 C100 10, 110 12, 118 16 C125 8, 135 4, 146 4 C170 4, 190 24, 190 48 C190 52, 189 56, 188 60 C195 65, 200 73, 200 82 C200 95, 190 106, 177 106 L45 106 C30 106, 18 94, 18 79 C18 79, 19 79, 20 80 Z" fill="#1E88E5" stroke="#1565C0" stroke-width="2"/>
                                    <circle cx="80" cy="60" r="8" fill="#2196F3"/>
                                </g>
                                <g transform="translate(160, 30)">
                                    <path d="M30 60 C30 35, 50 15, 75 15 C85 15, 94 18, 101 23 C108 18, 117 15, 127 15 C152 15, 172 35, 172 60 C172 63, 171 66, 170 69 C177 73, 182 80, 182 88 C182 98, 174 106, 164 106 L46 106 C34 106, 25 97, 25 86 C25 75, 32 66, 41 63 C35 59, 30 54, 30 60 Z" fill="#E91E63" stroke="#C2185B" stroke-width="2"/>
                                    <circle cx="90" cy="50" r="6" fill="#F06292"/>
                                </g>
                                <g transform="translate(120, 80)">
                                    <circle cx="0" cy="0" r="4" fill="#9C27B0"/>
                                    <circle cx="20" cy="10" r="3" fill="#673AB7"/>
                                    <circle cx="40" cy="5" r="2" fill="#3F51B5"/>
                                </g>
                                <g transform="translate(350, 80)">
                                    <text x="0" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#E91E63">Red</text>
                                    <text x="140" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#1565C0">Beryl</text>
                                    <text x="0" y="85" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#424242" letter-spacing="4px">TECH SOLUTIONS</text>
                                    <text x="0" y="110" font-family="Arial, sans-serif" font-size="16" fill="#666666" font-style="italic">Integrates Business With Technology</text>
                                </g>
                            </svg>
                        </div>
                        <div style="text-align: center; flex: 1;">
                            <h1 style="font-size: 32px; font-weight: bold; margin: 0; color: #333; text-decoration: underline; letter-spacing: 2px;">INVOICE</h1>
                        </div>
                        <div style="flex: 1;"></div>
                    </div>

                    <!-- Invoice Details and Billed To -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 30px;">
                        <div>
                            <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333;">Invoice Details :-</h3>
                            <div style="margin-bottom: 5px;"><strong>Invoice Date:</strong> ${new Date(invoice.issueDate).toLocaleDateString('en-GB')}</div>
                            <div style="margin-bottom: 5px;"><strong>Invoice No.:</strong> RB/24-25/${invoice.id.toString().padStart(3, '0')}</div>
                            <div style="margin-bottom: 5px;"><strong>Invoice Month:</strong> ${new Date(invoice.issueDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</div>
                            <div style="margin-bottom: 5px;"><strong>Invoice For:</strong> ${invoice.invoiceType}</div>
                            <div style="margin-bottom: 5px;"><strong>HSN No.:</strong> ${invoice.hsnCode}</div>
                            <div style="margin-bottom: 5px;"><strong>Employee Name:</strong> ${invoice.employeeName}</div>
                            <div style="margin-bottom: 5px;"><strong>Employee Engagement Code:</strong> ${invoice.employeeEngagementCode}</div>
                        </div>
                        <div>
                            <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333;">Billed To :-</h3>
                            <div style="margin-bottom: 5px;"><strong>${invoice.client}</strong></div>
                            <div style="margin-bottom: 5px;">${invoice.project}</div>
                            <div style="margin-bottom: 5px;"><strong>GST No:</strong></div>
                        </div>
                    </div>

                    <!-- Billing Table -->
                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; border: 1px solid #333;">
                        <thead>
                            <tr>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Employee Name</th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Joining Date</th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Rate</th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Bill Amount</th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">GST</th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Total Bill Amount</th>
                            </tr>
                            <tr>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 2px; border: 1px solid #333; font-size: 9px;">
                                    <div style="display: flex; justify-content: space-around;">
                                        <span>CGST @9%</span>
                                        <span>SGST @9%</span>
                                        <span>IGST @18%</span>
                                    </div>
                                </th>
                                <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${invoice.employeeName}</td>
                                <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${new Date(invoice.joiningDate).toLocaleDateString('en-GB')}</td>
                                <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${amounts.baseAmount}</td>
                                <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${amounts.baseAmount}</td>
                                <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">
                                    <div style="display: flex; justify-content: space-around; font-size: 10px;">
                                        <span>${amounts.cgstAmount}</span>
                                        <span>${amounts.sgstAmount}</span>
                                        <span>₹0.00</span>
                                    </div>
                                </td>
                                <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px; font-weight: bold;">${amounts.totalAmount}</td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- GST Type Information -->
                    <div style="font-size: 12px; margin-bottom: 10px; text-align: left; color: #1565C0; font-weight: bold;">
                        GST Type: Intra State (Maharashtra) - CGST (9%) + SGST (9%) = 18%
                    </div>

                    <!-- Net Payable -->
                    <div style="font-size: 14px; font-weight: bold; margin-bottom: 20px; text-align: left;">
                        <strong>Net Payable: ${amounts.totalAmount}</strong> /- (Fifty Nine Thousand Only)
                    </div>

                    <!-- Payment Information and Authorized Signatory -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                        <div>
                            <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333; background-color: #f8f9fa; padding: 5px; text-align: center; border: 1px solid #333;">Payment Information</h3>
                            <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
                                <tbody>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Bank Name:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.bankName}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Branch Name:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.branchName}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Account Name:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.accountName}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Account No:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.accountNo}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">IFSC Code:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.ifscCode}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Account Type:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.accountType}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">GSTIN:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.gstin}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">CIN:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.cin}</td></tr>
                                    <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">PAN No:</td><td style="padding: 3px; border: 1px solid #333;">${invoice.panNo}</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333; background-color: #f8f9fa; padding: 5px; text-align: center; border: 1px solid #333;">Authorized Signatory</h3>
                            <div style="height: 100px; border: 1px solid #333; display: flex; align-items: flex-end; justify-content: center; padding: 10px; font-size: 12px;">
                                <div style="text-align: center;">
                                    <div style="margin-bottom: 40px;"></div>
                                    <div>For RedBeryl Tech Solutions Pvt. Ltd.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div style="text-align: center; font-size: 12px; color: #333; border-top: 1px solid #333; padding-top: 10px; font-style: italic;">
                        Thank you for doing business with us.
                    </div>
                </div>
            `;
        }

        function previewInvoice() {
            showStatus('Generating invoice preview...', 'info');
            try {
                const html = generateInvoiceHTML(sampleInvoice);
                document.getElementById('invoice-preview').innerHTML = html;
                showStatus('Invoice preview generated successfully!', 'success');
            } catch (error) {
                showStatus('Error generating preview: ' + error.message, 'error');
            }
        }

        async function testPdfGeneration() {
            showStatus('Testing PDF generation...', 'info');
            try {
                // This would require the actual PDF libraries
                showStatus('PDF generation test would require jsPDF and html2canvas libraries. Preview shows the template that would be converted to PDF.', 'info');
                previewInvoice();
            } catch (error) {
                showStatus('Error in PDF generation: ' + error.message, 'error');
            }
        }

        async function testOneDriveUpload() {
            showStatus('Testing OneDrive upload...', 'info');
            try {
                // This would require the actual OneDrive integration
                showStatus('OneDrive upload test would require Microsoft Graph API integration. The template is ready for PDF conversion and upload.', 'info');
            } catch (error) {
                showStatus('Error in OneDrive upload: ' + error.message, 'error');
            }
        }

        // Auto-preview on load
        window.onload = function() {
            previewInvoice();
        };
    </script>
</body>
</html>
