import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, CloudUpload, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService, { OneDriveUploadResponse } from '@/services/oneDriveService';
import { fetchInvoicePdfBlob, generateInvoicePdfBlob } from '@/utils/pdfUtils';
import OneDriveAuthModal from '@/components/OneDriveAuthModal';
import { Invoice } from '@/types/invoice';

interface InvoiceOneDriveButtonProps {
  invoice: Invoice;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onUploadSuccess?: (response: OneDriveUploadResponse) => void;
  onUploadError?: (error: string) => void;
}

const InvoiceOneDriveButton: React.FC<InvoiceOneDriveButtonProps> = ({
  invoice,
  variant = 'ghost',
  size = 'sm',
  className = '',
  onUploadSuccess,
  onUploadError
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuthenticate = () => {
    setShowAuthModal(true);
  };

  const handleAuthSuccess = (accessToken: string) => {
    setIsAuthenticated(true);
    setShowAuthModal(false);
    toast.success('Successfully authenticated with OneDrive!');
    // After authentication, proceed with upload
    performUpload();
  };

  const handleAuthError = (error: string) => {
    setShowAuthModal(false);
    setUploadStatus('error');
    toast.error('Authentication failed', {
      description: error
    });
    if (onUploadError) {
      onUploadError(error);
    }
  };

  const generatePdfContent = async (): Promise<Blob> => {
    console.log('Starting PDF generation for invoice:', invoice.id);

    // Extract numeric ID from invoice ID (same logic as regular invoice generation)
    let numericId: number;
    if (typeof invoice.id === 'string') {
      // Handle formatted invoice numbers like "INV-25-001"
      const match = invoice.id.match(/(\d+)$/);
      if (match) {
        numericId = parseInt(match[1], 10);
      } else {
        numericId = parseInt(invoice.id, 10);
      }
    } else {
      numericId = invoice.id;
    }

    console.log(`DEBUG: Original ID: "${invoice.id}", Extracted numeric ID: ${numericId}`);

    // Use the same API endpoints as the regular invoice generation
    const apiUrls = [
      `/api/invoice-generation/public/pdf/by-number/${invoice.id}`,
      `/api/invoice-generation/public/pdf/${numericId}`,
      `/api/invoice-generation/pdf/${numericId}`
    ];

    let lastError: Error | null = null;
    const errors: string[] = [];

    for (const url of apiUrls) {
      try {
        console.log(`Attempting to fetch PDF from: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          // Add timeout
          signal: AbortSignal.timeout(30000) // 30 seconds timeout
        });

        console.log(`Response status: ${response.status} for ${url}`);

        if (response.ok) {
          const blob = await response.blob();
          console.log(`Blob size: ${blob.size} bytes, type: ${blob.type}`);

          if (blob.size > 0 && blob.type === 'application/pdf') {
            console.log(`Successfully fetched valid PDF from: ${url}`);
            return blob;
          } else {
            errors.push(`${url}: Invalid PDF (size: ${blob.size}, type: ${blob.type})`);
          }
        } else {
          const errorText = await response.text();
          errors.push(`${url}: HTTP ${response.status} - ${errorText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to fetch from ${url}:`, errorMessage);
        errors.push(`${url}: ${errorMessage}`);
        lastError = error instanceof Error ? error : new Error(errorMessage);
      }
    }

    // Create detailed error message - DO NOT use client-side generation as fallback
    // We want to use the same template as the backend (invoice-template-clean.html)
    const errorDetails = errors.join('; ');
    console.error('All backend PDF generation attempts failed:', errorDetails);

    throw new Error(`Failed to generate PDF using backend template. Please ensure the backend is running and the invoice data is complete. Errors: ${errorDetails}`);
  };

  const performUpload = async () => {
    try {
      setIsUploading(true);
      setUploadStatus('idle');

      console.log('=== OneDrive Upload Process Started ===');
      console.log('Invoice ID:', invoice.id);
      console.log('Invoice Number:', invoice.invoiceNumber);

      // Generate PDF content using the same template as regular invoice generation
      console.log('Step 1: Generating PDF content...');
      const pdfBlob = await generatePdfContent();

      // Validate PDF content
      if (!pdfBlob || pdfBlob.size === 0) {
        throw new Error('Generated PDF is empty or invalid');
      }

      if (pdfBlob.type !== 'application/pdf') {
        console.warn('PDF blob type is not application/pdf:', pdfBlob.type);
      }

      console.log('✓ PDF generated successfully, size:', pdfBlob.size, 'bytes, type:', pdfBlob.type);

      // Upload to OneDrive
      console.log('Step 2: Uploading PDF to OneDrive...');
      const response = await oneDriveService.uploadPdf(pdfBlob, invoice.invoiceNumber || invoice.id.toString());
      console.log('OneDrive upload response:', response);

      if (response.success) {
        setUploadStatus('success');
        console.log('✓ OneDrive upload successful:', response);
        toast.success('Invoice PDF saved to OneDrive!', {
          description: `File: ${response.fileName || 'Invoice PDF'}`,
          action: response.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(response.webUrl, '_blank')
          } : undefined
        });

        if (onUploadSuccess) {
          onUploadSuccess(response);
        }
      } else {
        setUploadStatus('error');
        const errorMessage = response.error || response.message || 'Upload failed';
        console.error('✗ OneDrive upload failed:', errorMessage);

        // Provide more specific error messages
        let userFriendlyMessage = errorMessage;
        if (errorMessage.includes('authentication') || errorMessage.includes('token')) {
          userFriendlyMessage = 'Authentication expired. Please try again.';
        } else if (errorMessage.includes('PDF') || errorMessage.includes('content')) {
          userFriendlyMessage = 'Failed to generate PDF content. Please try again.';
        } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
          userFriendlyMessage = 'Network error. Please check your connection and try again.';
        }

        toast.error('Failed to save to OneDrive', {
          description: userFriendlyMessage
        });

        if (onUploadError) {
          onUploadError(errorMessage);
        }
      }
    } catch (error) {
      setUploadStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      console.error('✗ OneDrive upload error:', error);

      // Provide more specific error messages
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes('PDF') || errorMessage.includes('generate')) {
        userFriendlyMessage = 'Failed to generate invoice PDF. Please try again or contact support.';
      } else if (errorMessage.includes('authentication') || errorMessage.includes('token')) {
        userFriendlyMessage = 'Authentication required. Please authenticate with OneDrive first.';
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        userFriendlyMessage = 'Network error. Please check your connection and try again.';
      }

      toast.error('Failed to save to OneDrive', {
        description: userFriendlyMessage
      });

      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
      // Reset status after 3 seconds
      setTimeout(() => setUploadStatus('idle'), 3000);
    }
  };

  const handleUpload = async () => {
    try {
      console.log('=== OneDrive Upload Started ===');
      console.log('Invoice data:', {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        client: invoice.client?.name || 'Unknown',
        candidate: invoice.candidate?.name || 'Unknown'
      });

      // Check authentication first
      const authStatus = await oneDriveService.checkAuthentication();

      if (!authStatus.authenticated) {
        console.log('User not authenticated, showing authentication modal...');
        setShowAuthModal(true);
        return;
      }

      console.log('✓ Authentication verified');
      setIsAuthenticated(true);
      await performUpload();
    } catch (error) {
      console.error('Error checking authentication:', error);
      setUploadStatus('error');
      toast.error('Authentication check failed', {
        description: 'Please try again'
      });
    }
  };

  const getButtonIcon = () => {
    if (isUploading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (uploadStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (uploadStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    return <CloudUpload className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isUploading) {
      return 'Saving...';
    }
    
    if (uploadStatus === 'success') {
      return 'Saved';
    }
    
    if (uploadStatus === 'error') {
      return 'Failed';
    }
    
    return 'OneDrive';
  };

  const getButtonVariant = () => {
    if (uploadStatus === 'success') {
      return 'default';
    }
    
    if (uploadStatus === 'error') {
      return 'destructive';
    }
    
    return variant;
  };

  return (
    <>
      <Button
        onClick={handleUpload}
        disabled={isUploading}
        variant={getButtonVariant()}
        size={size}
        className={`${className} transition-all duration-200`}
        title={`Save Invoice ${invoice.id} to OneDrive`}
      >
        {getButtonIcon()}
        {size !== 'icon' && <span className="ml-1">{getButtonText()}</span>}
      </Button>

      <OneDriveAuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
        onError={handleAuthError}
      />
    </>
  );
};

export default InvoiceOneDriveButton;
