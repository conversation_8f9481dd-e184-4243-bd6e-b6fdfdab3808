import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, CloudUpload, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService, { OneDriveUploadResponse } from '@/services/oneDriveService';
import { fetchInvoicePdfBlob, generateInvoicePdfBlob } from '@/utils/pdfUtils';
import OneDriveAuthModal from '@/components/OneDriveAuthModal';
import { Invoice } from '@/types/invoice';

interface InvoiceOneDriveButtonProps {
  invoice: Invoice;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onUploadSuccess?: (response: OneDriveUploadResponse) => void;
  onUploadError?: (error: string) => void;
}

const InvoiceOneDriveButton: React.FC<InvoiceOneDriveButtonProps> = ({
  invoice,
  variant = 'ghost',
  size = 'sm',
  className = '',
  onUploadSuccess,
  onUploadError
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const handleAuthenticate = () => {
    setShowAuthModal(true);
  };

  const handleAuthSuccess = (accessToken: string) => {
    setIsAuthenticated(true);
    setShowAuthModal(false);
    toast.success('Successfully authenticated with OneDrive!');
    // After authentication, proceed with upload
    performUpload();
  };

  const handleAuthError = (error: string) => {
    setShowAuthModal(false);
    setUploadStatus('error');
    toast.error('Authentication failed', {
      description: error
    });
    if (onUploadError) {
      onUploadError(error);
    }
  };

  const generatePdfContent = async (): Promise<Blob> => {
    console.log('Starting PDF generation for invoice:', invoice.id);

    // First, try backend PDF generation
    try {
      console.log('Attempting backend PDF generation...');

      // Extract numeric ID from invoice ID
      let numericId: number;
      if (typeof invoice.id === 'string') {
        const match = invoice.id.match(/(\d+)$/);
        if (match) {
          numericId = parseInt(match[1], 10);
        } else {
          numericId = parseInt(invoice.id, 10);
        }
      } else {
        numericId = invoice.id;
      }

      console.log(`DEBUG: Original ID: "${invoice.id}", Extracted numeric ID: ${numericId}`);

      // Try backend endpoints
      const apiUrls = [
        `/api/invoice-generation/public/pdf/by-number/${invoice.id}`,
        `/api/invoice-generation/public/pdf/${numericId}`,
        `/api/invoice-generation/pdf/${numericId}`
      ];

      for (const url of apiUrls) {
        try {
          console.log(`Attempting to fetch PDF from: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/pdf',
              'Cache-Control': 'no-cache'
            },
            signal: AbortSignal.timeout(10000) // 10 seconds timeout
          });

          if (response.ok) {
            const blob = await response.blob();
            if (blob.size > 0 && blob.type === 'application/pdf') {
              console.log(`✓ Successfully fetched PDF from backend: ${url}`);
              return blob;
            }
          }
        } catch (error) {
          console.warn(`Backend endpoint ${url} failed:`, error);
        }
      }

      console.log('Backend PDF generation failed, falling back to client-side generation');
    } catch (error) {
      console.warn('Backend PDF generation failed:', error);
    }

    // Fallback to client-side PDF generation
    try {
      console.log('Generating PDF client-side...');
      const pdfBlob = await generateInvoicePdfBlob(invoice);

      if (pdfBlob.size === 0) {
        throw new Error('Client-side generation returned empty PDF');
      }

      console.log('✓ Successfully generated PDF client-side, size:', pdfBlob.size, 'bytes');
      return pdfBlob;
    } catch (clientError) {
      console.error('Client-side PDF generation failed:', clientError);
      throw new Error(`Failed to generate PDF. Both backend and client-side generation failed. Error: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
    }
  };

  const performUpload = async () => {
    try {
      setIsUploading(true);
      setUploadStatus('idle');

      console.log('=== OneDrive Upload Process Started ===');
      console.log('Invoice ID:', invoice.id);
      console.log('Invoice Number:', invoice.invoiceNumber);

      // Generate PDF content using the same template as regular invoice generation
      console.log('Step 1: Generating PDF content...');
      const pdfBlob = await generatePdfContent();

      // Validate PDF content
      if (!pdfBlob || pdfBlob.size === 0) {
        throw new Error('Generated PDF is empty or invalid');
      }

      if (pdfBlob.type !== 'application/pdf') {
        console.warn('PDF blob type is not application/pdf:', pdfBlob.type);
      }

      console.log('✓ PDF generated successfully, size:', pdfBlob.size, 'bytes, type:', pdfBlob.type);

      // Upload to OneDrive
      console.log('Step 2: Uploading PDF to OneDrive...');
      const response = await oneDriveService.uploadPdf(pdfBlob, invoice.invoiceNumber || invoice.id.toString());
      console.log('OneDrive upload response:', response);

      if (response.success) {
        setUploadStatus('success');
        console.log('✓ OneDrive upload successful:', response);
        toast.success('Invoice PDF saved to OneDrive!', {
          description: `File: ${response.fileName || 'Invoice PDF'}`,
          action: response.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(response.webUrl, '_blank')
          } : undefined
        });

        if (onUploadSuccess) {
          onUploadSuccess(response);
        }
      } else {
        setUploadStatus('error');
        const errorMessage = response.error || response.message || 'Upload failed';
        console.error('✗ OneDrive upload failed:', errorMessage);

        // Provide more specific error messages
        let userFriendlyMessage = errorMessage;
        if (errorMessage.includes('authentication') || errorMessage.includes('token')) {
          userFriendlyMessage = 'Authentication expired. Please try again.';
        } else if (errorMessage.includes('PDF') || errorMessage.includes('content')) {
          userFriendlyMessage = 'Failed to generate PDF content. Please try again.';
        } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
          userFriendlyMessage = 'Network error. Please check your connection and try again.';
        }

        toast.error('Failed to save to OneDrive', {
          description: userFriendlyMessage
        });

        if (onUploadError) {
          onUploadError(errorMessage);
        }
      }
    } catch (error) {
      setUploadStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      console.error('✗ OneDrive upload error:', error);

      // Provide more specific error messages
      let userFriendlyMessage = errorMessage;
      if (errorMessage.includes('Invoice generation service is not available')) {
        userFriendlyMessage = 'Backend service is not running. Please start the backend server and try again.';
      } else if (errorMessage.includes('PDF') || errorMessage.includes('generate')) {
        userFriendlyMessage = 'Failed to generate invoice PDF. Please ensure the invoice data is complete and try again.';
      } else if (errorMessage.includes('authentication') || errorMessage.includes('token')) {
        userFriendlyMessage = 'Authentication required. Please authenticate with OneDrive first.';
      } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        userFriendlyMessage = 'Network error. Please check your connection and try again.';
      } else if (errorMessage.includes('backend') || errorMessage.includes('8091')) {
        userFriendlyMessage = 'Backend service unavailable. Please ensure the backend is running on port 8091.';
      }

      toast.error('Failed to save to OneDrive', {
        description: userFriendlyMessage
      });

      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
      // Reset status after 3 seconds
      setTimeout(() => setUploadStatus('idle'), 3000);
    }
  };

  const handleUpload = async () => {
    try {
      console.log('=== OneDrive Upload Started ===');
      console.log('Invoice data:', {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        client: invoice.client?.name || 'Unknown',
        candidate: invoice.candidate?.name || 'Unknown'
      });

      // Check authentication first
      const authStatus = await oneDriveService.checkAuthentication();

      if (!authStatus.authenticated) {
        console.log('User not authenticated, showing authentication modal...');
        setShowAuthModal(true);
        return;
      }

      console.log('✓ Authentication verified');
      setIsAuthenticated(true);
      await performUpload();
    } catch (error) {
      console.error('Error checking authentication:', error);
      setUploadStatus('error');
      toast.error('Authentication check failed', {
        description: 'Please try again'
      });
    }
  };

  const getButtonIcon = () => {
    if (isUploading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (uploadStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (uploadStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    return <CloudUpload className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isUploading) {
      return 'Saving...';
    }
    
    if (uploadStatus === 'success') {
      return 'Saved';
    }
    
    if (uploadStatus === 'error') {
      return 'Failed';
    }
    
    return 'OneDrive';
  };

  const getButtonVariant = () => {
    if (uploadStatus === 'success') {
      return 'default';
    }
    
    if (uploadStatus === 'error') {
      return 'destructive';
    }
    
    return variant;
  };

  return (
    <>
      <Button
        onClick={handleUpload}
        disabled={isUploading}
        variant={getButtonVariant()}
        size={size}
        className={`${className} transition-all duration-200`}
        title={`Save Invoice ${invoice.id} to OneDrive`}
      >
        {getButtonIcon()}
        {size !== 'icon' && <span className="ml-1">{getButtonText()}</span>}
      </Button>

      <OneDriveAuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
        onError={handleAuthError}
      />
    </>
  );
};

export default InvoiceOneDriveButton;
