package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.StaffingTypeDto;

import java.util.List;

public interface StaffingTypeService {
    List<StaffingTypeDto> getAllStaffingTypes();
    StaffingTypeDto getStaffingTypeById(Long id);
    StaffingTypeDto getStaffingTypeByName(String name);
    StaffingTypeDto createStaffingType(StaffingTypeDto staffingTypeDto);
    StaffingTypeDto updateStaffingType(Long id, StaffingTypeDto staffingTypeDto);
    void deleteStaffingType(Long id);
}
