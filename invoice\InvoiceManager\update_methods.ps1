# PowerShell script to replace getUpdatedAt() with getModifiedAt() in all Java files
$files = Get-ChildItem -Path "src", "backend\src" -Recurse -Filter "*.java"

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $newContent = $content -replace "\.getUpdatedAt\(\)", ".getModifiedAt()"
    
    if ($content -ne $newContent) {
        Set-Content -Path $file.FullName -Value $newContent
        Write-Host "Updated $($file.FullName)"
    }
}

Write-Host "Done!"
