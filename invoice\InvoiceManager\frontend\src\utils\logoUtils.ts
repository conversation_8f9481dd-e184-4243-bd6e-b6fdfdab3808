// Utility functions for handling RedBeryl logo in different contexts

/**
 * Convert an image file to base64 data URL for embedding in PDFs
 * @param file - The image file to convert
 * @returns Promise<string> - Base64 data URL
 */
export const convertImageToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert image to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Get the RedBeryl logo URL based on environment and availability
 * @returns string - Logo URL or fallback
 */
export const getRedBerylLogoUrl = (): string => {
  // Try different possible locations for the logo
  const possiblePaths = [
    '/assets/redberyl-logo.png',
    '/assets/redberyl-logo.jpg',
    '/assets/redberyl-logo.svg',
    '/images/redberyl-logo.png',
    '/public/assets/redberyl-logo.png'
  ];
  
  // In a real implementation, you might want to check if the file exists
  // For now, return the primary path
  return possiblePaths[0];
};

/**
 * Create a base64 data URL from an image URL for PDF embedding
 * @param imageUrl - URL of the image
 * @returns Promise<string> - Base64 data URL
 */
export const createBase64FromUrl = async (imageUrl: string): Promise<string> => {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to convert to base64'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read blob'));
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    throw new Error(`Failed to fetch image: ${error}`);
  }
};

/**
 * Default RedBeryl logo as SVG string for fallback
 */
export const getDefaultRedBerylLogoSVG = (): string => {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="600" height="240" viewBox="0 0 600 240" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="600" height="240" fill="white"/>
      <g transform="translate(30, 60)">
        <path d="M0 60 C0 27, 27 0, 60 0 C93 0, 120 27, 120 60 C120 93, 93 120, 60 120 C27 120, 0 93, 0 60 Z" fill="#4A90E2" opacity="0.9"/>
        <path d="M80 40 C80 18, 98 0, 120 0 C142 0, 160 18, 160 40 C160 62, 142 80, 120 80 C98 80, 80 62, 80 40 Z" fill="#E91E63" opacity="0.9"/>
        <path d="M40 80 C40 62, 54 48, 72 48 C90 48, 104 62, 104 80 C104 98, 90 112, 72 112 C54 112, 40 98, 40 80 Z" fill="#8E44AD" opacity="0.8"/>
        <circle cx="90" cy="60" r="6" fill="#3498DB"/>
        <circle cx="70" cy="70" r="4" fill="#E74C3C"/>
        <circle cx="110" cy="50" r="3" fill="#F39C12"/>
      </g>
      <g transform="translate(220, 60)">
        <text x="0" y="45" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#E91E63">Red</text>
        <text x="105" y="45" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#1565C0">Beryl</text>
        <text x="0" y="75" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="#666" letter-spacing="3px">TECH SOLUTIONS</text>
        <text x="0" y="100" font-family="Arial, sans-serif" font-size="14" fill="#888" font-style="italic">Integrates Business With Technology</text>
      </g>
    </svg>
  `)}`;
};

/**
 * Instructions for updating the logo
 */
export const LOGO_UPDATE_INSTRUCTIONS = `
To update the RedBeryl logo in invoice PDFs:

1. Save your logo image as: /public/assets/redberyl-logo.png
   - Recommended size: 600x240 pixels or similar aspect ratio
   - Supported formats: PNG, JPG, SVG

2. The logo will automatically be used in:
   - PDF invoice generation
   - Invoice preview
   - All RedBeryl branding components

3. For best results:
   - Use a transparent background (PNG)
   - Ensure good contrast for PDF printing
   - Test the logo in both light and dark themes

4. Fallback: If the image file is not found, the system will use the default SVG logo
`;
