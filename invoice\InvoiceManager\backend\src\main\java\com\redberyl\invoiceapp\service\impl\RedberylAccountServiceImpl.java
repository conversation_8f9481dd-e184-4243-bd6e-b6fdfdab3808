package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.RedberylAccountDto;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.service.RedberylAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RedberylAccountServiceImpl implements RedberylAccountService {

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Override
    public List<RedberylAccountDto> getAllRedberylAccounts() {
        List<RedberylAccount> accounts = redberylAccountRepository.findAll();
        if (accounts.isEmpty()) {
            throw new NoContentException("No Redberyl accounts found");
        }
        return accounts.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public RedberylAccountDto getRedberylAccountById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Redberyl Account ID cannot be null");
        }

        RedberylAccount redberylAccount = redberylAccountRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Redberyl Account not found with id: " + id));
        return convertToDto(redberylAccount);
    }

    @Override
    public RedberylAccountDto getRedberylAccountByAccountNo(String accountNo) {
        if (!StringUtils.hasText(accountNo)) {
            throw new NullConstraintViolationException("accountNo", "Account number cannot be empty");
        }

        RedberylAccount redberylAccount = redberylAccountRepository.findByAccountNo(accountNo)
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Redberyl Account not found with account number: " + accountNo));
        return convertToDto(redberylAccount);
    }

    @Override
    public RedberylAccountDto getRedberylAccountByGstn(String gstn) {
        if (!StringUtils.hasText(gstn)) {
            throw new NullConstraintViolationException("gstn", "GSTN cannot be empty");
        }

        RedberylAccount redberylAccount = redberylAccountRepository.findByGstn(gstn)
                .orElseThrow(() -> new ResourceNotFoundException("Redberyl Account not found with GSTN: " + gstn));
        return convertToDto(redberylAccount);
    }

    @Override
    public RedberylAccountDto getRedberylAccountByPanNo(String panNo) {
        if (!StringUtils.hasText(panNo)) {
            throw new NullConstraintViolationException("panNo", "PAN number cannot be empty");
        }

        RedberylAccount redberylAccount = redberylAccountRepository.findByPanNo(panNo)
                .orElseThrow(() -> new ResourceNotFoundException("Redberyl Account not found with PAN: " + panNo));
        return convertToDto(redberylAccount);
    }

    private void validateRedberylAccountDto(RedberylAccountDto redberylAccountDto) {
        if (redberylAccountDto == null) {
            throw new NullConstraintViolationException("redberylAccountDto", "Redberyl Account data cannot be null");
        }

        if (!StringUtils.hasText(redberylAccountDto.getAccountNo())) {
            throw new NullConstraintViolationException("accountNo", "Account number cannot be empty");
        }

        if (!StringUtils.hasText(redberylAccountDto.getAccountName())) {
            throw new NullConstraintViolationException("accountName", "Account name cannot be empty");
        }

        // Check for duplicate account number if it's a new account
        if (redberylAccountDto.getId() == null &&
                redberylAccountRepository.findByAccountNo(redberylAccountDto.getAccountNo()).isPresent()) {
            throw new UniqueConstraintViolationException("accountNo",
                    "Redberyl Account with account number " + redberylAccountDto.getAccountNo() + " already exists");
        }

        // Check for duplicate GSTN if provided
        if (StringUtils.hasText(redberylAccountDto.getGstn()) &&
                redberylAccountDto.getId() == null &&
                redberylAccountRepository.findByGstn(redberylAccountDto.getGstn()).isPresent()) {
            throw new UniqueConstraintViolationException("gstn",
                    "Redberyl Account with GSTN " + redberylAccountDto.getGstn() + " already exists");
        }

        // Check for duplicate PAN if provided
        if (StringUtils.hasText(redberylAccountDto.getPanNo()) &&
                redberylAccountDto.getId() == null &&
                redberylAccountRepository.findByPanNo(redberylAccountDto.getPanNo()).isPresent()) {
            throw new UniqueConstraintViolationException("panNo",
                    "Redberyl Account with PAN " + redberylAccountDto.getPanNo() + " already exists");
        }
    }

    @Override
    @Transactional
    public RedberylAccountDto createRedberylAccount(RedberylAccountDto redberylAccountDto) {
        validateRedberylAccountDto(redberylAccountDto);

        try {
            RedberylAccount redberylAccount = convertToEntity(redberylAccountDto);
            RedberylAccount savedRedberylAccount = redberylAccountRepository.save(redberylAccount);
            return convertToDto(savedRedberylAccount);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field",
                        "A Redberyl Account with these details already exists");
            } else {
                throw new CustomException("Error creating Redberyl Account: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating Redberyl Account", e);
        }
    }

    @Override
    @Transactional
    public RedberylAccountDto updateRedberylAccount(Long id, RedberylAccountDto redberylAccountDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Redberyl Account ID cannot be null");
        }

        if (redberylAccountDto == null) {
            throw new NullConstraintViolationException("redberylAccountDto", "Redberyl Account data cannot be null");
        }

        RedberylAccount existingRedberylAccount = redberylAccountRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Redberyl Account not found with id: " + id));

        // Check for duplicate account number if it's being changed
        if (StringUtils.hasText(redberylAccountDto.getAccountNo()) &&
                !redberylAccountDto.getAccountNo().equals(existingRedberylAccount.getAccountNo()) &&
                redberylAccountRepository.findByAccountNo(redberylAccountDto.getAccountNo()).isPresent()) {
            throw new UniqueConstraintViolationException("accountNo",
                    "Redberyl Account with account number " + redberylAccountDto.getAccountNo() + " already exists");
        }

        // Check for duplicate GSTN if it's being changed
        if (StringUtils.hasText(redberylAccountDto.getGstn()) &&
                (existingRedberylAccount.getGstn() == null ||
                        !redberylAccountDto.getGstn().equals(existingRedberylAccount.getGstn()))
                &&
                redberylAccountRepository.findByGstn(redberylAccountDto.getGstn()).isPresent()) {
            throw new UniqueConstraintViolationException("gstn",
                    "Redberyl Account with GSTN " + redberylAccountDto.getGstn() + " already exists");
        }

        // Check for duplicate PAN if it's being changed
        if (StringUtils.hasText(redberylAccountDto.getPanNo()) &&
                (existingRedberylAccount.getPanNo() == null ||
                        !redberylAccountDto.getPanNo().equals(existingRedberylAccount.getPanNo()))
                &&
                redberylAccountRepository.findByPanNo(redberylAccountDto.getPanNo()).isPresent()) {
            throw new UniqueConstraintViolationException("panNo",
                    "Redberyl Account with PAN " + redberylAccountDto.getPanNo() + " already exists");
        }

        try {
            updateRedberylAccountFromDto(existingRedberylAccount, redberylAccountDto);

            RedberylAccount updatedRedberylAccount = redberylAccountRepository.save(existingRedberylAccount);
            return convertToDto(updatedRedberylAccount);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field",
                        "A Redberyl Account with these details already exists");
            } else {
                throw new CustomException("Error updating Redberyl Account: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | UniqueConstraintViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating Redberyl Account", e);
        }
    }

    @Override
    @Transactional
    public void deleteRedberylAccount(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Redberyl Account ID cannot be null");
        }

        if (!redberylAccountRepository.existsById(id)) {
            throw new ResourceNotFoundException("Redberyl Account not found with id: " + id);
        }

        try {
            redberylAccountRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete Redberyl Account because it is referenced by other entities",
                        e);
            } else {
                throw new CustomException("Error deleting Redberyl Account: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting Redberyl Account", e);
        }
    }

    private RedberylAccountDto convertToDto(RedberylAccount redberylAccount) {
        RedberylAccountDto dto = new RedberylAccountDto();
        dto.setId(redberylAccount.getId());
        dto.setGlCode(redberylAccount.getGlCode());
        dto.setCostCenter(redberylAccount.getCostCenter());
        dto.setAccountingNotes(redberylAccount.getAccountingNotes());
        dto.setBankName(redberylAccount.getBankName());
        dto.setBranchName(redberylAccount.getBranchName());
        dto.setAccountName(redberylAccount.getAccountName());
        dto.setAccountNo(redberylAccount.getAccountNo());
        dto.setIfscCode(redberylAccount.getIfscCode());
        dto.setAccountType(redberylAccount.getAccountType());
        dto.setGstn(redberylAccount.getGstn());
        dto.setCin(redberylAccount.getCin());
        dto.setPanNo(redberylAccount.getPanNo());
        return dto;
    }

    private RedberylAccount convertToEntity(RedberylAccountDto dto) {
        RedberylAccount redberylAccount = new RedberylAccount();
        redberylAccount.setId(dto.getId());
        updateRedberylAccountFromDto(redberylAccount, dto);
        return redberylAccount;
    }

    private void updateRedberylAccountFromDto(RedberylAccount redberylAccount, RedberylAccountDto dto) {
        redberylAccount.setGlCode(dto.getGlCode());
        redberylAccount.setCostCenter(dto.getCostCenter());
        redberylAccount.setAccountingNotes(dto.getAccountingNotes());
        redberylAccount.setBankName(dto.getBankName());
        redberylAccount.setBranchName(dto.getBranchName());
        redberylAccount.setAccountName(dto.getAccountName());
        redberylAccount.setAccountNo(dto.getAccountNo());
        redberylAccount.setIfscCode(dto.getIfscCode());
        redberylAccount.setAccountType(dto.getAccountType());
        redberylAccount.setGstn(dto.getGstn());
        redberylAccount.setCin(dto.getCin());
        redberylAccount.setPanNo(dto.getPanNo());
    }
}
