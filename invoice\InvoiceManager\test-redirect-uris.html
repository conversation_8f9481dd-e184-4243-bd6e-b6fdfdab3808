<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Redirect URI Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            display: block;
            width: 100%;
            text-align: left;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .config-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 OneDrive Redirect URI Tester</h1>
    <p><strong>Purpose:</strong> Find which redirect URI works with your Azure app</p>

    <div class="test-section">
        <h2>🎯 Current Problem</h2>
        <div class="error result">
AADSTS50011: The redirect URI specified in the request does not match the redirect URIs configured for the application.

App ID: 86756722-ad2a-4ac0-8806-e2705653949a
Tenant ID: 14158288-a340-4380-88ed-a8989a932425
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 Test Different Redirect URIs</h2>
        <p>Click each button to test if that redirect URI works:</p>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:8091/api/onedrive/callback')">
            Test: http://localhost:8091/api/onedrive/callback (Current)
        </button>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost')">
            Test: http://localhost
        </button>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:8080')">
            Test: http://localhost:8080
        </button>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:3000')">
            Test: http://localhost:3000
        </button>
        
        <button class="test-button" onclick="testRedirectUri('https://localhost:8091/api/onedrive/callback')">
            Test: https://localhost:8091/api/onedrive/callback
        </button>
        
        <div id="test-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📋 How to Apply Working URI</h2>
        <div class="info result">
When you find a working redirect URI:

1. Stop your backend server (Ctrl+C)

2. Edit: backend/src/main/resources/application.properties

3. Change this line:
   onedrive.redirect.uri=WORKING_URI_HERE

4. Restart backend:
   cd backend
   mvn spring-boot:run

5. Test OneDrive button in your app
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 Manual Test</h2>
        <p>You can also manually test any redirect URI:</p>
        <input type="text" id="custom-uri" placeholder="Enter redirect URI to test" style="width: 100%; padding: 10px; margin: 10px 0;">
        <button class="test-button" onclick="testCustomUri()">Test Custom URI</button>
    </div>

    <div class="test-section">
        <h2>💡 What Each Result Means</h2>
        <div class="info result">
✅ SUCCESS: The redirect URI is configured in Azure
   → Use this URI in your application.properties

❌ AADSTS50011: The redirect URI is NOT configured in Azure
   → Try the next option or ask admin to add it

🔄 LOADING: Testing the redirect URI...
   → Wait for the result

⚠️  ERROR: Network or server issue
   → Check if backend is running on localhost:8091
        </div>
    </div>

    <script>
        async function testRedirectUri(redirectUri) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = `🔄 Testing: ${redirectUri}...`;

            try {
                // Create a test auth URL with this redirect URI
                const authUrl = `https://login.microsoftonline.com/14158288-a340-4380-88ed-a8989a932425/oauth2/v2.0/authorize?client_id=86756722-ad2a-4ac0-8806-e2705653949a&response_type=code&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent('https://graph.microsoft.com/Files.ReadWrite')}&state=test&response_mode=query`;

                // Open in a small popup to test
                const popup = window.open(authUrl, 'test-popup', 'width=500,height=600,scrollbars=yes');
                
                if (!popup) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Failed to open popup. Please allow popups and try again.`;
                    return;
                }

                // Monitor the popup for results
                let checkCount = 0;
                const maxChecks = 30; // 30 seconds timeout
                
                const checkPopup = setInterval(() => {
                    checkCount++;
                    
                    try {
                        if (popup.closed) {
                            clearInterval(checkPopup);
                            resultDiv.className = 'result warning';
                            resultDiv.textContent = `⚠️ Test cancelled. Popup was closed before completion.`;
                            return;
                        }

                        // Check if popup URL contains error or success
                        const popupUrl = popup.location.href;
                        
                        if (popupUrl.includes('error=')) {
                            clearInterval(checkPopup);
                            popup.close();
                            
                            if (popupUrl.includes('AADSTS50011')) {
                                resultDiv.className = 'result error';
                                resultDiv.textContent = `❌ FAILED: ${redirectUri}\n\nAADSTS50011: This redirect URI is NOT configured in Azure.\n\nTry the next option or ask your admin to add this URI to Azure.`;
                            } else {
                                const errorMatch = popupUrl.match(/error=([^&]+)/);
                                const error = errorMatch ? decodeURIComponent(errorMatch[1]) : 'Unknown error';
                                resultDiv.className = 'result error';
                                resultDiv.textContent = `❌ FAILED: ${redirectUri}\n\nError: ${error}`;
                            }
                        } else if (popupUrl.includes('code=') || popupUrl.includes(redirectUri)) {
                            clearInterval(checkPopup);
                            popup.close();
                            
                            resultDiv.className = 'result success';
                            resultDiv.innerHTML = `✅ SUCCESS: ${redirectUri}\n\nThis redirect URI works! 🎉\n\nTo use it:\n1. Stop backend (Ctrl+C)\n2. Edit application.properties:\n   onedrive.redirect.uri=${redirectUri}\n3. Restart backend: mvn spring-boot:run\n4. Test OneDrive in your app`;
                        }
                    } catch (e) {
                        // Cross-origin error, popup is still on Microsoft's domain
                        // This is normal, continue checking
                    }
                    
                    if (checkCount >= maxChecks) {
                        clearInterval(checkPopup);
                        popup.close();
                        resultDiv.className = 'result warning';
                        resultDiv.textContent = `⚠️ Test timeout. The popup took too long to respond.\n\nThis might mean:\n- The redirect URI is not configured\n- Network issues\n- User didn't complete authentication\n\nTry the next option.`;
                    }
                }, 1000);

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}`;
            }
        }

        function testCustomUri() {
            const customUri = document.getElementById('custom-uri').value.trim();
            if (!customUri) {
                alert('Please enter a redirect URI to test');
                return;
            }
            testRedirectUri(customUri);
        }

        // Auto-test the current configuration on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                const resultDiv = document.getElementById('test-result');
                resultDiv.className = 'result info';
                resultDiv.textContent = '🎯 Ready to test redirect URIs!\n\nClick any button above to test if that redirect URI is configured in your Azure app.';
            }, 1000);
        });
    </script>
</body>
</html>
