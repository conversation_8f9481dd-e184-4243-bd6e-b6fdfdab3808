package com.redberyl.invoiceapp.exception;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
public class ValidationErrorDetails extends ErrorDetails {
    private Map<String, String> errors;
    
    public ValidationErrorDetails(LocalDateTime timestamp, String message, String details, int status, Map<String, String> errors) {
        super(timestamp, message, details, status);
        this.errors = errors;
    }
}
