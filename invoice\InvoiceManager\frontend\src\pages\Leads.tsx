import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  MoreHorizontal,
  User,
  Building,
  Mail,
  PhoneCall,
  Calendar,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { format } from "date-fns";
import LeadStatusDropdown from "@/components/leads/LeadStatusDropdown";
import LeadFormDialog from "@/components/leads/LeadFormDialog";
import { toast } from "sonner";
import { Lead, leadService } from "@/services/leadService";

const Leads = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [leads, setLeads] = useState<Lead[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<Lead[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddLeadDialogOpen, setIsAddLeadDialogOpen] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [currentLead, setCurrentLead] = useState<Lead | null>(null);

  // Function to fetch leads from the backend
  const fetchLeads = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('Fetching leads data...');
      const loadingToast = toast.loading('Loading leads...');

      try {
        // Direct API call to ensure we get the latest data from the database
        const response = await fetch('http://localhost:8091/api/leads', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
          // Handle 204 No Content as an empty array
          if (response.status === 204) {
            console.log('No leads found (204 No Content)');
            setLeads([]);
            setFilteredLeads([]);
            toast.info('No leads found. Add your first lead!', {
              id: loadingToast
            });
            return;
          }

          const errorText = await response.text();
          console.error('API error response:', errorText);
          throw new Error(`API error: ${response.status} ${response.statusText}`);
        }

        const responseText = await response.text();
        console.log('Raw API response:', responseText);

        if (!responseText.trim()) {
          console.log('Empty response from API, setting empty leads array');
          setLeads([]);
          setFilteredLeads([]);
          toast.info('No leads found. Add your first lead!', {
            id: loadingToast
          });
          return;
        }

        try {
          const data = JSON.parse(responseText);
          console.log('Leads data from API:', data);

          if (data && Array.isArray(data)) {
            // Format the leads data
            const formattedData = data.map(lead => ({
              id: lead.id,
              name: lead.name,
              company: lead.company || '',
              email: lead.email,
              phone: lead.phone || '',
              status: lead.status || 'New',
              source: lead.source || 'Website',
              dateAdded: lead.createdAt ? new Date(lead.createdAt).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]
            }));

            setLeads(formattedData);
            setFilteredLeads(formattedData);

            if (formattedData.length === 0) {
              console.log('No leads found in the response');
              toast.info('No leads found. Add your first lead!', {
                id: loadingToast
              });
            } else {
              console.log(`Successfully loaded ${formattedData.length} leads`);
              toast.success(`Successfully loaded ${formattedData.length} leads`, {
                id: loadingToast
              });
            }
          } else {
            console.error('Invalid data format received:', data);
            setError('Received invalid data format from the server.');
            toast.error('Failed to load leads: Invalid data format', {
              id: loadingToast
            });
          }
        } catch (parseError) {
          console.error('Error parsing JSON response:', parseError);
          setError('Failed to parse response from server.');
          toast.error('Failed to load leads: Invalid response format', {
            id: loadingToast
          });
        }
      } catch (err) {
        console.error('Error fetching leads:', err);
        setError('Failed to load leads. Please try again later.');
        toast.error('Failed to load leads', {
          description: err instanceof Error ? err.message : 'Unknown error occurred',
          id: loadingToast
        });
        throw err; // Re-throw to propagate the error
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch leads on component mount
  useEffect(() => {
    fetchLeads();
  }, []);

  // Function to retry loading leads
  const handleRetry = () => {
    console.log('Retrying lead data fetch...');
    toast.info('Retrying to load leads...');
    fetchLeads();
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    const filtered = leads.filter(
      (lead) =>
        lead.name.toLowerCase().includes(term) ||
        lead.company.toLowerCase().includes(term) ||
        lead.email.toLowerCase().includes(term) ||
        lead.status.toLowerCase().includes(term) ||
        lead.source.toLowerCase().includes(term)
    );

    setFilteredLeads(filtered);
  };

  const handleStatusChange = async (id: string | number, newStatus: string) => {
    try {
      // Show loading toast
      toast.loading('Updating lead status...');

      // Call the API to update the lead status
      await leadService.updateLeadStatus(id, newStatus);

      // Update the lead status in the state
      const updatedLeads = leads.map((lead) =>
        lead.id === id ? { ...lead, status: newStatus } : lead
      );

      setLeads(updatedLeads);
      setFilteredLeads(
        filteredLeads.map((lead) =>
          lead.id === id ? { ...lead, status: newStatus } : lead
        )
      );

      // Show a success toast
      toast.success(`Lead status updated to "${newStatus}"`, {
        description: `Lead ID: ${id}`,
      });
    } catch (error) {
      console.error('Error updating lead status:', error);
      toast.error('Failed to update lead status. Please try again.');
    }
  };

  const handleAddLead = () => {
    setCurrentLead(null);
    setIsAddLeadDialogOpen(true);
  };

  const handleSaveLead = (leadData: Lead) => {
    console.log('Handling saved lead data:', leadData);

    // The lead should be saved to the backend by the LeadFormDialog component
    // We need to refresh the leads list to ensure we have the latest data from the server

    // Show loading toast
    const loadingToast = toast.loading('Refreshing leads list...');

    // Immediately refresh the leads list to get the data from the database
    fetchLeads()
      .then(() => {
        toast.success('Leads list refreshed', {
          description: 'The leads list has been updated with the latest data from the database.',
          id: loadingToast
        });
      })
      .catch((error) => {
        console.error('Error refreshing leads list:', error);
        toast.error('Failed to refresh leads list', {
          description: 'Please try again or check the console for more details.',
          id: loadingToast
        });
      });
  };

  const handleViewLead = (id: string | number) => {
    const lead = leads.find(lead => lead.id === id);
    if (lead) {
      setCurrentLead(lead);
      setIsEditFormOpen(true);
    } else {
      toast.error(`Lead with ID ${id} not found`);
    }
  };

  const handleEditLead = (id: string | number) => {
    const lead = leads.find(lead => lead.id === id);
    if (lead) {
      setCurrentLead(lead);
      setIsEditFormOpen(true);
    } else {
      toast.error(`Lead with ID ${id} not found`);
    }
  };

  const handleDeleteLead = async (id: string | number) => {
    try {
      // Show loading toast
      toast.loading('Deleting lead...');

      // Call the API to delete the lead
      await leadService.deleteLead(id);

      // Remove the lead from our state
      const updatedLeads = leads.filter((lead) => lead.id !== id);
      setLeads(updatedLeads);
      setFilteredLeads(filteredLeads.filter((lead) => lead.id !== id));

      toast.success("Lead deleted successfully", {
        description: `Lead ID: ${id} has been removed.`,
      });
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast.error('Failed to delete lead. Please try again.');
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Leads</h2>
        <p className="text-muted-foreground">Track payment status and manage transactions.</p>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search leads..."
            className="pl-8 w-full md:w-[300px]"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <div className="flex gap-2">
          <Button onClick={handleAddLead}>
            <Plus className="mr-2 h-4 w-4" />
            Add Lead
          </Button>
          <Button variant="outline" onClick={handleRetry} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Client Leads</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Date Added</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-10">
                        <div className="flex flex-col items-center justify-center">
                          <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                          <span className="text-muted-foreground">Loading leads...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-10 text-red-500">
                        <div className="flex flex-col items-center justify-center">
                          <span>{error}</span>
                          <Button
                            variant="outline"
                            className="mt-4"
                            onClick={handleRetry}
                          >
                            Retry
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredLeads.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-6">
                        <div className="flex flex-col items-center justify-center">
                          <span className="text-muted-foreground mb-2">
                            {searchTerm ? "No leads match your search" : "No leads found. Add your first lead!"}
                          </span>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleAddLead}
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              Add Lead
                            </Button>
                            <Button
                              variant="secondary"
                              size="sm"
                              onClick={handleRetry}
                            >
                              Refresh
                            </Button>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLeads.map((lead) => (
                      <TableRow key={lead.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                              <User className="h-4 w-4 text-primary" />
                            </div>
                            {lead.name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Building className="h-4 w-4 mr-1 text-muted-foreground" />
                            {lead.company}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col space-y-1">
                            <div className="flex items-center">
                              <Mail className="h-3 w-3 mr-1 text-muted-foreground" />
                              <span className="text-xs">{lead.email}</span>
                            </div>
                            <div className="flex items-center">
                              <PhoneCall className="h-3 w-3 mr-1 text-muted-foreground" />
                              <span className="text-xs">{lead.phone || 'N/A'}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <LeadStatusDropdown
                            currentStatus={lead.status}
                            onStatusChange={(newStatus) => handleStatusChange(lead.id, newStatus)}
                          />
                        </TableCell>
                        <TableCell>{lead.source}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                            {lead.dateAdded ? format(new Date(lead.dateAdded), "MM/dd/yyyy") :
                             lead.createdAt ? format(new Date(lead.createdAt), "MM/dd/yyyy") : 'N/A'}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleViewLead(lead.id)}>
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEditLead(lead.id)}>
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDeleteLead(lead.id)}>
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Lead Form Dialog */}
      <LeadFormDialog
        open={isAddLeadDialogOpen}
        onOpenChange={setIsAddLeadDialogOpen}
        onSave={handleSaveLead}
      />

      {/* Edit Lead Form Dialog */}
      {currentLead && (
        <LeadFormDialog
          open={isEditFormOpen}
          onOpenChange={setIsEditFormOpen}
          onSave={handleSaveLead}
          initialData={currentLead}
          title="Edit Lead"
        />
      )}
    </div>
  );
};

export default Leads;
