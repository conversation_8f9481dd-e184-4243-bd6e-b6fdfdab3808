package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.RedberylAccountDto;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.service.RedberylAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Redberyl Account", description = "Redberyl Account management API")
public class RedberylAccountController {

    @Autowired
    private RedberylAccountService redberylAccountService;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @GetMapping("/redberyl-accounts/getAll")
    @Operation(summary = "Get all Redberyl accounts", description = "Get all Redberyl accounts")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Redberyl accounts found"),
            @ApiResponse(responseCode = "204", description = "No Redberyl accounts found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<RedberylAccountDto>> getAllRedberylAccounts() {
        try {
            List<RedberylAccountDto> redberylAccounts = redberylAccountService.getAllRedberylAccounts();
            return new ResponseEntity<>(redberylAccounts, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/redberyl-accounts/getById/{id}")
    @Operation(summary = "Get Redberyl account by ID", description = "Get Redberyl account by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Redberyl account found"),
            @ApiResponse(responseCode = "404", description = "Redberyl account not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountById(@PathVariable Long id) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountById(id);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @GetMapping("/redberyl-accounts/getByAccountNo/{accountNo}")
    @Operation(summary = "Get Redberyl account by account number", description = "Get Redberyl account by account number")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountByAccountNo(@PathVariable String accountNo) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountByAccountNo(accountNo);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @GetMapping("/redberyl-accounts/getByGstn/{gstn}")
    @Operation(summary = "Get Redberyl account by GSTN", description = "Get Redberyl account by GSTN")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountByGstn(@PathVariable String gstn) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountByGstn(gstn);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @GetMapping("/redberyl-accounts/getByPanNo/{panNo}")
    @Operation(summary = "Get Redberyl account by PAN", description = "Get Redberyl account by PAN")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> getRedberylAccountByPanNo(@PathVariable String panNo) {
        RedberylAccountDto redberylAccount = redberylAccountService.getRedberylAccountByPanNo(panNo);
        return new ResponseEntity<>(redberylAccount, HttpStatus.OK);
    }

    @PostMapping("/redberyl-accounts/create")
    @Operation(summary = "Create Redberyl account", description = "Create Redberyl account")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> createRedberylAccount(
            @Valid @RequestBody RedberylAccountDto redberylAccountDto) {
        RedberylAccountDto createdRedberylAccount = redberylAccountService.createRedberylAccount(redberylAccountDto);
        return new ResponseEntity<>(createdRedberylAccount, HttpStatus.CREATED);
    }

    @PutMapping("/redberyl-accounts/update/{id}")
    @Operation(summary = "Update Redberyl account", description = "Update Redberyl account")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<RedberylAccountDto> updateRedberylAccount(@PathVariable Long id,
            @Valid @RequestBody RedberylAccountDto redberylAccountDto) {
        RedberylAccountDto updatedRedberylAccount = redberylAccountService.updateRedberylAccount(id,
                redberylAccountDto);
        return new ResponseEntity<>(updatedRedberylAccount, HttpStatus.OK);
    }

    @DeleteMapping("/redberyl-accounts/deleteById/{id}")
    @Operation(summary = "Delete Redberyl account", description = "Delete Redberyl account")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteRedberylAccount(@PathVariable Long id) {
        redberylAccountService.deleteRedberylAccount(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/noauth/redberyl-accounts")
    @Operation(summary = "Get all Redberyl accounts (public)", description = "Get all Redberyl accounts without authentication")
    public ResponseEntity<List<RedberylAccountDto>> getAllRedberylAccountsPublic() {
        try {
            List<RedberylAccountDto> redberylAccounts = redberylAccountService.getAllRedberylAccounts();
            return new ResponseEntity<>(redberylAccounts, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/noauth/redberyl-accounts/init-default")
    @Operation(summary = "Initialize default RedBeryl account", description = "Create a default RedBeryl account if none exists")
    public ResponseEntity<String> initializeDefaultAccount() {
        try {
            // Check if any account exists
            long count = redberylAccountRepository.count();
            if (count > 0) {
                return ResponseEntity.ok("RedBeryl account already exists. Count: " + count);
            }

            // Create default account
            RedberylAccount defaultAccount = new RedberylAccount();
            defaultAccount.setAccountName("Acme Corporation Pvt Ltd");
            defaultAccount.setAccountNo("************");
            defaultAccount.setBankName("HDFC Bank");
            defaultAccount.setIfscCode("HDFC0001234");
            defaultAccount.setBranchName("MG Road Branch");
            defaultAccount.setAccountType("Current");
            defaultAccount.setGstn("29**********2Z5");
            defaultAccount.setCin("U12345KA2020PTC123456");
            defaultAccount.setPanNo("**********");

            RedberylAccount savedAccount = redberylAccountRepository.save(defaultAccount);
            return ResponseEntity.ok("Default RedBeryl account created successfully with ID: " + savedAccount.getId());

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating default account: " + e.getMessage());
        }
    }
}
