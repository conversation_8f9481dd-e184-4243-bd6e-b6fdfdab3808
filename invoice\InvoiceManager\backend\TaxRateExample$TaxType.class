����   = 1
      java/lang/Object <init> ()V	  	 
   TaxRateExample$TaxType id Ljava/lang/Long;	     taxType Ljava/lang/String;	     taxTypeDescription Code LineNumberTable getId ()Ljava/lang/Long; setId (Ljava/lang/Long;)V 
getTaxType ()Ljava/lang/String; 
setTaxType (Ljava/lang/String;)V getTaxTypeDescription setTaxTypeDescription 
SourceFile TaxRateExample.java RuntimeVisibleAnnotations 3Lcom/fasterxml/jackson/annotation/JsonIdentityInfo; 	generator GLcom/fasterxml/jackson/annotation/ObjectIdGenerators$PropertyGenerator; property NestHost ) TaxRateExample InnerClasses TaxType - Ecom/fasterxml/jackson/annotation/ObjectIdGenerators$PropertyGenerator / 3com/fasterxml/jackson/annotation/ObjectIdGenerators PropertyGenerator !                                  *� �           D             *� �           J             *+� �           K             *� 
�           M             *+� 
�           N             *� �           P             *+� �           Q       ! "     #  $c % &s  '    ( *      ( + 	 , . 0	