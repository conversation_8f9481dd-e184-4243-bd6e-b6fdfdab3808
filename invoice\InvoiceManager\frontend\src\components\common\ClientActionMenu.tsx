import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Eye, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import SimpleDeleteDialog from "@/components/ui/SimpleDeleteDialog";

interface ClientActionMenuProps {
  clientId: string;
  clientName: string;
  onEdit: (id: string) => void;
  onViewProjects: (name: string) => void;
  onDelete: (id: string) => void;
  refetchClients?: () => void;
}

const ClientActionMenu: React.FC<ClientActionMenuProps> = ({
  clientId,
  clientName,
  onEdit,
  onViewProjects,
  onDelete,
  refetchClients
}) => {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  // Handle edit client
  const handleEdit = () => {
    try {
      console.log(`Editing client with ID: ${clientId} (${typeof clientId})`);
      console.log(`Client name: ${clientName}`);

      // Ensure clientId is a string
      const idToUse = clientId?.toString();

      console.log(`Using client ID for edit: ${idToUse}`);

      // Call the edit handler with a small delay to ensure state updates properly
      setTimeout(() => {
        console.log(`Calling onEdit with ID: ${idToUse}`);
        onEdit(idToUse);
      }, 100);
    } catch (error) {
      console.error("Error in handleEdit:", error);
      toast.error(`Failed to edit client: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle view projects
  const handleViewProjects = () => {
    console.log(`Viewing projects for client: ${clientName}`);
    onViewProjects(clientName);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    try {
      setIsDeleting(true);

      // Ensure clientId is a number
      const numericId = parseInt(clientId?.toString() || '0');
      if (!numericId || numericId <= 0) {
        toast.error("Invalid client ID");
        return;
      }

      console.log(`Deleting client with ID: ${numericId}`);

      // Show loading toast
      const loadingToast = toast.loading(`Deleting client: ${clientName}...`);

      // Import and call the client service
      const { clientService } = await import('@/services/clientService');
      await clientService.deleteClient(numericId);

      // Success
      toast.success(`Client "${clientName}" deleted successfully`, { id: loadingToast });

      // Call the onDelete callback to update the UI
      onDelete(clientId);

      // Refetch clients if the function is provided
      if (refetchClients) {
        refetchClients();
      }
    } catch (error) {
      console.error('Error deleting client:', error);
      toast.error(`Failed to delete client: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            <span>Edit</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleViewProjects}>
            <Eye className="mr-2 h-4 w-4" />
            <span>View Projects</span>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsDeleteDialogOpen(true)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete confirmation dialog */}
      <SimpleDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        itemName={clientName}
        description={`Are you sure you want to delete client "${clientName}"? This will also delete all associated projects and invoices. This action cannot be undone.`}
        isDeleting={isDeleting}
      />
    </>
  );
};

export default ClientActionMenu;
