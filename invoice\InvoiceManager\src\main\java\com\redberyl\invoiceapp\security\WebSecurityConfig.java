package com.redberyl.invoiceapp.security;

import com.redberyl.invoiceapp.security.jwt.AuthEntryPointJwt;
import com.redberyl.invoiceapp.security.jwt.AuthTokenFilter;
import com.redberyl.invoiceapp.security.services.UserDetailsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
// import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
// @EnableMethodSecurity - Disabled for testing
public class WebSecurityConfig {
    @Autowired
    UserDetailsServiceImpl userDetailsService;

    @Autowired
    private AuthEntryPointJwt unauthorizedHandler;

    @Bean
    public AuthTokenFilter authenticationJwtTokenFilter() {
        return new AuthTokenFilter();
    }

    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();

        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());

        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
                .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth -> auth
                        // Authentication endpoints
                        .requestMatchers("/api/auth/**").permitAll()

                        // Documentation endpoints
                        .requestMatchers("/api-docs/**").permitAll()
                        .requestMatchers("/swagger-ui/**").permitAll()
                        .requestMatchers("/swagger-ui.html").permitAll()
                        .requestMatchers("/swagger-ui/index.html").permitAll()
                        .requestMatchers("/swagger").permitAll()
                        .requestMatchers("/docs").permitAll()
                        .requestMatchers("/api-docs-ui").permitAll()
                        .requestMatchers("/static/swagger-ui/**").permitAll()

                        // Public endpoints
                        .requestMatchers("/api/direct/**").permitAll()
                        .requestMatchers("/public/**").permitAll()
                        .requestMatchers("/noauth/**").permitAll()

                        // Tax-related endpoints
                        .requestMatchers("/api/tax-rates/**").permitAll()
                        .requestMatchers("/api/tax-types/**").permitAll()
                        .requestMatchers("/api/fixed-tax-rates/**").permitAll()
                        .requestMatchers("/api/examples/**").permitAll()

                        // HTML forms
                        .requestMatchers("/tax-rate-form").permitAll()
                        .requestMatchers("/tax-rate-form.html").permitAll()
                        .requestMatchers("/fixed-tax-rate-form.html").permitAll()
                        .requestMatchers("/tax-rate-with-audit").permitAll()
                        .requestMatchers("/tax-rate-with-audit.html").permitAll()
                        .requestMatchers("/tax-rate-example.json").permitAll()
                        .requestMatchers("/static/**").permitAll()

                        // Allow all API endpoints for testing
                        .requestMatchers("/**").permitAll()

                        // Require authentication for any other requests
                        .anyRequest().authenticated());

        http.authenticationProvider(authenticationProvider());

        http.addFilterBefore(authenticationJwtTokenFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
