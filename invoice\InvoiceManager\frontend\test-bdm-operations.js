// Simple test script to verify BDM operations work correctly
// Run this in the browser console on the BDM page

console.log('🧪 Testing BDM Operations...');

// Test 1: Check if BDM data is loaded
function testBdmDataLoaded() {
  console.log('📊 Test 1: Checking if BDM data is loaded...');
  
  const bdmRows = document.querySelectorAll('table tbody tr');
  if (bdmRows.length > 0) {
    console.log('✅ BDM data is loaded:', bdmRows.length, 'BDMs found');
    return true;
  } else {
    console.log('❌ No BDM data found');
    return false;
  }
}

// Test 2: Check if Add BDM button works
function testAddBdmButton() {
  console.log('➕ Test 2: Testing Add BDM button...');
  
  const addButton = document.querySelector('button:has-text("Add BDM"), button[aria-label*="Add"], button:contains("Add")');
  if (addButton) {
    console.log('✅ Add BDM button found');
    return true;
  } else {
    console.log('❌ Add BDM button not found');
    return false;
  }
}

// Test 3: Check if action menus are present
function testActionMenus() {
  console.log('⚙️ Test 3: Testing action menus...');
  
  const actionButtons = document.querySelectorAll('button[aria-haspopup="menu"]');
  if (actionButtons.length > 0) {
    console.log('✅ Action menus found:', actionButtons.length, 'menus');
    return true;
  } else {
    console.log('❌ No action menus found');
    return false;
  }
}

// Test 4: Check if search functionality works
function testSearchFunctionality() {
  console.log('🔍 Test 4: Testing search functionality...');
  
  const searchInput = document.querySelector('input[placeholder*="Search"], input[type="search"]');
  if (searchInput) {
    console.log('✅ Search input found');
    return true;
  } else {
    console.log('❌ Search input not found');
    return false;
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running all BDM tests...');
  
  const results = {
    dataLoaded: testBdmDataLoaded(),
    addButton: testAddBdmButton(),
    actionMenus: testActionMenus(),
    search: testSearchFunctionality()
  };
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`📈 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! BDM page is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the implementation.');
  }
  
  return results;
}

// Auto-run tests after a short delay
setTimeout(() => {
  runAllTests();
}, 2000);

console.log('📝 Test script loaded. Tests will run automatically in 2 seconds.');
console.log('💡 You can also run individual tests manually:');
console.log('   - testBdmDataLoaded()');
console.log('   - testAddBdmButton()');
console.log('   - testActionMenus()');
console.log('   - testSearchFunctionality()');
console.log('   - runAllTests()');
