package com.redberyl.invoiceapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Debug controller for direct database access
 * This is for debugging purposes only and should be removed in production
 */
@RestController
@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060" },
        allowedHeaders = { "Authorization", "Content-Type", "Accept" },
        methods = { RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS },
        allowCredentials = "false",
        maxAge = 3600)
public class DebugController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping("/debug/invoices-with-candidates")
    public ResponseEntity<List<Map<String, Object>>> getInvoicesWithCandidates() {
        String sql = "SELECT i.id, i.invoice_number, i.candidate_id, c.name as candidate_name " +
                "FROM invoices i " +
                "LEFT JOIN candidates c ON i.candidate_id = c.id " +
                "ORDER BY i.id";

        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/debug/invoices-simple")
    public ResponseEntity<List<Map<String, Object>>> getInvoicesSimple() {
        try {
            String sql = "SELECT " +
                    "i.id, " +
                    "i.invoice_number, " +
                    "i.billing_amount, " +
                    "i.tax_amount, " +
                    "i.total_amount, " +
                    "i.invoice_date, " +
                    "i.due_date, " +
                    "i.is_recurring, " +
                    "i.published_to_finance, " +
                    "i.published_at, " +
                    "i.created_at " +
                    "FROM invoices i " +
                    "ORDER BY i.id";

            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            System.err.println("Error in getInvoicesSimple: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500).body(null);
        }
    }

    @GetMapping("/debug/candidates")
    public ResponseEntity<List<Map<String, Object>>> getAllCandidates() {
        String sql = "SELECT id, name FROM candidates ORDER BY id";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/debug/fix-candidates")
    public ResponseEntity<String> fixCandidates() {
        // This endpoint will update the first invoice to have a candidate if none exists
        String checkSql = "SELECT COUNT(*) FROM invoices WHERE candidate_id IS NOT NULL";
        Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class);

        if (count != null && count > 0) {
            return ResponseEntity.ok("Candidates already exist in invoices. No fix needed.");
        }

        // Get the first candidate
        String candidateSql = "SELECT id FROM candidates ORDER BY id LIMIT 1";
        Integer candidateId = jdbcTemplate.queryForObject(candidateSql, Integer.class);

        if (candidateId == null) {
            return ResponseEntity.ok("No candidates found in the database.");
        }

        // Update the first invoice
        String updateSql = "UPDATE invoices SET candidate_id = ? WHERE id = (SELECT id FROM invoices ORDER BY id LIMIT 1)";
        int updated = jdbcTemplate.update(updateSql, candidateId);

        return ResponseEntity.ok("Updated " + updated + " invoice(s) with candidate ID " + candidateId);
    }
}
