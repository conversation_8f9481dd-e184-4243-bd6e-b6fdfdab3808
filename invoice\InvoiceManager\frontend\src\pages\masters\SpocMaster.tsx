import { useState, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Loader2, MoreHorizontal, Plus, Search } from "lucide-react";
import { Spoc, spocService } from "@/services/spocService";

// Sample SPOCs for development/fallback
const sampleSpocs: Spoc[] = [
  {
    id: 1,
    name: "<PERSON>",
    emailId: "<EMAIL>",
    contactNo: "+91 9876543210"
  },
  {
    id: 2,
    name: "<PERSON> <PERSON>",
    emailId: "<EMAIL>",
    contactNo: "+91 9876543211"
  },
  {
    id: 3,
    name: "Robert Johnson",
    emailId: "<EMAIL>",
    contactNo: "+91 9876543212"
  }
];

// Empty array for SPOCs - we'll fetch from the database
const emptySpocs: Spoc[] = [];

const SpocMaster = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedSpoc, setSelectedSpoc] = useState<Spoc | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    emailId: "",
    contactNo: "",
  });

  // State for SPOCs
  const [spocs, setSpocs] = useState<Spoc[]>([]);
  const [spocsLoading, setSpocsLoading] = useState(true);
  const [spocsError, setSpocsError] = useState<Error | null>(null);

  // Function to load SPOCs from database
  const loadSpocs = async () => {
    setSpocsLoading(true);

    try {
      console.log("Fetching SPOCs from database...");

      // Try to fetch SPOCs from the API
      try {
        const data = await spocService.getAllSpocs();
        console.log("Fetched SPOCs:", data);

        if (Array.isArray(data) && data.length > 0) {
          // Sort SPOCs by ID in descending order to show newest first
          const sortedData = [...data].sort((a, b) => {
            // If both have IDs, sort by ID in descending order
            if (a.id && b.id) {
              return b.id - a.id;
            }
            // If only one has an ID, prioritize the one with an ID
            if (a.id) return -1;
            if (b.id) return 1;
            // If neither has an ID, maintain original order
            return 0;
          });

          setSpocs(sortedData);
          console.log("Set sorted SPOCs:", sortedData);
        } else {
          console.log("No SPOCs found in database, using empty array");
          setSpocs(emptySpocs);
        }

        setSpocsError(null);
      } catch (apiError) {
        console.error("Error from SPOC API:", apiError);

        // Try direct fetch as fallback with multiple endpoints
        const fallbackEndpoints = [
          'http://localhost:8091/api/spocs',
          'http://localhost:8091/api/spocs/getAll'
        ];

        for (const endpoint of fallbackEndpoints) {
          try {
            console.log(`Trying direct fallback fetch: ${endpoint}`);
            const response = await fetch(endpoint, {
              headers: {
                'Authorization': 'Basic ' + btoa('admin:admin123')
              }
            });

            console.log(`Fallback response status for ${endpoint}:`, response.status);

            if (response.ok || response.status === 204) {
              const text = await response.text();
              console.log(`Fallback raw response from ${endpoint}:`, text);

              if (response.status === 204 || !text) {
                console.log("No SPOCs found via fallback (204 or empty response)");
                setSpocs(emptySpocs);
                setSpocsError(null);
                return;
              }

              const fallbackData = JSON.parse(text);
              if (Array.isArray(fallbackData)) {
                console.log(`✅ Successfully fetched SPOCs via fallback from ${endpoint}`);
                setSpocs(fallbackData);
                setSpocsError(null);
                return;
              }
            }
          } catch (fallbackError) {
            console.log(`❌ Fallback endpoint ${endpoint} failed:`, fallbackError);
          }
        }

        // If we get here, all fallbacks failed
        console.error("All fallback endpoints failed");
        throw apiError;
      }
    } catch (error) {
      console.error("Error loading SPOCs from database:", error);
      toast.error(`Failed to load SPOCs: ${error instanceof Error ? error.message : 'Database connection error'}`);
      setSpocsError(error instanceof Error ? error : new Error("Failed to load SPOCs from database"));

      // Use sample data as fallback when in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Using sample SPOCs as fallback in development mode');
        setSpocs(sampleSpocs);
        toast.info('Using sample data as fallback');
      } else {
        // Set empty array on error in production
        setSpocs(emptySpocs);
      }
    } finally {
      setSpocsLoading(false);
    }
  };

  // Function to refresh SPOCs
  const refetchSpocs = async () => {
    try {
      console.log("Manually refreshing SPOCs");
      await loadSpocs();
      toast.success("SPOCs refreshed successfully");
    } catch (error) {
      console.error("Error refreshing SPOCs:", error);
      toast.error(`Failed to refresh SPOCs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Filter SPOCs based on search term
  const filteredSpocs = spocs.filter((spoc) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      spoc.name.toLowerCase().includes(searchLower) ||
      spoc.emailId.toLowerCase().includes(searchLower) ||
      spoc.contactNo.toLowerCase().includes(searchLower)
    );
  });

  // Handle input change in form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Open form for creating a new SPOC
  const handleAddNew = () => {
    setFormData({
      name: "",
      emailId: "",
      contactNo: "",
    });
    setSelectedSpoc(null);
    setIsEditMode(false);
    setIsFormOpen(true);
  };

  // Open form for editing an existing SPOC
  const handleEdit = (spoc: Spoc) => {
    console.log("Editing SPOC:", spoc);

    // Make sure we have all fields from the SPOC
    const formValues = {
      name: spoc.name,
      emailId: spoc.emailId || "",
      contactNo: spoc.contactNo || "",
      email: spoc.emailId || "", // Add email for API compatibility
      contact: spoc.contactNo || "", // Add contact for API compatibility
      phone: spoc.contactNo || "", // Add phone for API compatibility
    };

    console.log("Setting form values:", formValues);
    setFormData(formValues);
    setSelectedSpoc(spoc);
    setIsEditMode(true);
    setIsFormOpen(true);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    if (!formData.name) {
      toast.error("SPOC name is required");
      return;
    }

    if (!formData.emailId) {
      toast.error("Email is required");
      return;
    }

    if (!formData.contactNo) {
      toast.error("Contact number is required");
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading(
      isEditMode ? "Updating SPOC..." : "Creating SPOC..."
    );

    try {
      if (isEditMode && selectedSpoc) {
        // Update existing SPOC in database
        console.log(`Updating SPOC with ID ${selectedSpoc.id}:`, formData);

        const updatedSpoc = await spocService.updateSpoc(selectedSpoc.id!, {
          ...selectedSpoc,
          name: formData.name,
          emailId: formData.emailId,
          contactNo: formData.contactNo
        });

        console.log("SPOC updated successfully in database:", updatedSpoc);

        // Update the SPOC in the local state to avoid a full reload
        setSpocs(prevSpocs =>
          prevSpocs.map(spoc =>
            spoc.id === selectedSpoc.id ? updatedSpoc : spoc
          )
        );
      } else {
        // Create new SPOC in database
        console.log("Creating new SPOC:", formData);

        const newSpoc = await spocService.createSpoc({
          name: formData.name,
          emailId: formData.emailId,
          contactNo: formData.contactNo
        });

        console.log("SPOC created successfully in database:", newSpoc);

        // Add the new SPOC to the local state to avoid a full reload
        if (newSpoc && newSpoc.id) {
          setSpocs(prevSpocs => [newSpoc, ...prevSpocs]);
        } else {
          // If the new SPOC doesn't have an ID, reload all data
          await loadSpocs();
        }
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success(
        isEditMode
          ? "SPOC updated successfully"
          : "SPOC created successfully"
      );

      // Close the form
      setIsFormOpen(false);
    } catch (error) {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error(
        `Failed to ${isEditMode ? "update" : "create"} SPOC: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // Handle delete SPOC
  const handleDelete = async (spoc: Spoc) => {
    if (!spoc.id) {
      toast.error("Cannot delete SPOC without ID");
      return;
    }

    // Confirm deletion
    if (!window.confirm(`Are you sure you want to delete SPOC "${spoc.name}"?`)) {
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading("Deleting SPOC...");

    try {
      await spocService.deleteSpoc(spoc.id);

      // Remove the SPOC from the local state
      setSpocs(prevSpocs => prevSpocs.filter(s => s.id !== spoc.id));

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success("SPOC deleted successfully");
    } catch (error) {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error(
        `Failed to delete SPOC: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // Load SPOCs on component mount
  useEffect(() => {
    console.log("Loading SPOCs on component mount");
    let isMounted = true;
    let retryCount = 0;
    const maxRetries = 2;
    const retryDelay = 1500; // 1.5 seconds

    // Function to load data with retry logic
    const loadWithRetry = async () => {
      try {
        if (isMounted) {
          console.log(`Loading SPOCs (attempt ${retryCount + 1}/${maxRetries + 1})`);
          await loadSpocs();
        }
      } catch (error) {
        console.error(`Error loading SPOCs (attempt ${retryCount + 1}):`, error);

        // Retry if we haven't exceeded max retries and component is still mounted
        if (retryCount < maxRetries && isMounted) {
          retryCount++;
          console.log(`Retrying in ${retryDelay}ms...`);

          // Wait before retrying
          setTimeout(() => {
            if (isMounted) {
              loadWithRetry();
            }
          }, retryDelay);
        }
      }
    };

    // Start loading
    loadWithRetry();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array to run only once on mount

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle>SPOC Master</CardTitle>
          <CardDescription>
            Manage Single Point of Contact (SPOC) information for your organization.
          </CardDescription>
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search SPOCs..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  await refetchSpocs();
                } catch (error) {
                  console.error("Error refreshing SPOCs:", error);
                }
              }}
              disabled={spocsLoading}
            >
              {spocsLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "Refresh"
              )}
            </Button>
            <Button size="sm" onClick={handleAddNew}>
              <Plus className="mr-2 h-4 w-4" />
              Add SPOC
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {spocsLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : spocsError ? (
            <div className="text-center py-8">
              <div className="text-red-500 font-medium mb-2">Error loading SPOCs. Please try again.</div>
              <div className="text-sm text-gray-600 mb-4">{spocsError.message}</div>
              <Button
                variant="outline"
                size="sm"
                onClick={refetchSpocs}
                className="mx-auto"
              >
                Retry
              </Button>
            </div>
          ) : filteredSpocs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No SPOCs found. Add your first SPOC!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Contact Number</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSpocs.map((spoc: Spoc) => (
                  <TableRow key={spoc.id}>
                    <TableCell>{spoc.name}</TableCell>
                    <TableCell>{spoc.emailId}</TableCell>
                    <TableCell>{spoc.contactNo}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(spoc)}>
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDelete(spoc)}>
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* SPOC Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Edit SPOC" : "Add New SPOC"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update the SPOC information below."
                : "Fill in the SPOC details below."}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-2">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="emailId" className="text-right">
                  Email
                </Label>
                <Input
                  id="emailId"
                  name="emailId"
                  type="email"
                  value={formData.emailId}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="contactNo" className="text-right">
                  Contact Number
                </Label>
                <Input
                  id="contactNo"
                  name="contactNo"
                  value={formData.contactNo}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsFormOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {isEditMode ? "Update SPOC" : "Add SPOC"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SpocMaster;
