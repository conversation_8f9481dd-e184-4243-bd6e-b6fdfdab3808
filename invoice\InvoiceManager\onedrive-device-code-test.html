<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Device Code Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .code-display {
            font-family: monospace;
            font-size: 24px;
            font-weight: bold;
            background: #f8f9fa;
            padding: 15px;
            border: 2px solid #007bff;
            border-radius: 4px;
            text-align: center;
            letter-spacing: 3px;
            margin: 15px 0;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OneDrive Device Code Authentication</h1>
        <p><strong>This method works without any redirect URI configuration!</strong></p>

        <div class="info">
            <h3>✅ Why Device Code Flow Works:</h3>
            <ul>
                <li><strong>No Redirect URI needed</strong> - Bypasses AADSTS50011 error completely</li>
                <li><strong>No Azure Portal changes required</strong> - Works with existing app registration</li>
                <li><strong>User-friendly</strong> - Simple copy/paste authentication flow</li>
                <li><strong>Secure</strong> - Microsoft-approved authentication method</li>
            </ul>
        </div>

        <div id="step1" class="step">
            <h3>Step 1: Start Authentication</h3>
            <p>Click the button below to start the device code authentication flow:</p>
            <button id="startAuth" class="button">Start OneDrive Authentication</button>
        </div>

        <div id="step2" class="step hidden">
            <h3>Step 2: Copy the Code</h3>
            <p>Copy this code to your clipboard:</p>
            <div id="userCode" class="code-display"></div>
            <button id="copyCode" class="button">Copy Code to Clipboard</button>
        </div>

        <div id="step3" class="step hidden">
            <h3>Step 3: Open Verification Page</h3>
            <p>Click the button below to open the Microsoft verification page:</p>
            <button id="openVerification" class="button">Open Verification Page</button>
            <p><small>URL: <span id="verificationUrl"></span></small></p>
        </div>

        <div id="step4" class="step hidden">
            <h3>Step 4: Enter Code and Sign In</h3>
            <p>On the verification page:</p>
            <ol>
                <li>Paste the code you copied</li>
                <li>Sign in with your Microsoft account</li>
                <li>Grant permissions to the application</li>
            </ol>
        </div>

        <div id="polling" class="step hidden">
            <h3>Step 5: Waiting for Authentication</h3>
            <p><span class="spinner"></span>Waiting for you to complete authentication...</p>
            <p><small>This page will automatically update when authentication is complete.</small></p>
        </div>

        <div id="result"></div>

        <div class="info">
            <h3>🔧 How to Integrate This Into Your App:</h3>
            <ol>
                <li><strong>Backend is ready</strong> - Device code endpoints are already implemented</li>
                <li><strong>Frontend component available</strong> - Use <code>OneDriveDeviceAuth.tsx</code></li>
                <li><strong>Update OneDrive service</strong> - Use <code>authenticateWithDeviceCode()</code> method</li>
                <li><strong>Replace popup authentication</strong> - No more redirect URI issues!</li>
            </ol>
        </div>
    </div>

    <script>
        let deviceCode = null;
        let pollInterval = null;

        document.getElementById('startAuth').addEventListener('click', startAuthentication);
        document.getElementById('copyCode').addEventListener('click', copyCode);
        document.getElementById('openVerification').addEventListener('click', openVerification);

        async function startAuthentication() {
            const button = document.getElementById('startAuth');
            button.disabled = true;
            button.textContent = 'Starting...';

            try {
                const response = await fetch('/api/onedrive/device-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    deviceCode = data.device_code;
                    document.getElementById('userCode').textContent = data.user_code;
                    document.getElementById('verificationUrl').textContent = data.verification_uri;
                    
                    // Show steps
                    document.getElementById('step2').classList.remove('hidden');
                    document.getElementById('step3').classList.remove('hidden');
                    document.getElementById('step4').classList.remove('hidden');
                    document.getElementById('polling').classList.remove('hidden');

                    // Start polling
                    startPolling(data.interval || 5);
                } else {
                    showError('Failed to start authentication: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                button.disabled = false;
                button.textContent = 'Start OneDrive Authentication';
            }
        }

        function copyCode() {
            const code = document.getElementById('userCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                const button = document.getElementById('copyCode');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#28a745';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#007bff';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy code:', err);
            });
        }

        function openVerification() {
            const url = document.getElementById('verificationUrl').textContent;
            window.open(url, '_blank');
        }

        function startPolling(interval) {
            pollInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/onedrive/device-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ device_code: deviceCode })
                    });

                    const data = await response.json();

                    if (data.success && data.access_token) {
                        clearInterval(pollInterval);
                        showSuccess('✅ Authentication successful! Access token received.');
                        document.getElementById('polling').classList.add('hidden');
                        
                        // Test the token
                        testAccessToken(data.access_token);
                    } else if (data.error === 'authorization_declined') {
                        clearInterval(pollInterval);
                        showError('❌ Authentication was declined by the user.');
                        document.getElementById('polling').classList.add('hidden');
                    } else if (data.error === 'expired_token') {
                        clearInterval(pollInterval);
                        showError('❌ The device code has expired. Please start over.');
                        document.getElementById('polling').classList.add('hidden');
                    } else if (data.error !== 'authorization_pending') {
                        clearInterval(pollInterval);
                        showError('❌ Authentication failed: ' + (data.error || 'Unknown error'));
                        document.getElementById('polling').classList.add('hidden');
                    }
                    // Continue polling if authorization_pending
                } catch (error) {
                    clearInterval(pollInterval);
                    showError('❌ Polling error: ' + error.message);
                    document.getElementById('polling').classList.add('hidden');
                }
            }, interval * 1000);

            // Timeout after 15 minutes
            setTimeout(() => {
                if (pollInterval) {
                    clearInterval(pollInterval);
                    showError('❌ Authentication timeout. Please try again.');
                    document.getElementById('polling').classList.add('hidden');
                }
            }, 15 * 60 * 1000);
        }

        async function testAccessToken(accessToken) {
            try {
                const response = await fetch('/api/onedrive/test', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + accessToken
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    showSuccess('🎉 OneDrive connection test successful! You can now upload files to OneDrive.');
                } else {
                    showError('⚠️ Authentication successful but OneDrive test failed: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                showError('⚠️ Authentication successful but failed to test OneDrive connection: ' + error.message);
            }
        }

        function showSuccess(message) {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="success">' + message + '</div>';
        }

        function showError(message) {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="error">' + message + '</div>';
        }
    </script>
</body>
</html>
