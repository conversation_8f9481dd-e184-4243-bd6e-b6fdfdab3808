package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class InvoiceDto extends BaseDto {
    private Long id;
    
    @NotBlank(message = "Invoice number is required")
    private String invoiceNumber;
    
    @NotNull(message = "Client ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long clientId;
    private ClientDto client;
    
    @NotNull(message = "Invoice type ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long invoiceTypeId;
    private InvoiceTypeDto invoiceType;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long projectId;
    private ProjectDto project;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long candidateId;
    private CandidateDto candidate;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long staffingTypeId;
    private StaffingTypeDto staffingType;
    
    @NotNull(message = "Billing amount is required")
    @Positive(message = "Billing amount must be positive")
    private BigDecimal billingAmount;
    
    @NotNull(message = "Tax amount is required")
    @Positive(message = "Tax amount must be positive")
    private BigDecimal taxAmount;
    
    @NotNull(message = "Total amount is required")
    @Positive(message = "Total amount must be positive")
    private BigDecimal totalAmount;
    
    @NotNull(message = "Invoice date is required")
    private LocalDate invoiceDate;
    
    private LocalDate dueDate;
    private Boolean isRecurring;
    private Boolean publishedToFinance;
    private LocalDateTime publishedAt;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long hsnId;
    private HsnCodeDto hsnCode;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long redberylAccountId;
    private RedberylAccountDto redberylAccount;
}
