package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceTaxDto;

import java.util.List;

public interface InvoiceTaxService {
    List<InvoiceTaxDto> getAllInvoiceTaxes();
    InvoiceTaxDto getInvoiceTaxById(Long id);
    List<InvoiceTaxDto> getInvoiceTaxesByInvoiceId(Long invoiceId);
    List<InvoiceTaxDto> getInvoiceTaxesByTaxRateId(Long taxRateId);
    InvoiceTaxDto createInvoiceTax(InvoiceTaxDto invoiceTaxDto);
    InvoiceTaxDto updateInvoiceTax(Long id, InvoiceTaxDto invoiceTaxDto);
    void deleteInvoiceTax(Long id);
}
