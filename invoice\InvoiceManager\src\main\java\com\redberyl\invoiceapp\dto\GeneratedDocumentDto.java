package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class GeneratedDocumentDto extends BaseDto {
    private Long id;
    
    @NotNull(message = "Template ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long templateId;
    
    // Include the full template object
    private DocumentTemplateDto template;
    
    @NotNull(message = "Version ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long versionId;
    
    // Include the full version object
    private DocumentTemplateVersionDto version;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long clientId;
    
    // Include the full client object
    private ClientDto client;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long dealId;
    
    // Include the full deal object
    private DealDto deal;
    
    private String fileName;
    private String filePath;
    private String filledContent;
    private String status;
}
