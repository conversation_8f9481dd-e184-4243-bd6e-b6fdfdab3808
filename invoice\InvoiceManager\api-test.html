<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
            max-height: 400px;
            overflow: auto;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .api-group {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        h3 {
            margin-top: 0;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    <p>This tool tests if the API calls are working through the proxy.</p>
    
    <div class="container">
        <button id="clearResults">Clear All Results</button>
        
        <div class="api-group">
            <h3>Authentication</h3>
            <button data-endpoint="/api/auth/login" data-method="POST" data-body='{"username":"admin","password":"admin123"}'>Test Login</button>
            <button data-endpoint="/api/auth/signup" data-method="POST" data-body='{"username":"testuser","email":"<EMAIL>","password":"password123","roles":["user"]}'>Test Signup</button>
            <div class="result" id="auth-result"></div>
        </div>
        
        <div class="api-group">
            <h3>Clients</h3>
            <button data-endpoint="/api/clients" data-method="GET">Get Clients</button>
            <div class="result" id="clients-result"></div>
        </div>
        
        <div class="api-group">
            <h3>Projects</h3>
            <button data-endpoint="/api/projects" data-method="GET">Get Projects</button>
            <button data-endpoint="/projects/getAll" data-method="GET">Get All Projects</button>
            <div class="result" id="projects-result"></div>
        </div>
        
        <div class="api-group">
            <h3>BDMs</h3>
            <button data-endpoint="/api/bdms" data-method="GET">Get BDMs</button>
            <div class="result" id="bdms-result"></div>
        </div>
        
        <div class="api-group">
            <h3>HSN Codes</h3>
            <button data-endpoint="/api/hsn-codes" data-method="GET">Get HSN Codes</button>
            <div class="result" id="hsn-result"></div>
        </div>
    </div>
    
    <script>
        // Function to make API calls
        async function callApi(endpoint, method, body = null) {
            const resultId = endpoint.includes('auth') ? 'auth-result' : 
                            endpoint.includes('clients') ? 'clients-result' : 
                            endpoint.includes('projects') ? 'projects-result' : 
                            endpoint.includes('bdms') ? 'bdms-result' : 'hsn-result';
            
            const resultDiv = document.getElementById(resultId);
            resultDiv.innerHTML = `<p>Calling ${method} ${endpoint}...</p>`;
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include' // Include credentials when using the proxy
                };
                
                if (body && (method === 'POST' || method === 'PUT')) {
                    options.body = typeof body === 'string' ? body : JSON.stringify(body);
                }
                
                const response = await fetch(endpoint, options);
                
                resultDiv.innerHTML += `<p>Response status: ${response.status} ${response.statusText}</p>`;
                
                // Log all response headers for debugging
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                resultDiv.innerHTML += `<p>Response headers: ${JSON.stringify(headers)}</p>`;
                
                // Try to parse the response body
                try {
                    const text = await response.text();
                    
                    if (!text || text.trim() === '') {
                        resultDiv.innerHTML += `<p>Empty response</p>`;
                        return;
                    }
                    
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML += `<p class="${response.ok ? 'success' : 'error'}">Response: ${JSON.stringify(data, null, 2)}</p>`;
                    } catch (jsonError) {
                        resultDiv.innerHTML += `<p>Response text: ${text}</p>`;
                    }
                } catch (textError) {
                    resultDiv.innerHTML += `<p class="error">Error getting response text: ${textError.message}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">Error: ${error.message}</p>`;
            }
        }
        
        // Add event listeners to all buttons
        document.querySelectorAll('button[data-endpoint]').forEach(button => {
            button.addEventListener('click', () => {
                const endpoint = button.getAttribute('data-endpoint');
                const method = button.getAttribute('data-method');
                const bodyAttr = button.getAttribute('data-body');
                const body = bodyAttr ? JSON.parse(bodyAttr) : null;
                
                callApi(endpoint, method, body);
            });
        });
        
        // Clear all results
        document.getElementById('clearResults').addEventListener('click', () => {
            document.querySelectorAll('.result').forEach(div => {
                div.innerHTML = '';
            });
        });
    </script>
</body>
</html>
