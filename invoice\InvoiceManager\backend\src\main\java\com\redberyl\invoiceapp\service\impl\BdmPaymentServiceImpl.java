package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.BdmPaymentDto;
import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.entity.Bdm;
import com.redberyl.invoiceapp.entity.BdmPayment;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.BdmPaymentRepository;
import com.redberyl.invoiceapp.repository.BdmRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.service.BdmPaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class BdmPaymentServiceImpl implements BdmPaymentService {

    @Autowired
    private BdmPaymentRepository bdmPaymentRepository;

    @Autowired
    private BdmRepository bdmRepository;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Override
    public List<BdmPaymentDto> getAllBdmPayments() {
        List<BdmPayment> bdmPayments = bdmPaymentRepository.findAll();
        if (bdmPayments.isEmpty()) {
            throw new NoContentException("No BDM payments found");
        }
        return bdmPayments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public BdmPaymentDto getBdmPaymentById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "BDM payment ID cannot be null");
        }

        BdmPayment bdmPayment = bdmPaymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BdmPayment", "id", id));
        return convertToDto(bdmPayment);
    }

    @Override
    public List<BdmPaymentDto> getBdmPaymentsByBdmId(Long bdmId) {
        if (bdmId == null) {
            throw new NullConstraintViolationException("bdmId", "BDM ID cannot be null");
        }

        // Check if BDM exists
        if (!bdmRepository.existsById(bdmId)) {
            throw new ResourceNotFoundException("Bdm", "id", bdmId);
        }

        List<BdmPayment> bdmPayments = bdmPaymentRepository.findByBdmId(bdmId);
        if (bdmPayments.isEmpty()) {
            throw new NoContentException("No BDM payments found for BDM with id: " + bdmId);
        }

        return bdmPayments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<BdmPaymentDto> getBdmPaymentsByInvoiceId(Long invoiceId) {
        if (invoiceId == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        // Check if invoice exists
        if (!invoiceRepository.existsById(invoiceId)) {
            throw new ResourceNotFoundException("Invoice", "id", invoiceId);
        }

        List<BdmPayment> bdmPayments = bdmPaymentRepository.findByInvoiceId(invoiceId);
        if (bdmPayments.isEmpty()) {
            throw new NoContentException("No BDM payments found for invoice with id: " + invoiceId);
        }

        return bdmPayments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateBdmPaymentDto(BdmPaymentDto bdmPaymentDto) {
        if (bdmPaymentDto == null) {
            throw new NullConstraintViolationException("bdmPaymentDto", "BDM payment data cannot be null");
        }

        if (bdmPaymentDto.getAmount() == null) {
            throw new NullConstraintViolationException("amount", "Amount cannot be null");
        }

        if (bdmPaymentDto.getBdmId() == null) {
            throw new NullConstraintViolationException("bdmId", "BDM ID cannot be null");
        }

        if (bdmPaymentDto.getInvoiceId() == null) {
            throw new NullConstraintViolationException("invoiceId", "Invoice ID cannot be null");
        }

        // Validate BDM ID
        if (!bdmRepository.existsById(bdmPaymentDto.getBdmId())) {
            throw new ForeignKeyViolationException("bdmId", "BDM not found with id: " + bdmPaymentDto.getBdmId());
        }

        // Validate Invoice ID
        if (!invoiceRepository.existsById(bdmPaymentDto.getInvoiceId())) {
            throw new ForeignKeyViolationException("invoiceId",
                    "Invoice not found with id: " + bdmPaymentDto.getInvoiceId());
        }
    }

    @Override
    @Transactional
    public BdmPaymentDto createBdmPayment(BdmPaymentDto bdmPaymentDto) {
        validateBdmPaymentDto(bdmPaymentDto);

        try {
            BdmPayment bdmPayment = convertToEntity(bdmPaymentDto);
            BdmPayment savedBdmPayment = bdmPaymentRepository.save(bdmPayment);
            return convertToDto(savedBdmPayment);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field", "BDM payment with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating BDM payment: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating BDM payment", e);
        }
    }

    @Override
    @Transactional
    public BdmPaymentDto updateBdmPayment(Long id, BdmPaymentDto bdmPaymentDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "BDM payment ID cannot be null");
        }

        BdmPayment existingBdmPayment = bdmPaymentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("BdmPayment", "id", id));

        try {
            // Update fields
            if (bdmPaymentDto.getBdmId() != null) {
                Bdm bdm = bdmRepository.findById(bdmPaymentDto.getBdmId())
                        .orElseThrow(() -> new ForeignKeyViolationException("bdmId",
                                "BDM not found with id: " + bdmPaymentDto.getBdmId()));
                existingBdmPayment.setBdm(bdm);
            }

            if (bdmPaymentDto.getInvoiceId() != null) {
                Invoice invoice = invoiceRepository.findById(bdmPaymentDto.getInvoiceId())
                        .orElseThrow(() -> new ForeignKeyViolationException("invoiceId",
                                "Invoice not found with id: " + bdmPaymentDto.getInvoiceId()));
                existingBdmPayment.setInvoice(invoice);
            }

            if (bdmPaymentDto.getAmount() != null) {
                existingBdmPayment.setAmount(bdmPaymentDto.getAmount());
            }

            if (bdmPaymentDto.getPaidOn() != null) {
                existingBdmPayment.setPaidOn(bdmPaymentDto.getPaidOn());
            }

            if (bdmPaymentDto.getIsPaid() != null) {
                existingBdmPayment.setIsPaid(bdmPaymentDto.getIsPaid());
            }

            BdmPayment updatedBdmPayment = bdmPaymentRepository.save(existingBdmPayment);
            return convertToDto(updatedBdmPayment);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field", "BDM payment with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating BDM payment: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating BDM payment", e);
        }
    }

    @Override
    @Transactional
    public void deleteBdmPayment(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "BDM payment ID cannot be null");
        }

        if (!bdmPaymentRepository.existsById(id)) {
            throw new ResourceNotFoundException("BdmPayment", "id", id);
        }

        try {
            bdmPaymentRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete BDM payment because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting BDM payment: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting BDM payment", e);
        }
    }

    private BdmPaymentDto convertToDto(BdmPayment bdmPayment) {
        BdmPaymentDto dto = BdmPaymentDto.builder()
                .id(bdmPayment.getId())
                .amount(bdmPayment.getAmount())
                .paidOn(bdmPayment.getPaidOn())
                .isPaid(bdmPayment.getIsPaid())
                .build();

        // Set the invoice ID and BDM ID
        if (bdmPayment.getInvoice() != null) {
            dto.setInvoiceId(bdmPayment.getInvoice().getId());

            // Set the invoice object
            InvoiceDto invoiceDto = InvoiceDto.builder()
                    .id(bdmPayment.getInvoice().getId())
                    .invoiceNumber(bdmPayment.getInvoice().getInvoiceNumber())
                    .billingAmount(bdmPayment.getInvoice().getBillingAmount())
                    .taxAmount(bdmPayment.getInvoice().getTaxAmount())
                    .totalAmount(bdmPayment.getInvoice().getTotalAmount())
                    .invoiceDate(bdmPayment.getInvoice().getInvoiceDate())
                    .dueDate(bdmPayment.getInvoice().getDueDate())
                    .build();
            // Commented out to avoid circular reference
            // dto.setInvoice(invoiceDto);
        }

        if (bdmPayment.getBdm() != null) {
            dto.setBdmId(bdmPayment.getBdm().getId());

            // Set the BDM object
            BdmDto bdmDto = BdmDto.builder()
                    .id(bdmPayment.getBdm().getId())
                    .name(bdmPayment.getBdm().getName())
                    .email(bdmPayment.getBdm().getEmail())
                    .phone(bdmPayment.getBdm().getPhone())
                    .gstNumber(bdmPayment.getBdm().getGstNumber())
                    .billingAddress(bdmPayment.getBdm().getBillingAddress())
                    .build();
            dto.setBdm(bdmDto);
        }

        // Set the audit fields from BaseEntity
        dto.setCreatedAt(bdmPayment.getCreatedAt());
        dto.setUpdatedAt(bdmPayment.getModifiedAt());

        return dto;
    }

    private BdmPayment convertToEntity(BdmPaymentDto dto) {
        BdmPayment bdmPayment = BdmPayment.builder()
                .amount(dto.getAmount())
                .paidOn(dto.getPaidOn())
                .isPaid(dto.getIsPaid())
                .build();

        // Set the ID if it's an update operation
        if (dto.getId() != null) {
            bdmPayment.setId(dto.getId());
        }

        // Set the invoice and BDM
        if (dto.getInvoiceId() != null) {
            Invoice invoice = invoiceRepository.findById(dto.getInvoiceId())
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice", "id", dto.getInvoiceId()));
            bdmPayment.setInvoice(invoice);
        }

        if (dto.getBdmId() != null) {
            Bdm bdm = bdmRepository.findById(dto.getBdmId())
                    .orElseThrow(() -> new ResourceNotFoundException("Bdm", "id", dto.getBdmId()));
            bdmPayment.setBdm(bdm);
        }

        return bdmPayment;
    }
}
