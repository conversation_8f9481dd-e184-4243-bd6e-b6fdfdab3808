import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { authApi } from "@/utils/api";

interface AuthContextType {
  isAuthenticated: boolean;
  username: string | null;
  login: (username: string, password: string, rememberMe: boolean) => Promise<void>;
  signup: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  user: any | null;
  loading: boolean;
}

interface LoginResponse {
  id: number;
  username: string;
  email: string;
  roles: string[];
  accessToken: string;
  tokenType: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  // Start with authenticated state to prevent redirects during page refresh
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(true);
  const [username, setUsername] = useState<string | null>("defaultuser");
  const [user, setUser] = useState<any | null>({ username: "defaultuser" });
  const [loading, setLoading] = useState<boolean>(false);

  console.log("AuthProvider state:", { isAuthenticated, username, loading });

  useEffect(() => {
    // Check if user is already authenticated
    const token = localStorage.getItem("token");
    const userData = localStorage.getItem("user");
    const storedUsername = localStorage.getItem("username");

    // If we have any authentication data, consider user authenticated
    if (token || userData || storedUsername) {
      try {
        let parsedUser = null;

        if (userData) {
          parsedUser = JSON.parse(userData);
        } else if (storedUsername) {
          // Create minimal user object from stored username
          parsedUser = { username: storedUsername };
        }

        if (parsedUser) {
          setIsAuthenticated(true);
          setUsername(parsedUser.username);
          setUser(parsedUser);

          // Store user data if not already stored
          if (!userData && parsedUser) {
            localStorage.setItem("user", JSON.stringify(parsedUser));
          }
        }
      } catch (error) {
        console.error("Error parsing user data:", error);
        // Don't clear everything on parse error, just the problematic data
        if (userData) {
          localStorage.removeItem("user");
        }
      }
    }

    setLoading(false);
  }, []);

  const login = async (username: string, password: string, rememberMe: boolean) => {
    setLoading(true);

    try {
      // Check if we already have user data in localStorage (from direct login)
      const storedUserData = localStorage.getItem("user");

      let userData;
      if (storedUserData) {
        // Use the stored user data if available
        userData = JSON.parse(storedUserData);
      } else {
        // If no stored data, create minimal user object
        userData = {
          username,
          email: `${username}@example.com`, // Default email
          roles: ["USER"] // Default role
        };
      }

      // Set authentication state
      setIsAuthenticated(true);
      setUsername(userData.username);
      setUser(userData);

      // Store authentication data
      localStorage.setItem("user", JSON.stringify(userData));
      localStorage.setItem("token", "dummy-token"); // Store a dummy token for auth check

      // Store username if rememberMe is true
      if (rememberMe) {
        localStorage.setItem("username", username);
      } else {
        localStorage.removeItem("username");
      }

    } catch (error) {
      console.error("Login error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (username: string, email: string, password: string) => {
    setLoading(true);

    try {
      // This function is now just a placeholder since we're handling signup directly in the NewSignup component
      // The actual API call is made in the NewSignup component
      console.log("Signup called from AuthContext");
      return { success: true, message: "User registered successfully!" };
    } catch (error) {
      console.error("Signup error:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    setUsername(null);
    setUser(null);
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("username");
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      username,
      login,
      signup,
      logout,
      user,
      loading
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
