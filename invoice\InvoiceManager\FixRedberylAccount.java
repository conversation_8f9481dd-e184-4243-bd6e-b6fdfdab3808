import java.sql.*;

public class FixRedberylAccount {
    public static void main(String[] args) {
        String url = "*******************************************";
        String username = "postgres";
        String password = "postgres";
        
        try (Connection conn = DriverManager.getConnection(url, username, password)) {
            System.out.println("Connected to PostgreSQL database!");
            
            // 1. Create RedBeryl Account
            String createAccountSQL = """
                INSERT INTO redberyl_accounts (account_name, account_no, bank_name, ifsc_code, branch_name, account_type, gstn, cin, pan_no, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()) 
                ON CONFLICT (account_no) DO NOTHING
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(createAccountSQL)) {
                stmt.setString(1, "RedBeryl Tech Solutions Pvt Ltd.");
                stmt.setString(2, "**************");
                stmt.setString(3, "HDFC Bank Ltd.");
                stmt.setString(4, "HDFC0000486");
                stmt.setString(5, "Destination Centre, Magarpatta, Pune");
                stmt.setString(6, "Current Account");
                stmt.setString(7, "27**********1Z5");
                stmt.setString(8, "U72900PN2022PTC213381");
                stmt.setString(9, "**********");
                
                int result = stmt.executeUpdate();
                System.out.println("RedBeryl Account created/exists: " + result + " rows affected");
            }
            
            // 2. Create HSN Code
            String createHsnSQL = """
                INSERT INTO hsn_codes (code, description, created_at, updated_at) 
                VALUES (?, ?, NOW(), NOW()) 
                ON CONFLICT (code) DO NOTHING
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(createHsnSQL)) {
                stmt.setString(1, "998313");
                stmt.setString(2, "IT consulting services");
                
                int result = stmt.executeUpdate();
                System.out.println("HSN Code created/exists: " + result + " rows affected");
            }
            
            // 3. Update INV-005
            String updateInvoiceSQL = """
                UPDATE invoices 
                SET redberyl_account_id = (SELECT id FROM redberyl_accounts WHERE account_no = '**************' LIMIT 1),
                    hsn_id = (SELECT id FROM hsn_codes WHERE code = '998313' LIMIT 1),
                    updated_at = NOW()
                WHERE invoice_number = 'INV-005'
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(updateInvoiceSQL)) {
                int result = stmt.executeUpdate();
                System.out.println("INV-005 updated: " + result + " rows affected");
            }
            
            // 4. Verify the update
            String verifySQL = """
                SELECT i.id, i.invoice_number, i.redberyl_account_id, i.hsn_id, ra.account_name, ra.account_no, hc.code as hsn_code
                FROM invoices i
                LEFT JOIN redberyl_accounts ra ON i.redberyl_account_id = ra.id
                LEFT JOIN hsn_codes hc ON i.hsn_id = hc.id
                WHERE i.invoice_number = 'INV-005'
                """;
            
            try (PreparedStatement stmt = conn.prepareStatement(verifySQL);
                 ResultSet rs = stmt.executeQuery()) {
                
                System.out.println("\n=== VERIFICATION ===");
                while (rs.next()) {
                    System.out.println("Invoice ID: " + rs.getLong("id"));
                    System.out.println("Invoice Number: " + rs.getString("invoice_number"));
                    System.out.println("RedBeryl Account ID: " + rs.getLong("redberyl_account_id"));
                    System.out.println("HSN ID: " + rs.getLong("hsn_id"));
                    System.out.println("Account Name: " + rs.getString("account_name"));
                    System.out.println("Account Number: " + rs.getString("account_no"));
                    System.out.println("HSN Code: " + rs.getString("hsn_code"));
                }
            }
            
            System.out.println("\n=== SUCCESS! ===");
            System.out.println("INV-005 is now properly linked to RedBeryl account and HSN code!");
            System.out.println("Generate the PDF again - it should show real data now!");
            
        } catch (SQLException e) {
            System.err.println("Database error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
