<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Tax Rate</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
            display: none;
        }
        .code-block {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Create Tax Rate</h1>
    
    <div class="form-group">
        <label for="taxTypeId">Tax Type ID:</label>
        <input type="number" id="taxTypeId" value="1">
    </div>
    
    <div class="form-group">
        <label for="rate">Rate (%):</label>
        <input type="number" id="rate" step="0.01" value="18.0">
    </div>
    
    <div class="form-group">
        <label for="effectiveFrom">Effective From:</label>
        <input type="date" id="effectiveFrom" value="2025-04-17">
    </div>
    
    <div class="form-group">
        <label for="effectiveTo">Effective To:</label>
        <input type="date" id="effectiveTo" value="2025-06-22">
    </div>
    
    <button onclick="generateJson()">Generate JSON</button>
    <button onclick="createTaxRate()">Create Tax Rate</button>
    <button onclick="createTaxRateWithParams()">Create with URL Parameters</button>
    
    <div class="result" id="jsonResult">
        <h2>JSON Payload:</h2>
        <pre id="jsonPayload"></pre>
    </div>
    
    <div class="result" id="apiResult">
        <h2>API Response:</h2>
        <pre id="apiResponse"></pre>
    </div>
    
    <div class="code-block">
        <h2>Curl Command:</h2>
        <pre id="curlCommand">curl -X POST "http://localhost:8081/api/tax-rates" \
-H "Content-Type: application/json" \
-d "{\"taxTypeId\": 1, \"rate\": 18.0, \"effectiveFrom\": \"2025-04-17\", \"effectiveTo\": \"2025-06-22\"}"</pre>
    </div>
    
    <script>
        function generateJson() {
            const taxTypeId = parseInt(document.getElementById('taxTypeId').value);
            const rate = parseFloat(document.getElementById('rate').value);
            const effectiveFrom = document.getElementById('effectiveFrom').value;
            const effectiveTo = document.getElementById('effectiveTo').value;
            
            const payload = {
                taxTypeId: taxTypeId,
                rate: rate,
                effectiveFrom: effectiveFrom,
                effectiveTo: effectiveTo
            };
            
            const jsonPayload = JSON.stringify(payload, null, 2);
            document.getElementById('jsonPayload').textContent = jsonPayload;
            document.getElementById('jsonResult').style.display = 'block';
            
            // Update curl command
            document.getElementById('curlCommand').textContent = `curl -X POST "http://localhost:8081/api/tax-rates" \\
-H "Content-Type: application/json" \\
-d '${JSON.stringify(payload)}'`;
        }
        
        async function createTaxRate() {
            const taxTypeId = parseInt(document.getElementById('taxTypeId').value);
            const rate = parseFloat(document.getElementById('rate').value);
            const effectiveFrom = document.getElementById('effectiveFrom').value;
            const effectiveTo = document.getElementById('effectiveTo').value;
            
            const payload = {
                taxTypeId: taxTypeId,
                rate: rate,
                effectiveFrom: effectiveFrom,
                effectiveTo: effectiveTo
            };
            
            try {
                const response = await fetch('/api/tax-rates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                document.getElementById('apiResult').style.display = 'block';
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
            }
        }
        
        async function createTaxRateWithParams() {
            const taxTypeId = parseInt(document.getElementById('taxTypeId').value);
            const rate = parseFloat(document.getElementById('rate').value);
            const effectiveFrom = document.getElementById('effectiveFrom').value;
            const effectiveTo = document.getElementById('effectiveTo').value;
            
            try {
                const url = `/api/fixed-tax-rates/create?taxTypeId=${taxTypeId}&rate=${rate}&effectiveFrom=${effectiveFrom}&effectiveTo=${effectiveTo}`;
                const response = await fetch(url, {
                    method: 'POST'
                });
                
                const data = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                document.getElementById('apiResult').style.display = 'block';
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
            }
        }
    </script>
</body>
</html>
