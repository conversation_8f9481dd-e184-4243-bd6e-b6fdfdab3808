package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.dashboard.DashboardMetricsDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.GeneratedDocument;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Payment;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.DocumentRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.PaymentRepository;
import com.redberyl.invoiceapp.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Service
public class DashboardServiceImpl implements DashboardService {

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Override
    public DashboardMetricsDto getDashboardMetrics() {
        // Get current date and first day of current month
        LocalDate now = LocalDate.now();
        LocalDate firstDayOfMonth = now.withDayOfMonth(1);
        LocalDate firstDayOfLastMonth = now.minusMonths(1).withDayOfMonth(1);
        LocalDate lastDayOfLastMonth = firstDayOfMonth.minusDays(1);

        // Get invoice metrics
        int totalInvoices = (int) invoiceRepository.count();
        int pendingApprovalInvoices = (int) invoiceRepository.findAll().stream()
                .filter(invoice -> !invoice.getPublishedToFinance())
                .count();

        // Calculate invoice trend
        List<Invoice> lastMonthInvoices = invoiceRepository.findByInvoiceDateBetween(firstDayOfLastMonth, lastDayOfLastMonth);
        int lastMonthInvoiceCount = lastMonthInvoices.size();
        int invoiceTrend = lastMonthInvoiceCount > 0
                ? (int) (((double) totalInvoices - lastMonthInvoiceCount) / lastMonthInvoiceCount * 100)
                : 0;

        // Get payment metrics
        List<Payment> allPayments = paymentRepository.findAll();
        double totalPaymentAmount = allPayments.stream()
                .mapToDouble(payment -> payment.getAmountReceived() != null ? payment.getAmountReceived().doubleValue() : 0.0)
                .sum();
        int pendingPayments = (int) allPayments.stream()
                .filter(payment -> "pending".equalsIgnoreCase(payment.getPaymentMode()))
                .count();

        // Calculate payment trend
        List<Payment> lastMonthPayments = paymentRepository.findByReceivedOnBetween(firstDayOfLastMonth, lastDayOfLastMonth);
        double lastMonthPaymentAmount = lastMonthPayments.stream()
                .mapToDouble(payment -> payment.getAmountReceived() != null ? payment.getAmountReceived().doubleValue() : 0.0)
                .sum();
        int paymentTrend = lastMonthPaymentAmount > 0
                ? (int) ((totalPaymentAmount - lastMonthPaymentAmount) / lastMonthPaymentAmount * 100)
                : 0;

        // Get client metrics
        int totalClients = (int) clientRepository.count();
        int newClientsThisMonth = (int) clientRepository.findAll().stream()
                .filter(client -> client.getCreatedAt() != null &&
                        client.getCreatedAt().isAfter(firstDayOfMonth.atStartOfDay().minusSeconds(1)))
                .count();

        // Calculate client trend
        int lastMonthClientCount = (int) clientRepository.findAll().stream()
                .filter(client -> client.getCreatedAt() != null &&
                        client.getCreatedAt().isBefore(firstDayOfMonth.atStartOfDay()) &&
                        client.getCreatedAt().isAfter(firstDayOfLastMonth.atStartOfDay().minusSeconds(1)))
                .count();
        int clientTrend = lastMonthClientCount > 0
                ? (int) (((double) totalClients - (totalClients - newClientsThisMonth)) / lastMonthClientCount * 100)
                : 0;

        // Get document metrics
        int totalDocuments = (int) documentRepository.count();
        int awaitingApprovalDocuments = (int) documentRepository.findAll().stream()
                .filter(doc -> "awaiting_approval".equalsIgnoreCase(doc.getStatus()))
                .count();

        // Calculate document trend
        int lastMonthDocumentCount = (int) documentRepository.findAll().stream()
                .filter(doc -> doc.getCreatedAt() != null &&
                        doc.getCreatedAt().isBefore(firstDayOfMonth.atStartOfDay()) &&
                        doc.getCreatedAt().isAfter(firstDayOfLastMonth.atStartOfDay().minusSeconds(1)))
                .count();
        int documentTrend = lastMonthDocumentCount > 0
                ? (int) (((double) totalDocuments - lastMonthDocumentCount) / lastMonthDocumentCount * 100)
                : 0;

        // Build and return the DTO with actual data
        return DashboardMetricsDto.builder()
                .totalInvoices(DashboardMetricsDto.InvoiceMetricsDto.builder()
                        .count(totalInvoices)
                        .trend(invoiceTrend)
                        .pendingApproval(pendingApprovalInvoices)
                        .build())
                .totalPayments(DashboardMetricsDto.PaymentMetricsDto.builder()
                        .amount(totalPaymentAmount)
                        .trend(paymentTrend)
                        .pendingPayments(pendingPayments)
                        .build())
                .activeClients(DashboardMetricsDto.ClientMetricsDto.builder()
                        .count(totalClients)
                        .trend(clientTrend)
                        .newThisMonth(newClientsThisMonth)
                        .build())
                .documents(DashboardMetricsDto.DocumentMetricsDto.builder()
                        .count(totalDocuments)
                        .trend(documentTrend)
                        .awaitingApproval(awaitingApprovalDocuments)
                        .build())
                .build();
    }
}
