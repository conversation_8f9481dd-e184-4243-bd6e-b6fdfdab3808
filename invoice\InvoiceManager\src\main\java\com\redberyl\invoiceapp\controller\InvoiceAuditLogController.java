package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceAuditLogDto;
import com.redberyl.invoiceapp.service.InvoiceAuditLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/invoice-audit-logs")
@Tag(name = "Invoice Audit Log", description = "Invoice Audit Log management API")
public class InvoiceAuditLogController {

    @Autowired
    private InvoiceAuditLogService invoiceAuditLogService;

    @GetMapping
    @Operation(summary = "Get all invoice audit logs", description = "Retrieve a list of all invoice audit logs")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceAuditLogDto>> getAllInvoiceAuditLogs() {
        List<InvoiceAuditLogDto> invoiceAuditLogs = invoiceAuditLogService.getAllInvoiceAuditLogs();
        return new ResponseEntity<>(invoiceAuditLogs, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get invoice audit log by ID", description = "Retrieve an invoice audit log by its ID")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceAuditLogDto> getInvoiceAuditLogById(@PathVariable Long id) {
        InvoiceAuditLogDto invoiceAuditLog = invoiceAuditLogService.getInvoiceAuditLogById(id);
        return new ResponseEntity<>(invoiceAuditLog, HttpStatus.OK);
    }

    @GetMapping("/invoice/{invoiceId}")
    @Operation(summary = "Get invoice audit logs by invoice ID", description = "Retrieve all audit logs for a specific invoice")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceAuditLogDto>> getInvoiceAuditLogsByInvoiceId(@PathVariable Long invoiceId) {
        List<InvoiceAuditLogDto> invoiceAuditLogs = invoiceAuditLogService.getInvoiceAuditLogsByInvoiceId(invoiceId);
        return new ResponseEntity<>(invoiceAuditLogs, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create invoice audit log", description = "Create a new invoice audit log")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceAuditLogDto> createInvoiceAuditLog(@Valid @RequestBody InvoiceAuditLogDto invoiceAuditLogDto) {
        InvoiceAuditLogDto createdInvoiceAuditLog = invoiceAuditLogService.createInvoiceAuditLog(invoiceAuditLogDto);
        return new ResponseEntity<>(createdInvoiceAuditLog, HttpStatus.CREATED);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete invoice audit log", description = "Delete an invoice audit log by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceAuditLog(@PathVariable Long id) {
        invoiceAuditLogService.deleteInvoiceAuditLog(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
