<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Integration Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>🔧 OneDrive Integration Diagnostic</h1>
    <p>This tool helps diagnose OneDrive integration issues by testing all endpoints and configurations.</p>

    <div class="diagnostic-section">
        <h2>🌐 Network Connectivity Test</h2>
        <button class="test-button" onclick="testNetworkConnectivity()">Test Network Connectivity</button>
        <div id="network-result" class="result"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🔗 Backend API Endpoints Test</h2>
        <button class="test-button" onclick="testBackendEndpoints()">Test Backend Endpoints</button>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="diagnostic-section">
        <h2>☁️ OneDrive API Test</h2>
        <button class="test-button" onclick="testOneDriveEndpoints()">Test OneDrive Endpoints</button>
        <div id="onedrive-result" class="result"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🔄 Proxy Configuration Test</h2>
        <button class="test-button" onclick="testProxyConfiguration()">Test Proxy Configuration</button>
        <div id="proxy-result" class="result"></div>
    </div>

    <div class="diagnostic-section">
        <h2>📊 Complete Diagnostic</h2>
        <button class="test-button" onclick="runCompleteDiagnostic()">Run Complete Diagnostic</button>
        <div id="complete-result" class="result"></div>
    </div>

    <script>
        async function testEndpoint(url, name, options = {}) {
            try {
                console.log(`Testing ${name}: ${url}`);
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    ...options
                });

                const isJson = response.headers.get('content-type')?.includes('application/json');
                const data = isJson ? await response.json() : await response.text();

                return {
                    name,
                    url,
                    status: response.status,
                    success: response.ok,
                    data: typeof data === 'string' ? data : JSON.stringify(data, null, 2),
                    headers: Object.fromEntries(response.headers.entries())
                };
            } catch (error) {
                return {
                    name,
                    url,
                    status: 'ERROR',
                    success: false,
                    data: error.message,
                    error: error
                };
            }
        }

        async function testNetworkConnectivity() {
            const resultDiv = document.getElementById('network-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Testing network connectivity...';

            const tests = [
                { url: 'http://localhost:8091', name: 'Backend Server (localhost)' },
                { url: 'http://127.0.0.1:8091', name: 'Backend Server (127.0.0.1)' },
                { url: 'http://localhost:3060', name: 'Frontend Server (localhost)' },
                { url: 'https://graph.microsoft.com', name: 'Microsoft Graph API' }
            ];

            let results = [];
            for (const test of tests) {
                const result = await testEndpoint(test.url, test.name);
                results.push(result);
            }

            const successCount = results.filter(r => r.success).length;
            const allSuccess = successCount === results.length;

            resultDiv.className = `result ${allSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = `Network Connectivity: ${successCount}/${results.length} endpoints reachable\n\n` +
                results.map(r => `${r.success ? '✅' : '❌'} ${r.name}: ${r.status}`).join('\n');
        }

        async function testBackendEndpoints() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Testing backend endpoints...';

            const endpoints = [
                { url: 'http://localhost:8091/api/dashboard/metrics', name: 'Dashboard Metrics' },
                { url: 'http://localhost:8091/api/invoice-generation/test', name: 'Invoice Generation' },
                { url: 'http://localhost:8091/api/onedrive/test', name: 'OneDrive Test' },
                { url: '/api/dashboard/metrics', name: 'Dashboard (Proxied)' },
                { url: '/api/invoice-generation/test', name: 'Invoice Generation (Proxied)' },
                { url: '/api/onedrive/test', name: 'OneDrive (Proxied)' }
            ];

            let results = [];
            for (const endpoint of endpoints) {
                const result = await testEndpoint(endpoint.url, endpoint.name);
                results.push(result);
            }

            const successCount = results.filter(r => r.success).length;
            const allSuccess = successCount === results.length;

            resultDiv.className = `result ${allSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = `Backend Endpoints: ${successCount}/${results.length} working\n\n` +
                results.map(r => `${r.success ? '✅' : '❌'} ${r.name}: ${r.status}`).join('\n') + '\n\n' +
                'Failed endpoints:\n' +
                results.filter(r => !r.success).map(r => `❌ ${r.name}: ${r.data}`).join('\n');
        }

        async function testOneDriveEndpoints() {
            const resultDiv = document.getElementById('onedrive-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Testing OneDrive endpoints...';

            const endpoints = [
                { url: '/api/onedrive/test', name: 'OneDrive Test (Proxied)' },
                { url: '/api/onedrive/auth-url', name: 'OneDrive Auth URL (Proxied)' },
                { url: 'http://localhost:8091/api/onedrive/test', name: 'OneDrive Test (Direct)' },
                { url: 'http://localhost:8091/api/onedrive/auth-url', name: 'OneDrive Auth URL (Direct)' }
            ];

            let results = [];
            for (const endpoint of endpoints) {
                const result = await testEndpoint(endpoint.url, endpoint.name);
                results.push(result);
            }

            const successCount = results.filter(r => r.success).length;
            const allSuccess = successCount === results.length;

            resultDiv.className = `result ${allSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = `OneDrive Endpoints: ${successCount}/${results.length} working\n\n` +
                results.map(r => `${r.success ? '✅' : '❌'} ${r.name}: ${r.status}`).join('\n') + '\n\n' +
                'Detailed results:\n' +
                results.map(r => `${r.name}: ${r.success ? 'SUCCESS' : 'FAILED'}\n${r.data}`).join('\n\n');
        }

        async function testProxyConfiguration() {
            const resultDiv = document.getElementById('proxy-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Testing proxy configuration...';

            // Test if requests to /api/* are being proxied correctly
            const proxyTests = [
                { url: '/api/onedrive/test', direct: 'http://localhost:8091/api/onedrive/test' },
                { url: '/api/dashboard/metrics', direct: 'http://localhost:8091/api/dashboard/metrics' }
            ];

            let results = [];
            for (const test of proxyTests) {
                const proxiedResult = await testEndpoint(test.url, `Proxied: ${test.url}`);
                const directResult = await testEndpoint(test.direct, `Direct: ${test.direct}`);
                
                results.push({
                    endpoint: test.url,
                    proxied: proxiedResult,
                    direct: directResult,
                    proxyWorking: proxiedResult.success === directResult.success
                });
            }

            const workingProxies = results.filter(r => r.proxyWorking).length;
            const allWorking = workingProxies === results.length;

            resultDiv.className = `result ${allWorking ? 'success' : 'error'}`;
            resultDiv.textContent = `Proxy Configuration: ${workingProxies}/${results.length} proxies working correctly\n\n` +
                results.map(r => 
                    `${r.proxyWorking ? '✅' : '❌'} ${r.endpoint}\n` +
                    `  Proxied: ${r.proxied.status}\n` +
                    `  Direct: ${r.direct.status}`
                ).join('\n\n');
        }

        async function runCompleteDiagnostic() {
            const resultDiv = document.getElementById('complete-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = 'Running complete diagnostic...';

            // Run all tests
            await testNetworkConnectivity();
            await testBackendEndpoints();
            await testOneDriveEndpoints();
            await testProxyConfiguration();

            resultDiv.className = 'result info';
            resultDiv.textContent = '✅ Complete diagnostic finished!\n\nCheck the results in each section above.\n\nIf OneDrive is still not working:\n1. Ensure backend is running on localhost:8091\n2. Ensure frontend is running on localhost:3060\n3. Clear browser cache and localStorage\n4. Check browser console for errors';
        }
    </script>
</body>
</html>
