<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #106ebe;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OneDrive Simple Test & Fix</h1>
        <p>This page tests OneDrive integration without requiring the backend server.</p>

        <!-- Step 1: Test Backend Connection -->
        <div class="test-section">
            <h2>Step 1: Test Backend Connection</h2>
            <button onclick="testBackend()">Test Backend</button>
            <div id="backend-result"></div>
        </div>

        <!-- Step 2: OneDrive Authentication -->
        <div class="test-section">
            <h2>Step 2: OneDrive Authentication</h2>
            <p>Enter your OneDrive access token (get it from the main app):</p>
            <input type="text" id="access-token" placeholder="Enter OneDrive access token">
            <button onclick="testAuth()">Test Authentication</button>
            <div id="auth-result"></div>
        </div>

        <!-- Step 3: Generate Test PDF -->
        <div class="test-section">
            <h2>Step 3: Generate Test PDF</h2>
            <button onclick="generateTestPdf()">Generate Test PDF</button>
            <div id="pdf-result"></div>
        </div>

        <!-- Step 4: Upload to OneDrive -->
        <div class="test-section">
            <h2>Step 4: Upload to OneDrive</h2>
            <button onclick="uploadToOneDrive()" id="upload-btn" disabled>Upload Test PDF to OneDrive</button>
            <div id="upload-result"></div>
        </div>

        <!-- Step 5: Instructions -->
        <div class="test-section">
            <h2>📋 Instructions to Fix OneDrive Issue</h2>
            <div id="instructions">
                <p><strong>If you see "No PDF content provided" error:</strong></p>
                <ol>
                    <li>Click "Test Backend" above - if it fails, the backend is not running</li>
                    <li>If backend is not running, the OneDrive button will use client-side PDF generation</li>
                    <li>Get your OneDrive access token from the main app (authenticate first)</li>
                    <li>Paste the token above and test authentication</li>
                    <li>Generate a test PDF and upload it to verify the fix works</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        let testPdfBlob = null;
        let accessToken = null;

        // Test backend connection
        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<div class="info">Testing backend connection...</div>';

            try {
                const response = await fetch('/api/invoice-generation/test');
                if (response.ok) {
                    const text = await response.text();
                    resultDiv.innerHTML = `<div class="success">✓ Backend is running: ${text}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Backend responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Backend is not running: ${error.message}<br><br>
                    <strong>This is OK!</strong> The OneDrive button will use client-side PDF generation instead.</div>`;
            }
        }

        // Test OneDrive authentication
        async function testAuth() {
            const token = document.getElementById('access-token').value;
            const resultDiv = document.getElementById('auth-result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">Please enter an access token</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Testing authentication...</div>';
            accessToken = token;

            try {
                // Try to make a simple request to OneDrive API
                const response = await fetch('https://graph.microsoft.com/v1.0/me/drive', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<div class="success">✓ Authentication successful!<br>Drive: ${data.name || 'OneDrive'}</div>`;
                    document.getElementById('upload-btn').disabled = false;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Authentication failed: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Authentication test failed: ${error.message}</div>`;
            }
        }

        // Generate test PDF
        async function generateTestPdf() {
            const resultDiv = document.getElementById('pdf-result');
            resultDiv.innerHTML = '<div class="info">Generating test PDF...</div>';

            try {
                // Create a simple test PDF content
                const testContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Invoice PDF) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`;

                testPdfBlob = new Blob([testContent], { type: 'application/pdf' });
                
                // Create download link for testing
                const url = URL.createObjectURL(testPdfBlob);
                resultDiv.innerHTML = `<div class="success">✓ Test PDF generated successfully!<br>
                    <a href="${url}" download="test-invoice.pdf">Download Test PDF</a></div>`;
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ PDF generation failed: ${error.message}</div>`;
            }
        }

        // Upload to OneDrive
        async function uploadToOneDrive() {
            const resultDiv = document.getElementById('upload-result');
            
            if (!testPdfBlob) {
                resultDiv.innerHTML = '<div class="error">Please generate a test PDF first</div>';
                return;
            }

            if (!accessToken) {
                resultDiv.innerHTML = '<div class="error">Please authenticate first</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Uploading to OneDrive...</div>';

            try {
                const fileName = `test-invoice-${Date.now()}.pdf`;
                const uploadUrl = `https://graph.microsoft.com/v1.0/me/drive/root:/Documents/RedBeryl/${fileName}:/content`;

                const response = await fetch(uploadUrl, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/pdf'
                    },
                    body: testPdfBlob
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `<div class="success">✓ Upload successful!<br>
                        File: ${data.name}<br>
                        <a href="${data.webUrl}" target="_blank">Open in OneDrive</a></div>`;
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="error">✗ Upload failed: ${response.status}<br>${errorText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Upload failed: ${error.message}</div>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Check for stored token
            const storedToken = localStorage.getItem('onedrive_access_token');
            if (storedToken) {
                document.getElementById('access-token').value = storedToken;
            }
        });
    </script>
</body>
</html>
