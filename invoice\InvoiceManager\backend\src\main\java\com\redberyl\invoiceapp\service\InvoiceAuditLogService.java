package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.InvoiceAuditLogDto;

import java.util.List;

public interface InvoiceAuditLogService {
    List<InvoiceAuditLogDto> getAllInvoiceAuditLogs();
    InvoiceAuditLogDto getInvoiceAuditLogById(Long id);
    List<InvoiceAuditLogDto> getInvoiceAuditLogsByInvoiceId(Long invoiceId);
    InvoiceAuditLogDto createInvoiceAuditLog(InvoiceAuditLogDto invoiceAuditLogDto);
    void deleteInvoiceAuditLog(Long id);
}
