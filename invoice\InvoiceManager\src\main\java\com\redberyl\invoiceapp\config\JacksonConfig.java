package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.redberyl.invoiceapp.entity.TaxRate;
import com.redberyl.invoiceapp.entity.TaxType;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.io.IOException;

@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // Configure serialization features
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        // Configure deserialization features
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // Register modules
        objectMapper.registerModule(new JavaTimeModule());

        // Register custom serializers
        SimpleModule module = new SimpleModule();
        module.addSerializer(TaxRate.class, new JsonSerializer<TaxRate>() {
            @Override
            public void serialize(TaxRate taxRate, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
                    throws IOException {
                jsonGenerator.writeStartObject();

                // Write the tax rate properties
                jsonGenerator.writeNumberField("id", taxRate.getId());

                if (taxRate.getRate() != null) {
                    jsonGenerator.writeNumberField("rate", taxRate.getRate());
                }

                if (taxRate.getEffectiveFrom() != null) {
                    jsonGenerator.writeStringField("effectiveFrom", taxRate.getEffectiveFrom().toString());
                }

                if (taxRate.getEffectiveTo() != null) {
                    jsonGenerator.writeStringField("effectiveTo", taxRate.getEffectiveTo().toString());
                }

                // Write the tax type as a nested object
                if (taxRate.getTaxType() != null) {
                    jsonGenerator.writeObjectFieldStart("taxType");
                    jsonGenerator.writeNumberField("id", taxRate.getTaxType().getId());
                    jsonGenerator.writeStringField("taxType", taxRate.getTaxType().getTaxType());
                    jsonGenerator.writeStringField("taxTypeDescription", taxRate.getTaxType().getTaxTypeDescription());
                    jsonGenerator.writeEndObject();
                }

                // Write the audit fields
                if (taxRate.getCreatedAt() != null) {
                    jsonGenerator.writeStringField("created_at", taxRate.getCreatedAt().toString());
                }

                jsonGenerator.writeEndObject();
            }
        });
        objectMapper.registerModule(module);

        return objectMapper;
    }
}
