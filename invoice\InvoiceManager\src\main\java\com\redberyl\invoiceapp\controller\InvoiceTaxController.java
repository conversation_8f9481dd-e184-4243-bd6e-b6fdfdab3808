package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceTaxDto;
import com.redberyl.invoiceapp.service.InvoiceTaxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/invoice-taxes")
@Tag(name = "Invoice Tax", description = "Invoice Tax management API")
public class InvoiceTaxController {

    @Autowired
    private InvoiceTaxService invoiceTaxService;

    @GetMapping
    @Operation(summary = "Get all invoice taxes", description = "Retrieve a list of all invoice taxes")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTaxDto>> getAllInvoiceTaxes() {
        List<InvoiceTaxDto> invoiceTaxes = invoiceTaxService.getAllInvoiceTaxes();
        return new ResponseEntity<>(invoiceTaxes, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get invoice tax by ID", description = "Retrieve an invoice tax by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTaxDto> getInvoiceTaxById(@PathVariable Long id) {
        InvoiceTaxDto invoiceTax = invoiceTaxService.getInvoiceTaxById(id);
        return new ResponseEntity<>(invoiceTax, HttpStatus.OK);
    }

    @GetMapping("/invoice/{invoiceId}")
    @Operation(summary = "Get invoice taxes by invoice ID", description = "Retrieve all taxes for a specific invoice")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTaxDto>> getInvoiceTaxesByInvoiceId(@PathVariable Long invoiceId) {
        List<InvoiceTaxDto> invoiceTaxes = invoiceTaxService.getInvoiceTaxesByInvoiceId(invoiceId);
        return new ResponseEntity<>(invoiceTaxes, HttpStatus.OK);
    }

    @GetMapping("/tax-rate/{taxRateId}")
    @Operation(summary = "Get invoice taxes by tax rate ID", description = "Retrieve all invoice taxes for a specific tax rate")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceTaxDto>> getInvoiceTaxesByTaxRateId(@PathVariable Long taxRateId) {
        List<InvoiceTaxDto> invoiceTaxes = invoiceTaxService.getInvoiceTaxesByTaxRateId(taxRateId);
        return new ResponseEntity<>(invoiceTaxes, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create invoice tax", description = "Create a new invoice tax")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTaxDto> createInvoiceTax(@Valid @RequestBody InvoiceTaxDto invoiceTaxDto) {
        InvoiceTaxDto createdInvoiceTax = invoiceTaxService.createInvoiceTax(invoiceTaxDto);
        return new ResponseEntity<>(createdInvoiceTax, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update invoice tax", description = "Update an existing invoice tax")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceTaxDto> updateInvoiceTax(@PathVariable Long id, @Valid @RequestBody InvoiceTaxDto invoiceTaxDto) {
        InvoiceTaxDto updatedInvoiceTax = invoiceTaxService.updateInvoiceTax(id, invoiceTaxDto);
        return new ResponseEntity<>(updatedInvoiceTax, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete invoice tax", description = "Delete an invoice tax by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoiceTax(@PathVariable Long id) {
        invoiceTaxService.deleteInvoiceTax(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
