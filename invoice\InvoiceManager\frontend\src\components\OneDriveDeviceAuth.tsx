import React, { useState } from 'react';
import { apiRequest } from '../services/api';

interface DeviceCodeResponse {
  success: boolean;
  device_code?: string;
  user_code?: string;
  verification_uri?: string;
  expires_in?: number;
  interval?: number;
  message?: string;
  error?: string;
}

interface TokenResponse {
  success: boolean;
  access_token?: string;
  error?: string;
}

interface OneDriveDeviceAuthProps {
  onSuccess: (accessToken: string) => void;
  onError: (error: string) => void;
}

const OneDriveDeviceAuth: React.FC<OneDriveDeviceAuthProps> = ({ onSuccess, onError }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [deviceCode, setDeviceCode] = useState<string | null>(null);
  const [userCode, setUserCode] = useState<string | null>(null);
  const [verificationUri, setVerificationUri] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  const startDeviceCodeFlow = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest('/onedrive/device-code', {
        method: 'POST'
      }) as DeviceCodeResponse;

      if (response.success && response.device_code) {
        setDeviceCode(response.device_code);
        setUserCode(response.user_code || null);
        setVerificationUri(response.verification_uri || null);
        setMessage(response.message || null);
        
        // Start polling for the token
        startPolling(response.device_code, response.interval || 5);
      } else {
        onError(response.error || 'Failed to start device code flow');
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to start device code flow');
    } finally {
      setIsLoading(false);
    }
  };

  const startPolling = (deviceCode: string, interval: number) => {
    setIsPolling(true);
    
    const pollInterval = setInterval(async () => {
      try {
        const response = await apiRequest('/onedrive/device-token', {
          method: 'POST',
          body: JSON.stringify({ device_code: deviceCode })
        }) as TokenResponse;

        if (response.success && response.access_token) {
          clearInterval(pollInterval);
          setIsPolling(false);
          onSuccess(response.access_token);
        } else if (response.error === 'authorization_declined') {
          clearInterval(pollInterval);
          setIsPolling(false);
          onError('Authorization was declined by the user');
        } else if (response.error === 'expired_token') {
          clearInterval(pollInterval);
          setIsPolling(false);
          onError('The device code has expired. Please try again.');
        } else if (response.error !== 'authorization_pending') {
          clearInterval(pollInterval);
          setIsPolling(false);
          onError(response.error || 'Failed to get access token');
        }
        // If authorization_pending, continue polling
      } catch (error) {
        clearInterval(pollInterval);
        setIsPolling(false);
        onError(error instanceof Error ? error.message : 'Failed to poll for token');
      }
    }, interval * 1000);

    // Set a timeout to stop polling after 15 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      if (isPolling) {
        setIsPolling(false);
        onError('Authentication timeout. Please try again.');
      }
    }, 15 * 60 * 1000);
  };

  const copyUserCode = () => {
    if (userCode) {
      navigator.clipboard.writeText(userCode);
    }
  };

  const openVerificationPage = () => {
    if (verificationUri) {
      window.open(verificationUri, '_blank');
    }
  };

  return (
    <div className="onedrive-device-auth">
      <h3>OneDrive Authentication</h3>
      
      {!deviceCode && (
        <div>
          <p>Click the button below to start OneDrive authentication using device code flow.</p>
          <button 
            onClick={startDeviceCodeFlow} 
            disabled={isLoading}
            className="btn btn-primary"
          >
            {isLoading ? 'Starting...' : 'Save to OneDrive'}
          </button>
        </div>
      )}

      {deviceCode && (
        <div className="device-code-instructions">
          <h4>Authentication Instructions</h4>
          
          <div className="alert alert-info">
            <p><strong>Step 1:</strong> Copy the code below:</p>
            <div className="user-code-container">
              <code className="user-code">{userCode}</code>
              <button onClick={copyUserCode} className="btn btn-sm btn-secondary ml-2">
                Copy Code
              </button>
            </div>
          </div>

          <div className="alert alert-warning">
            <p><strong>Step 2:</strong> Open the verification page:</p>
            <button onClick={openVerificationPage} className="btn btn-primary">
              Open Verification Page
            </button>
            <p className="mt-2">
              <small>URL: {verificationUri}</small>
            </p>
          </div>

          <div className="alert alert-success">
            <p><strong>Step 3:</strong> Enter the code on the verification page and sign in with your Microsoft account.</p>
          </div>

          {isPolling && (
            <div className="polling-status">
              <div className="spinner-border spinner-border-sm mr-2" role="status">
                <span className="sr-only">Loading...</span>
              </div>
              <span>Waiting for authentication... Please complete the steps above.</span>
            </div>
          )}

          {message && (
            <div className="alert alert-info">
              <p>{message}</p>
            </div>
          )}
        </div>
      )}

      <style jsx>{`
        .onedrive-device-auth {
          max-width: 600px;
          margin: 20px auto;
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 8px;
          background-color: #f9f9f9;
        }

        .user-code-container {
          display: flex;
          align-items: center;
          margin: 10px 0;
        }

        .user-code {
          font-size: 18px;
          font-weight: bold;
          padding: 8px 12px;
          background-color: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 4px;
          letter-spacing: 2px;
        }

        .polling-status {
          display: flex;
          align-items: center;
          margin: 15px 0;
          padding: 10px;
          background-color: #e3f2fd;
          border-radius: 4px;
        }

        .alert {
          padding: 12px;
          margin: 10px 0;
          border-radius: 4px;
        }

        .alert-info {
          background-color: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
        }

        .alert-warning {
          background-color: #fff3cd;
          border: 1px solid #ffeaa7;
          color: #856404;
        }

        .alert-success {
          background-color: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }

        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          text-decoration: none;
          display: inline-block;
        }

        .btn-primary {
          background-color: #007bff;
          color: white;
        }

        .btn-primary:hover {
          background-color: #0056b3;
        }

        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }

        .btn-secondary:hover {
          background-color: #545b62;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .spinner-border {
          width: 1rem;
          height: 1rem;
          border: 0.125em solid currentColor;
          border-right-color: transparent;
          border-radius: 50%;
          animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default OneDriveDeviceAuth;
