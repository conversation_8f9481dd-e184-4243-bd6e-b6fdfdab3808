package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.service.OneDriveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Separate controller for OneDrive OAuth callbacks without /api prefix
 * This handles direct redirects from Azure AD
 */
@RestController
@RequestMapping("/onedrive")
@Tag(name = "OneDrive Callback", description = "OneDrive OAuth callback endpoints")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3060", "http://localhost:8091"})
public class OneDriveCallbackController {

    private static final Logger logger = LoggerFactory.getLogger(OneDriveCallbackController.class);

    @Autowired
    private OneDriveService oneDriveService;

    @GetMapping("/callback")
    @Operation(summary = "Handle OneDrive OAuth callback", description = "Handle the OAuth callback from OneDrive without /api prefix")
    public ResponseEntity<String> handleCallback(
            @RequestParam("code") String code,
            @RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "error", required = false) String error) {

        logger.info("Received OneDrive callback - code: {}, state: {}, error: {}", 
                   code != null ? "present" : "null", state, error);

        if (error != null) {
            logger.error("OAuth error: {}", error);
            return ResponseEntity.ok(createCallbackResponse(false, error, null));
        }

        try {
            String accessToken = oneDriveService.handleCallback(code, state);
            logger.info("Successfully obtained access token");
            return ResponseEntity.ok(createCallbackResponse(true, null, accessToken));
        } catch (Exception e) {
            logger.error("Error handling callback", e);
            return ResponseEntity.ok(createCallbackResponse(false, e.getMessage(), null));
        }
    }

    /**
     * Create a callback response that works with popup windows
     */
    private String createCallbackResponse(boolean success, String error, String accessToken) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html>");
        html.append("<head>");
        html.append("<title>OneDrive Authentication</title>");
        html.append("<style>");
        html.append("body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; ");
        html.append("text-align: center; padding: 50px; background: #f5f5f5; }");
        html.append(".container { background: white; padding: 30px; border-radius: 8px; ");
        html.append("box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 400px; margin: 0 auto; }");
        html.append(".success { color: #28a745; }");
        html.append(".error { color: #dc3545; }");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div class='container'>");
        
        if (success) {
            html.append("<h2 class='success'>✓ Authentication Successful</h2>");
            html.append("<p>You can now close this window and return to the application.</p>");
        } else {
            html.append("<h2 class='error'>✗ Authentication Failed</h2>");
            html.append("<p>Error: ").append(error != null ? error : "Unknown error").append("</p>");
            html.append("<p>Please try again or contact support if the problem persists.</p>");
        }
        
        html.append("</div>");
        html.append("<script>");
        html.append("try {");
        html.append("  if (window.opener) {");
        html.append("    window.opener.postMessage({");
        html.append("      success: ").append(success).append(",");
        if (error != null) {
            html.append("      error: '").append(error.replace("'", "\\'")).append("',");
        }
        if (accessToken != null) {
            html.append("      accessToken: '").append(accessToken).append("',");
        }
        html.append("      source: 'onedrive-auth'");
        html.append("    }, '*');");
        html.append("    setTimeout(() => window.close(), 2000);");
        html.append("  } else {");
        html.append("    console.log('No opener window found');");
        html.append("  }");
        html.append("} catch (e) {");
        html.append("  console.error('Error posting message:', e);");
        html.append("}");
        html.append("</script>");
        html.append("</body>");
        html.append("</html>");
        
        return html.toString();
    }
}
