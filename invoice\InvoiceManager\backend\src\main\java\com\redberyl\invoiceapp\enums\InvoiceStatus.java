package com.redberyl.invoiceapp.enums;

/**
 * Enum representing the various statuses an invoice can have throughout its lifecycle.
 */
public enum InvoiceStatus {
    DRAFT("Draft"),
    APPROVED("Approved"),
    RAISED("Raised"),
    CLEARED("Cleared"),
    SENT_FOR_FILING("Sent for Filing"),
    FILING_COMPLETED("Filing Completed"),
    
    // Legacy statuses for backward compatibility
    PENDING("Pending"),
    PAID("Paid"),
    OVERDUE("Overdue");

    private final String displayName;

    InvoiceStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
