/**
 * Utility functions for authentication
 */

/**
 * Get authentication headers for API requests
 * @returns Object with Authorization header
 */
export const getAuthHeaders = (): Record<string, string> => {
  // Get the token from localStorage
  const token = localStorage.getItem('auth_token');
  
  // If token exists, return Authorization header with Bearer token
  if (token) {
    return {
      'Authorization': `Bearer ${token}`
    };
  }
  
  // For basic auth fallback (used in development)
  return {
    'Authorization': 'Basic ' + btoa('admin:admin123')
  };
};

/**
 * Check if user is authenticated
 * @returns boolean indicating if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('auth_token');
  return !!token;
};

/**
 * Get the current user's role
 * @returns string representing user role or null if not authenticated
 */
export const getUserRole = (): string | null => {
  const userInfo = localStorage.getItem('user_info');
  if (userInfo) {
    try {
      const parsedInfo = JSON.parse(userInfo);
      return parsedInfo.role || null;
    } catch (e) {
      console.error('Error parsing user info:', e);
      return null;
    }
  }
  return null;
};
