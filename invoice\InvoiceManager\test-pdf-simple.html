<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple PDF Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #1565C0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0D47A1;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #4CAF50;
        }
        .error {
            background: #FFEBEE;
            color: #C62828;
            border: 1px solid #F44336;
        }
        .info {
            background: #E3F2FD;
            color: #1565C0;
            border: 1px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple PDF Generation Test</h1>
        
        <div>
            <button class="test-button" onclick="testSimplePdf()">Generate Simple PDF</button>
            <button class="test-button" onclick="testInvoicePdf()">Generate Invoice PDF</button>
            <button class="test-button" onclick="downloadPdf()">Download Last PDF</button>
        </div>

        <div id="status"></div>
    </div>

    <script>
        let lastPdfBlob = null;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function testSimplePdf() {
            showStatus('Testing simple PDF generation...', 'info');
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF();
                
                pdf.text('Hello World!', 20, 20);
                pdf.text('This is a simple PDF test.', 20, 30);
                
                lastPdfBlob = pdf.output('blob');
                showStatus(`Simple PDF generated successfully! Size: ${lastPdfBlob.size} bytes`, 'success');
            } catch (error) {
                showStatus('Error generating simple PDF: ' + error.message, 'error');
            }
        }

        function testInvoicePdf() {
            showStatus('Testing invoice PDF generation...', 'info');
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                // Add invoice content
                addInvoiceContent(pdf);
                
                lastPdfBlob = pdf.output('blob');
                showStatus(`Invoice PDF generated successfully! Size: ${lastPdfBlob.size} bytes`, 'success');
            } catch (error) {
                showStatus('Error generating invoice PDF: ' + error.message, 'error');
            }
        }

        function addInvoiceContent(pdf) {
            // Set up fonts and colors
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(12);
            pdf.setTextColor(0, 0, 0);

            // Add header
            pdf.setFontSize(24);
            pdf.setFont('helvetica', 'bold');
            pdf.text('INVOICE', 105, 30, { align: 'center' });

            // Add company info
            pdf.setFontSize(16);
            pdf.setFont('helvetica', 'bold');
            pdf.text('RedBeryl Tech Solutions', 20, 50);
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'normal');
            pdf.text('Integrates Business With Technology', 20, 58);

            // Invoice details
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Invoice Details:', 20, 80);
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            
            pdf.text('Invoice Date: 21/01/2025', 20, 90);
            pdf.text('Invoice No.: RB/24-25/028', 20, 98);
            pdf.text('Invoice Month: January 2025', 20, 106);
            pdf.text('Invoice For: Services', 20, 114);
            pdf.text('HSN No.: 465757', 20, 122);
            pdf.text('Employee Name: Prathamesh Kadam', 20, 130);
            pdf.text('Employee Engagement Code: ENG-0020', 20, 138);

            // Billed To
            pdf.setFont('helvetica', 'bold');
            pdf.text('Billed To:', 120, 80);
            pdf.setFont('helvetica', 'normal');
            pdf.text('saurabh', 120, 90);
            pdf.text('hadapsar', 120, 98);
            pdf.text('GST No:', 120, 106);

            // Billing table
            let yPos = 160;
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(9);
            
            // Table headers
            pdf.rect(20, yPos, 170, 10);
            pdf.text('Employee Name', 25, yPos + 7);
            pdf.text('Joining Date', 60, yPos + 7);
            pdf.text('Rate', 90, yPos + 7);
            pdf.text('Bill Amount', 110, yPos + 7);
            pdf.text('GST', 140, yPos + 7);
            pdf.text('Total', 165, yPos + 7);

            // Table data
            yPos += 18;
            pdf.rect(20, yPos, 170, 12);
            pdf.setFontSize(9);
            pdf.setFont('helvetica', 'normal');
            
            pdf.text('Prathamesh Kadam', 25, yPos + 8);
            pdf.text('21/01/2025', 60, yPos + 8);
            pdf.text('₹50,000', 90, yPos + 8);
            pdf.text('₹50,000', 110, yPos + 8);
            pdf.text('₹9,000', 140, yPos + 8);
            pdf.text('₹59,000', 165, yPos + 8);

            // Net Payable
            yPos += 30;
            pdf.setTextColor(0, 0, 0);
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Net Payable: ₹59,000 /- (Fifty Nine Thousand Only)', 20, yPos);

            // Footer
            yPos += 40;
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'italic');
            pdf.text('Thank you for doing business with us.', 105, yPos, { align: 'center' });
        }

        function downloadPdf() {
            if (!lastPdfBlob) {
                showStatus('No PDF generated yet. Please generate a PDF first.', 'error');
                return;
            }

            try {
                const url = URL.createObjectURL(lastPdfBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'test-invoice.pdf';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                showStatus('PDF downloaded successfully!', 'success');
            } catch (error) {
                showStatus('Error downloading PDF: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
