package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentVariableDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.DocumentVariableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag(name = "Document Variable", description = "Document Variable management API")
public class DocumentVariableController {

    @Autowired
    private DocumentVariableService documentVariableService;

    @GetMapping("/document-variables/getAll")
    @Operation(summary = "Get all document variables", description = "Get all document variables")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document variables found"),
            @ApiResponse(responseCode = "204", description = "No document variables found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DocumentVariableDto>> getAllDocumentVariables() {
        try {
            List<DocumentVariableDto> documentVariables = documentVariableService.getAllDocumentVariables();
            return new ResponseEntity<>(documentVariables, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/document-variables/getById/{id}")
    @Operation(summary = "Get document variable by ID", description = "Get document variable by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document variable found"),
            @ApiResponse(responseCode = "404", description = "Document variable not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentVariableDto> getDocumentVariableById(@PathVariable Long id) {
        DocumentVariableDto documentVariable = documentVariableService.getDocumentVariableById(id);
        return new ResponseEntity<>(documentVariable, HttpStatus.OK);
    }

    @GetMapping("/document-variables/getByTemplateVersionId/{templateVersionId}")
    @Operation(summary = "Get document variables by template version ID", description = "Get document variables by template version ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document variables found"),
            @ApiResponse(responseCode = "204", description = "No document variables found for this template version"),
            @ApiResponse(responseCode = "404", description = "Template version not found"),
            @ApiResponse(responseCode = "400", description = "Invalid template version ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DocumentVariableDto>> getDocumentVariablesByTemplateVersionId(
            @PathVariable Long templateVersionId) {
        try {
            List<DocumentVariableDto> documentVariables = documentVariableService
                    .getDocumentVariablesByTemplateVersionId(templateVersionId);
            return new ResponseEntity<>(documentVariables, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/document-variables/create")
    @Operation(summary = "Create document variable", description = "Create document variable. The templateVersionId field supports flexible formats:\n\n" +
            "1. Simple string: \"templateVersionId\": \"1\"\n" +
            "2. Simple number: \"templateVersionId\": 1\n" +
            "3. Nested object: \"templateVersionId\": {\"id\": 1, ...}")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Document variable created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentVariableDto> createDocumentVariable(
            @Valid @RequestBody DocumentVariableDto documentVariableDto) {
        DocumentVariableDto createdDocumentVariable = documentVariableService
                .createDocumentVariable(documentVariableDto);
        return new ResponseEntity<>(createdDocumentVariable, HttpStatus.CREATED);
    }

    @PutMapping("/document-variables/update/{id}")
    @Operation(summary = "Update document variable", description = "Update document variable")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Document variable updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Document variable not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentVariableDto> updateDocumentVariable(@PathVariable Long id,
            @Valid @RequestBody DocumentVariableDto documentVariableDto) {
        DocumentVariableDto updatedDocumentVariable = documentVariableService.updateDocumentVariable(id,
                documentVariableDto);
        return new ResponseEntity<>(updatedDocumentVariable, HttpStatus.OK);
    }

    @DeleteMapping("/document-variables/deleteById/{id}")
    @Operation(summary = "Delete document variable", description = "Delete document variable")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Document variable deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or document variable is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Document variable not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteDocumentVariable(@PathVariable Long id) {
        documentVariableService.deleteDocumentVariable(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
