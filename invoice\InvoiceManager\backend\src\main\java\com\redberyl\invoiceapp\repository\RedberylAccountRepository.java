package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.RedberylAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RedberylAccountRepository extends JpaRepository<RedberylAccount, Long> {
    Optional<RedberylAccount> findByAccountNo(String accountNo);
    Optional<RedberylAccount> findByGstn(String gstn);
    Optional<RedberylAccount> findByPanNo(String panNo);
}
