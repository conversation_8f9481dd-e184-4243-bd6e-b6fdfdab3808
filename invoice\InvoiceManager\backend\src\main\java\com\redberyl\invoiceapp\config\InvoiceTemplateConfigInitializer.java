package com.redberyl.invoiceapp.config;

import com.redberyl.invoiceapp.service.InvoiceTemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Initializes default invoice template configurations on application startup
 */
@Component
@Order(20) // Run after other initializers
public class InvoiceTemplateConfigInitializer implements CommandLineRunner {

    @Autowired
    private InvoiceTemplateConfigService configService;

    @Override
    public void run(String... args) throws Exception {
        try {
            System.out.println("Checking invoice template configurations...");
            configService.initializeDefaultConfigurations();
            System.out.println("Invoice template configurations check completed.");
        } catch (Exception e) {
            System.err.println("Error initializing invoice template configurations: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
