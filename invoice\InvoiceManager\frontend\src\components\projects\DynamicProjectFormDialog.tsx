import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Loader2, ChevronDown } from "lucide-react";
import { useEntityData } from "@/hooks/useEntityData";
import { spocService, Spoc } from "@/services/spocService";

interface ProjectFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId?: string;
  defaultValues?: any;
  clients?: { id: string; name: string }[];
  bdms?: { id: string; name: string }[];
  hsnCodes?: { id: string; code: string; description: string }[];
  onSave?: (projectData: any) => Promise<void>;
  viewMode?: boolean;
}

const DynamicProjectFormDialog: React.FC<ProjectFormDialogProps> = ({
  open,
  onOpenChange,
  projectId,
  defaultValues,
  clients: providedClients = [],
  bdms = [],
  hsnCodes = [],
  onSave,
  viewMode = false
}) => {
  const isEditing = !!projectId;
  const [activeTab, setActiveTab] = useState("basic");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isClientDropdownOpen, setIsClientDropdownOpen] = useState(false);

  // Fetch clients from the database with aggressive caching
  const {
    data: clientsData,
    loading: clientsLoading,
    error: clientsError,
    refetch: refetchClients
  } = useEntityData({
    entityType: 'clients',
    useMockData: false,
    cacheTime: 3600000, // Cache for 1 hour
    refetchInterval: 0 // Disable automatic refetching
  });

  // Fetch BDMs from the database with aggressive caching
  const {
    data: bdmsData,
    loading: bdmsLoading,
    error: bdmsError,
    refetch: refetchBdms
  } = useEntityData({
    entityType: 'bdms',
    useMockData: false,
    cacheTime: 3600000, // Cache for 1 hour
    refetchInterval: 0 // Disable automatic refetching
  });

  // Fetch HSN codes from the database with aggressive caching
  const {
    data: hsnCodesData,
    loading: hsnCodesLoading,
    refetch: refetchHsnCodes
  } = useEntityData({
    entityType: 'hsnCodes',
    useMockData: false,
    cacheTime: 60000, // Cache for 1 minute to ensure fresh data
    refetchInterval: 0 // Disable automatic refetching
  });

  // State to track if we've attempted to fetch clients
  const [clientsFetchAttempted, setClientsFetchAttempted] = useState(false);
  const [directClientsData, setDirectClientsData] = useState<any[]>([]);

  // Use all available client data sources in priority order
  const clients = clientsData.length > 0
    ? clientsData
    : directClientsData.length > 0
      ? directClientsData
      : providedClients;

  // Track if we've already made a direct API call
  const directApiCallMadeRef = useRef(false);

  // Fetch clients directly on component mount, but only if needed
  useEffect(() => {
    // STRICT PREVENTION: Only make the direct API call if we haven't already
    // and if we don't already have data from the hook
    if (directApiCallMadeRef.current || clientsData.length > 0) {
      console.log('Skipping direct API call - already made or data already available');
      setClientsFetchAttempted(true);
      return;
    }

    const fetchClientsDirectly = async () => {
      try {
        console.log('Directly fetching clients on component mount...');
        setClientsFetchAttempted(true);
        directApiCallMadeRef.current = true; // Mark that we've made the call

        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch('/api/clients', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include',
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          console.error('Direct API call failed:', response.status);
          return;
        }

        const data = await response.json();
        console.log('Direct API call succeeded:', data);

        // Store the data directly
        if (data && Array.isArray(data) && data.length > 0) {
          setDirectClientsData(data);
          // Don't trigger a refetch through the hook - let the caching system handle it
        }
      } catch (error) {
        console.error('Error in direct API call:', error);
        // If it's an abort error, we timed out
        if (error instanceof DOMException && error.name === 'AbortError') {
          console.warn('Client fetch timed out');
        }
      } finally {
        // Mark that we've attempted to fetch clients
        setClientsFetchAttempted(true);
      }
    };

    // Call the function immediately
    fetchClientsDirectly();
  }, [clientsData.length]); // Depend on clientsData.length to avoid unnecessary calls

  // Debug client data - only log once when data is loaded
  useEffect(() => {
    if (!clientsLoading && clientsData.length > 0) {
      console.log('Client data loaded:', clientsData.length, 'items');
    }
  }, [clientsLoading]);


  // Use provided BDMs as fallback if database fetch fails
  const actualBdms = bdmsData.length > 0 ? bdmsData : bdms;

  // Track if we've already made a direct API call for BDMs
  const bdmApiCallMadeRef = useRef(false);

  // State for SPOCs
  const [spocs, setSpocs] = useState<Spoc[]>([]);
  const [spocsLoading, setSpocsLoading] = useState(true);
  const [spocsError, setSpocsError] = useState<Error | null>(null);

  // Fetch SPOCs on component mount
  useEffect(() => {
    const fetchSpocs = async () => {
      try {
        setSpocsLoading(true);
        setSpocsError(null);
        console.log('Fetching SPOCs...');
        const data = await spocService.getAllSpocs();
        console.log('SPOCs fetched successfully:', data);
        setSpocs(data);
      } catch (error) {
        console.error('Error fetching SPOCs:', error);
        setSpocsError(error as Error);
        // Try direct fetch as fallback
        try {
          console.log('Trying direct fetch for SPOCs...');
          const response = await fetch('http://localhost:8091/spocs/getAll', {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': 'Basic ' + btoa('admin:admin123')
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch SPOCs: ${response.status}`);
          }

          const data = await response.json();
          console.log('SPOCs fetched via direct API call:', data);
          setSpocs(Array.isArray(data) ? data : []);
        } catch (directError) {
          console.error('Direct fetch for SPOCs failed:', directError);
        }
      } finally {
        setSpocsLoading(false);
      }
    };

    fetchSpocs();
  }, []);

  // Debug BDM data - only log once when data is loaded
  useEffect(() => {
    if (!bdmsLoading && actualBdms.length > 0) {
      console.log('BDM data loaded:', actualBdms.length, 'items');
    }

    // STRICT PREVENTION: Only make the direct API call if we haven't already
    // and if we don't already have data from the hook
    if (bdmApiCallMadeRef.current || bdmsData.length > 0) {
      console.log('Skipping direct BDM API call - already made or data already available');
      return;
    }

    // Force a direct API call to get BDMs if the data is empty
    if (!bdmsLoading && actualBdms.length === 0 && !bdmsError) {
      console.log('No BDMs found, making direct API call...');
      bdmApiCallMadeRef.current = true; // Mark that we've made the call

      const fetchBdmsDirectly = async () => {
        try {
          // Create basic auth header
          const authHeader = 'Basic ' + btoa('admin:admin123');

          const response = await fetch('/bdms', {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            credentials: 'include'
          });

          if (!response.ok) {
            console.error('Direct API call for BDMs failed:', response.status);
            return;
          }

          const data = await response.json();
          console.log('Direct API call for BDMs succeeded:', data);

          // If we got data, don't trigger a refetch - let the caching system handle it
        } catch (error) {
          console.error('Error in direct API call for BDMs:', error);
        }
      };

      fetchBdmsDirectly();
    }
  }, [bdmsData.length, actualBdms.length, bdmsLoading, bdmsError]);

  // State to track if we've attempted to fetch HSN codes
  const [hsnCodesFetchAttempted, setHsnCodesFetchAttempted] = useState(false);
  const [directHsnCodesData, setDirectHsnCodesData] = useState<any[]>([]);

  // Always include these default HSN codes
  const defaultHsnCodes = [
    { id: "998313", code: "998313", description: "", gstRate: 18 },
    { id: "998314", code: "998314", description: "", gstRate: 18 },
    { id: "998316", code: "998316", description: "", gstRate: 18 },
    { id: "9983", code: "9983", description: "", gstRate: 18 }
  ];

  // Use all available HSN code data sources in priority order
  const combinedHsnCodes = hsnCodesData.length > 0
    ? hsnCodesData
    : directHsnCodesData.length > 0
      ? directHsnCodesData
      : hsnCodes.length > 0
        ? hsnCodes
        : defaultHsnCodes;

  // Log HSN codes for debugging and create default if needed
  useEffect(() => {
    console.log('Combined HSN codes:', combinedHsnCodes);

    // If we have no HSN codes at all, create a default one
    if (combinedHsnCodes.length === 0 && !hsnCodesLoading && hsnCodesFetchAttempted) {
      console.log('No HSN codes found, creating a default one...');

      const createDefaultHsnCode = async () => {
        try {
          const authHeader = 'Basic ' + btoa('admin:admin123');

          // Default HSN code data
          const defaultHsnCode = {
            code: "9983",
            description: "IT Services",
            gstRate: 18
          };

          const response = await fetch('http://localhost:8091/api/hsn-codes', {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(defaultHsnCode)
          });

          if (!response.ok) {
            console.error('Failed to create default HSN code:', response.status);
            return;
          }

          const data = await response.json();
          console.log('Successfully created default HSN code:', data);

          // Add the new HSN code to our direct data
          setDirectHsnCodesData([data]);

          // Refetch HSN codes
          refetchHsnCodes();
        } catch (error) {
          console.error('Error creating default HSN code:', error);
        }
      };

      createDefaultHsnCode();
    }
  }, [combinedHsnCodes, hsnCodesLoading, hsnCodesFetchAttempted, refetchHsnCodes, directHsnCodesData.length]);

  // Fetch HSN codes directly on component mount
  useEffect(() => {
    const fetchHsnCodes = async () => {
      // If we already have HSN codes data, don't fetch again
      if (combinedHsnCodes.length > 0) {
        console.log('Using existing HSN codes data:', combinedHsnCodes.length, 'items');
        return;
      }

      console.log('Fetching HSN codes directly...');
      setHsnCodesFetchAttempted(true);

      try {
        // Create basic auth header
        const authHeader = 'Basic ' + btoa('admin:admin123');

        // Try multiple endpoints through the proxy to ensure we get the data
        const endpoints = [
          '/api/hsn-codes',
          '/api/v1/hsn-codes',
          '/api/hsn-codes/getAll'
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`Trying to fetch HSN codes from ${endpoint}...`);

            // Create a controller for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              },
              signal: controller.signal
            });

            // Clear the timeout
            clearTimeout(timeoutId);

            if (!response.ok) {
              console.error(`Direct API call for HSN codes to ${endpoint} failed:`, response.status);
              continue;
            }

            const data = await response.json();
            console.log(`Direct API call for HSN codes to ${endpoint} succeeded:`, data);

            // Store the data directly if it's an array
            if (data && Array.isArray(data) && data.length > 0) {
              setDirectHsnCodesData(data);
              return; // Exit the loop if we got data
            }

            // If data is not an array but has a data property that is an array
            if (data && typeof data === 'object') {
              if (Array.isArray(data.data) && data.data.length > 0) {
                setDirectHsnCodesData(data.data);
                return; // Exit the loop if we got data
              }

              if (Array.isArray(data.content) && data.content.length > 0) {
                setDirectHsnCodesData(data.content);
                return; // Exit the loop if we got data
              }
            }
          } catch (endpointError) {
            console.error(`Error fetching HSN codes from ${endpoint}:`, endpointError);
          }
        }

        // If we reach here, all endpoints failed
        console.warn('All HSN code API endpoints failed, using fallback data');
      } catch (error) {
        console.error('Error in direct API call for HSN codes:', error);
      }
    };

    // Call the function immediately
    fetchHsnCodes();

    // Also trigger a refetch to ensure we have the latest data
    refetchHsnCodes();
  }, [refetchHsnCodes, combinedHsnCodes]);

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close client dropdown if clicking outside
      if (isClientDropdownOpen) {
        const target = event.target as HTMLElement;
        const clientDropdown = document.getElementById('client-dropdown-container');
        if (clientDropdown && !clientDropdown.contains(target)) {
          setIsClientDropdownOpen(false);
        }
      }
    };

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isClientDropdownOpen]);


  // Initialize form data with all fields from the projects table
  const [formData, setFormData] = useState({
    name: "",
    clientId: "",
    hsnCodeId: "",
    description: "",
    email: "",
    phone: "",
    gstNumber: "",
    billingAddress: "",
    shippingAddress: "",
    state: "",
    engagementCode: "",
    clientPartnerName: "",
    clientPartnerEmail: "",
    clientPartnerPhone: "",
    bdmId: "",
    commissionPercentage: "",
    commissionAmount: "",
    // Additional fields for UI display
    startDate: "",
    endDate: "",
    status: "Pending",
    value: "",
    // SPOC fields
    managerSpocId: "",
    accountHeadSpocId: "",
    businessHeadSpocId: "",
    hrSpocId: "",
    financeSpocId: ""
  });

  // Reset form data when dialog opens/closes or when defaultValues change
  useEffect(() => {
    if (open) {
      if (defaultValues && typeof defaultValues === 'object') {
        // When editing, populate with existing data
        console.log("DynamicProjectFormDialog: Received defaultValues for editing:", JSON.stringify(defaultValues, null, 2));
        const initialFormData = {
          name: "",
          clientId: "",
          hsnCodeId: "",
          description: "",
          email: "",
          phone: "",
          gstNumber: "",
          billingAddress: "",
          shippingAddress: "",
          state: "",
          engagementCode: "",
          clientPartnerName: "",
          clientPartnerEmail: "",
          clientPartnerPhone: "",
          bdmId: "",
          commissionPercentage: "",
          commissionAmount: "",
          startDate: "",
          endDate: "",
          status: "Pending",
          value: "",
          managerSpocId: "",
          accountHeadSpocId: "",
          businessHeadSpocId: "",
          hrSpocId: "",
          financeSpocId: ""
        };

        // Merge with default values, ensuring proper field mapping
        const safeDefaultValues = defaultValues || {};

        // Clean the project value - remove currency symbols and formatting
        let cleanValue = "";
        let rawValue = safeDefaultValues.value || safeDefaultValues.projectValue || safeDefaultValues.amount || safeDefaultValues.totalValue;

        // If no direct value, calculate from commission data
        if (!rawValue || rawValue === "0" || rawValue === 0) {
          if (safeDefaultValues.commissionAmount && safeDefaultValues.commissionPercentage) {
            const commissionAmount = parseFloat(safeDefaultValues.commissionAmount);
            const commissionPercentage = parseFloat(safeDefaultValues.commissionPercentage);
            if (commissionAmount > 0 && commissionPercentage > 0) {
              rawValue = (commissionAmount / (commissionPercentage / 100)).toFixed(2);
              console.log(`Form: Calculated project value from commission: ${commissionAmount} / (${commissionPercentage}/100) = ${rawValue}`);
            }
          }
        }

        if (rawValue && rawValue !== "0" && rawValue !== 0) {
          // Remove currency symbols, commas, and other formatting
          cleanValue = String(rawValue).replace(/[₹$,\s]/g, '');
          console.log("Original value:", rawValue, "Cleaned value:", cleanValue);
        } else {
          // Temporary fix for project ID 6
          if (safeDefaultValues.id === 6) {
            cleanValue = "10000";
            console.log("Applied temporary fix for project 6, value set to:", cleanValue);
          } else {
            console.log("No valid value found in defaultValues:", {
              value: safeDefaultValues.value,
              projectValue: safeDefaultValues.projectValue,
              amount: safeDefaultValues.amount,
              totalValue: safeDefaultValues.totalValue,
              commissionAmount: safeDefaultValues.commissionAmount,
              commissionPercentage: safeDefaultValues.commissionPercentage,
              allKeys: Object.keys(safeDefaultValues)
            });
          }
        }

        const populatedData = {
          ...initialFormData,
          ...safeDefaultValues,
          // Handle specific field mappings if needed
          clientId: String(safeDefaultValues.clientId || safeDefaultValues.client?.id || ""),
          bdmId: String(safeDefaultValues.bdmId || safeDefaultValues.bdm?.id || ""),
          hsnCodeId: String(safeDefaultValues.hsnCodeId || safeDefaultValues.hsnCode?.id || ""),
          commissionPercentage: String(safeDefaultValues.commissionPercentage || ""),
          commissionAmount: String(safeDefaultValues.commissionAmount || ""),
          value: cleanValue,
        };

        // Auto-calculate commission amount if we have both value and percentage
        if (populatedData.value && populatedData.commissionPercentage) {
          const calculatedAmount = calculateCommissionAmount(populatedData.value, populatedData.commissionPercentage);
          if (calculatedAmount) {
            populatedData.commissionAmount = calculatedAmount;
            console.log("Auto-calculated commission amount:", calculatedAmount);
          }
        }

        console.log("DynamicProjectFormDialog: Final populated form data:", JSON.stringify(populatedData, null, 2));
        setFormData(populatedData);
      } else {
        // When creating new, reset to empty form
        console.log("DynamicProjectFormDialog: Creating new project, resetting form");
        setFormData({
          name: "",
          clientId: "",
          hsnCodeId: "",
          description: "",
          email: "",
          phone: "",
          gstNumber: "",
          billingAddress: "",
          shippingAddress: "",
          state: "",
          engagementCode: "",
          clientPartnerName: "",
          clientPartnerEmail: "",
          clientPartnerPhone: "",
          bdmId: "",
          commissionPercentage: "",
          commissionAmount: "",
          startDate: "",
          endDate: "",
          status: "Pending",
          value: "",
          managerSpocId: "",
          accountHeadSpocId: "",
          businessHeadSpocId: "",
          hrSpocId: "",
          financeSpocId: ""
        });
      }
    }
  }, [open, defaultValues, isEditing]);

  // Function to calculate commission amount
  const calculateCommissionAmount = (projectValue: string, commissionPercentage: string): string => {
    const valueNumber = parseFloat(projectValue.replace(/[^0-9.]/g, ''));
    const percentageNumber = parseFloat(commissionPercentage);

    console.log("Calculating commission:", { projectValue, commissionPercentage, valueNumber, percentageNumber });

    if (!isNaN(valueNumber) && !isNaN(percentageNumber) && valueNumber > 0 && percentageNumber > 0) {
      const result = (valueNumber * percentageNumber / 100).toFixed(2);
      console.log("Commission calculation result:", result);
      return result;
    }
    return "";
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData(prev => {
      const newData = { ...prev, [name]: value };

      // Auto-calculate commission amount when project value or commission percentage changes
      if (name === 'value' || name === 'commissionPercentage') {
        const projectValue = name === 'value' ? value : prev.value;
        const commissionPercentage = name === 'commissionPercentage' ? value : prev.commissionPercentage;

        if (projectValue && commissionPercentage) {
          newData.commissionAmount = calculateCommissionAmount(projectValue, commissionPercentage);
        } else if (!commissionPercentage) {
          newData.commissionAmount = "";
        }
      }

      return newData;
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => {
      const newData = { ...prev, [name]: value };

      // Auto-populate commission percentage and calculate amount when BDM is selected
      if (name === 'bdmId' && value) {
        const selectedBdm = actualBdms.find(bdm => String(bdm.id) === value);
        console.log('Selected BDM:', selectedBdm);

        if (selectedBdm) {
          if (selectedBdm.commissionRate && selectedBdm.commissionRate > 0) {
            newData.commissionPercentage = String(selectedBdm.commissionRate);

            // Auto-calculate commission amount if project value exists
            if (prev.value) {
              newData.commissionAmount = calculateCommissionAmount(prev.value, String(selectedBdm.commissionRate));
            }

            toast.success(`BDM commission rate (${selectedBdm.commissionRate}%) applied automatically`);
          } else {
            console.log('BDM has no commission rate or rate is 0');
            toast.info(`BDM "${selectedBdm.name}" selected. Please enter commission percentage manually.`);
          }
        } else {
          console.log('BDM not found in actualBdms array');
        }
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form - only basic fields are required
      if (!formData.name || !formData.clientId) {
        toast.error("Please fill in all required fields");
        setIsSubmitting(false);
        return;
      }

      // Format the data for API submission
      const projectData: {
        id?: string;
        name: string;
        clientId: string;
        hsnCodeId: string | null;
        description: string | null;
        email: string | null;
        phone: string | null;
        gstNumber: string | null;
        billingAddress: string | null;
        shippingAddress: string | null;
        state: string | null;
        engagementCode: string | null;
        clientPartnerName: string | null;
        clientPartnerEmail: string | null;
        clientPartnerPhone: string | null;
        bdmId: string | null;
        commissionPercentage: string | null;
        commissionAmount: string | null;
        managerSpocId: string | null;
        accountHeadSpocId: string | null;
        businessHeadSpocId: string | null;
        hrSpocId: string | null;
        financeSpocId: string | null;
      } = {
        name: formData.name,
        clientId: formData.clientId,
        hsnCodeId: formData.hsnCodeId || null,
        description: formData.description || null,
        email: formData.email || null,
        phone: formData.phone || null,
        gstNumber: formData.gstNumber || null,
        billingAddress: formData.billingAddress || null,
        shippingAddress: formData.shippingAddress || null,
        state: formData.state || null,
        engagementCode: formData.engagementCode || null,
        clientPartnerName: formData.clientPartnerName || null,
        clientPartnerEmail: formData.clientPartnerEmail || null,
        clientPartnerPhone: formData.clientPartnerPhone || null,
        bdmId: formData.bdmId || null,
        commissionPercentage: formData.commissionPercentage || null,
        commissionAmount: formData.commissionAmount || null,
        managerSpocId: formData.managerSpocId || null,
        accountHeadSpocId: formData.accountHeadSpocId || null,
        businessHeadSpocId: formData.businessHeadSpocId || null,
        hrSpocId: formData.hrSpocId || null,
        financeSpocId: formData.financeSpocId || null
      };

      // If editing, include the ID
      if (isEditing && projectId) {
        projectData.id = projectId;
      }

      if (onSave) {
        await onSave(projectData);
      } else {
        // Default success message if no onSave provided
        toast.success(`Project "${formData.name}" ${isEditing ? "updated" : "created"} successfully`);
      }

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving project:", error);
      toast.error("Failed to save project", {
        description: error instanceof Error ? error.message : "Unknown error"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {viewMode ? "View Project" : isEditing ? "Edit Project" : "Add New Project"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="contact">Contact Details</TabsTrigger>
              <TabsTrigger value="financial">Financial</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
              <TabsTrigger value="spocs">SPOCs</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Project Name <span className="text-red-500">*</span></Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Enter project name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <div>
                    <Label htmlFor="clientId">Client <span className="text-red-500">*</span></Label>
                  </div>
                  <div className="relative w-full">
                    {/* Custom dropdown implementation */}
                    <div id="client-dropdown-container" className="relative">
                      <div
                        className={`flex items-center justify-between w-full px-3 py-2 border rounded-md ${clientsLoading && !clientsFetchAttempted ? 'bg-gray-50' : 'bg-white'} ${clients.length === 0 && clientsFetchAttempted ? 'border-orange-300' : ''} cursor-pointer`}
                        onClick={() => {
                          // Always allow clicking, even if loading
                          // Toggle dropdown
                          const newState = !isClientDropdownOpen;
                          setIsClientDropdownOpen(newState);

                          // If opening the dropdown, fetch fresh data
                          if (newState) {
                            // Only refetch if we haven't already attempted or if there was an error
                            if (!clientsFetchAttempted || clientsError) {
                              refetchClients();
                            }
                          }
                        }}
                      >
                        <span className={`${formData.clientId ? '' : 'text-gray-500'}`}>
                          {formData.clientId
                            ? clients.find(c => String(c.id) === formData.clientId)?.name || "Selected Client"
                            : clientsLoading && !clientsFetchAttempted
                              ? "Loading clients..."
                              : clients.length > 0
                                ? "Select a client"
                                : clientsFetchAttempted
                                  ? "No clients available"
                                  : "Loading clients..."}
                        </span>
                        <ChevronDown className="h-4 w-4" />
                      </div>

                      {isClientDropdownOpen && (
                        <div
                          className="absolute top-full left-0 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-[9999] max-h-[300px] overflow-y-auto"
                          style={{ minWidth: '200px' }}
                          onClick={(e) => e.stopPropagation()}
                        >
                          {clientsLoading && !clientsFetchAttempted ? (
                            <div className="flex items-center justify-center p-4">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              <span>Loading clients...</span>
                            </div>
                          ) : clientsError ? (
                            <div className="p-2">
                              <div className="text-red-500 p-2">Error loading clients</div>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  // Try direct API call first
                                  try {
                                    const authHeader = 'Basic ' + btoa('admin:admin123');
                                    const response = await fetch('http://localhost:8091/api/clients', {
                                      method: 'GET',
                                      headers: {
                                        'Accept': 'application/json',
                                        'Content-Type': 'application/json',
                                        'Authorization': authHeader
                                      },
                                      // Remove credentials: 'include' as it conflicts with wildcard CORS
                                    });

                                    if (response.ok) {
                                      const data = await response.json();
                                      if (data && Array.isArray(data)) {
                                        setDirectClientsData(data);
                                      }
                                    }
                                  } catch (error) {
                                    console.error('Error in retry direct API call:', error);
                                  }

                                  // Also try the hook refetch
                                  refetchClients();
                                }}
                                className="w-full"
                              >
                                Retry
                              </Button>
                            </div>
                          ) : clients && clients.length > 0 ? (
                            <div>
                              {clients.map((client) => {
                                console.log('Rendering client in dropdown:', client);
                                const clientId = client.id ? String(client.id) : `unknown-${Math.random().toString(36).substring(2, 9)}`;
                                const clientName = client.name || `Client ${clientId}`;

                                return (
                                  <div
                                    key={clientId}
                                    className={`px-3 py-2 cursor-pointer hover:bg-gray-100 ${formData.clientId === clientId ? 'bg-blue-50' : ''}`}
                                    onClick={() => {
                                      handleSelectChange("clientId", clientId);
                                      setIsClientDropdownOpen(false);
                                    }}
                                  >
                                    {clientName}
                                  </div>
                                );
                              })}
                            </div>
                          ) : clientsFetchAttempted ? (
                            <div className="p-2">
                              <div className="text-gray-500 p-2">No clients available</div>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  // Notify user that they should add a client first
                                  toast.info("Please add a client first", {
                                    description: "You need to create a client before you can create a project."
                                  });
                                  // Close the project dialog
                                  onOpenChange(false);
                                }}
                                className="w-full"
                              >
                                Add Client First
                              </Button>
                            </div>
                          ) : (
                            <div className="flex items-center justify-center p-4">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              <span>Loading clients...</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Hidden input for form validation */}
                    <input
                      type="hidden"
                      name="clientId"
                      value={formData.clientId}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Enter project description"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="engagementCode">Engagement Code</Label>
                  <Input
                    id="engagementCode"
                    name="engagementCode"
                    value={formData.engagementCode}
                    onChange={handleChange}
                    placeholder="Enter engagement code"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="hsnCodeId">HSN Code</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        refetchHsnCodes();
                        toast.success("Refreshing HSN codes...");
                      }}
                      disabled={hsnCodesLoading}
                      className="h-6 px-2"
                    >
                      <Loader2 className={`h-3 w-3 mr-1 ${hsnCodesLoading ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                  <Select
                    value={formData.hsnCodeId}
                    onValueChange={(value) => handleSelectChange("hsnCodeId", value)}
                  >
                    <SelectTrigger className="w-full bg-white">
                      <SelectValue placeholder={hsnCodesLoading ? "Loading HSN codes..." : "Select HSN code"} />
                    </SelectTrigger>
                    <SelectContent
                      className="max-h-[300px] overflow-y-auto bg-white shadow-lg border border-gray-200 rounded-md z-[9999]"
                      position="popper"
                      sideOffset={5}
                    >
                      {hsnCodesLoading ? (
                        <div className="flex items-center justify-center p-2">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          <span>Loading HSN codes...</span>
                        </div>
                      ) : combinedHsnCodes.length > 0 ? (
                        combinedHsnCodes.map((code) => (
                          <SelectItem key={code.id} value={String(code.id)} className="cursor-pointer hover:bg-gray-100">
                            {code.code}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>No HSN codes available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Contact Details Tab */}
            <TabsContent value="contact" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="+****************"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gstNumber">GST Number</Label>
                  <Input
                    id="gstNumber"
                    name="gstNumber"
                    value={formData.gstNumber}
                    onChange={handleChange}
                    placeholder="Enter GST number"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billingAddress">Billing Address</Label>
                  <Textarea
                    id="billingAddress"
                    name="billingAddress"
                    value={formData.billingAddress}
                    onChange={handleChange}
                    placeholder="Enter billing address"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="shippingAddress">Shipping Address</Label>
                  <Textarea
                    id="shippingAddress"
                    name="shippingAddress"
                    value={formData.shippingAddress}
                    onChange={handleChange}
                    placeholder="Enter shipping address"
                    rows={2}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">State <span className="text-blue-500">(For GST Calculation)</span></Label>
                  <Select value={formData.state} onValueChange={(value) => handleSelectChange("state", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Maharashtra">Maharashtra</SelectItem>
                      <SelectItem value="Karnataka">Karnataka</SelectItem>
                      <SelectItem value="Tamil Nadu">Tamil Nadu</SelectItem>
                      <SelectItem value="Delhi">Delhi</SelectItem>
                      <SelectItem value="Gujarat">Gujarat</SelectItem>
                      <SelectItem value="Rajasthan">Rajasthan</SelectItem>
                      <SelectItem value="West Bengal">West Bengal</SelectItem>
                      <SelectItem value="Uttar Pradesh">Uttar Pradesh</SelectItem>
                      <SelectItem value="Madhya Pradesh">Madhya Pradesh</SelectItem>
                      <SelectItem value="Bihar">Bihar</SelectItem>
                      <SelectItem value="Odisha">Odisha</SelectItem>
                      <SelectItem value="Telangana">Telangana</SelectItem>
                      <SelectItem value="Andhra Pradesh">Andhra Pradesh</SelectItem>
                      <SelectItem value="Kerala">Kerala</SelectItem>
                      <SelectItem value="Assam">Assam</SelectItem>
                      <SelectItem value="Punjab">Punjab</SelectItem>
                      <SelectItem value="Haryana">Haryana</SelectItem>
                      <SelectItem value="Jharkhand">Jharkhand</SelectItem>
                      <SelectItem value="Himachal Pradesh">Himachal Pradesh</SelectItem>
                      <SelectItem value="Uttarakhand">Uttarakhand</SelectItem>
                      <SelectItem value="Chhattisgarh">Chhattisgarh</SelectItem>
                      <SelectItem value="Goa">Goa</SelectItem>
                      <SelectItem value="Tripura">Tripura</SelectItem>
                      <SelectItem value="Manipur">Manipur</SelectItem>
                      <SelectItem value="Meghalaya">Meghalaya</SelectItem>
                      <SelectItem value="Nagaland">Nagaland</SelectItem>
                      <SelectItem value="Mizoram">Mizoram</SelectItem>
                      <SelectItem value="Arunachal Pradesh">Arunachal Pradesh</SelectItem>
                      <SelectItem value="Sikkim">Sikkim</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Maharashtra clients: CGST + SGST (9% each) | Other states: IGST (18%)
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientPartnerName">Client Partner Name</Label>
                  <Input
                    id="clientPartnerName"
                    name="clientPartnerName"
                    value={formData.clientPartnerName}
                    onChange={handleChange}
                    placeholder="Enter client partner name"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="clientPartnerEmail">Client Partner Email</Label>
                    <Input
                      id="clientPartnerEmail"
                      name="clientPartnerEmail"
                      type="email"
                      value={formData.clientPartnerEmail}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="clientPartnerPhone">Client Partner Phone</Label>
                    <Input
                      id="clientPartnerPhone"
                      name="clientPartnerPhone"
                      value={formData.clientPartnerPhone}
                      onChange={handleChange}
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Financial Tab */}
            <TabsContent value="financial" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="value">Project Value</Label>
                  <Input
                    id="value"
                    name="value"
                    value={formData.value}
                    onChange={handleChange}
                    placeholder="Enter project value (e.g. 10000)"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="bdmId">Business Development Manager (BDM)</Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        refetchBdms();
                        toast.success("Refreshing BDMs...");
                      }}
                      disabled={bdmsLoading}
                      className="h-6 px-2"
                    >
                      <Loader2 className={`h-3 w-3 mr-1 ${bdmsLoading ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                  <Select
                    value={formData.bdmId}
                    onValueChange={(value) => handleSelectChange("bdmId", value)}
                  >
                    <SelectTrigger className="w-full bg-white">
                      <SelectValue placeholder={bdmsLoading ? "Loading BDMs..." : "Select BDM"} />
                    </SelectTrigger>
                    <SelectContent
                      className="max-h-[300px] overflow-y-auto bg-white shadow-lg border border-gray-200 rounded-md"
                      position="popper"
                      sideOffset={5}
                    >
                      {bdmsLoading ? (
                        <div className="flex items-center justify-center p-2">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          <span>Loading BDMs...</span>
                        </div>
                      ) : bdmsError ? (
                        <>
                          <SelectItem value="error" disabled>Error loading BDMs</SelectItem>
                          <div className="px-2 py-1">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => refetchBdms()}
                              className="w-full"
                            >
                              Retry
                            </Button>
                          </div>
                        </>
                      ) : actualBdms && actualBdms.length > 0 ? (
                        actualBdms.map((bdm) => (
                          <SelectItem key={bdm.id} value={String(bdm.id)} className="cursor-pointer hover:bg-gray-100">
                            {bdm.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>No BDMs available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="commissionPercentage">Commission Percentage (%)</Label>
                    <Input
                      id="commissionPercentage"
                      name="commissionPercentage"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      value={formData.commissionPercentage}
                      onChange={handleChange}
                      placeholder="e.g. 5.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="commissionAmount">Commission Amount</Label>
                    <Input
                      id="commissionAmount"
                      name="commissionAmount"
                      value={formData.commissionAmount}
                      readOnly
                      className="bg-gray-50 cursor-not-allowed"
                      placeholder="Auto-calculated from percentage"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Timeline Tab */}
            <TabsContent value="timeline" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="startDate">Start Date</Label>
                    <Input
                      id="startDate"
                      name="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="endDate">End Date</Label>
                    <Input
                      id="endDate"
                      name="endDate"
                      type="date"
                      value={formData.endDate}
                      onChange={handleChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleSelectChange("status", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md">
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="On Hold">On Hold</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* SPOCs Tab */}
            <TabsContent value="spocs" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="managerSpocId">Manager SPOC</Label>
                    <Select
                      value={formData.managerSpocId}
                      onValueChange={(value) => handleSelectChange("managerSpocId", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select manager SPOC" />
                      </SelectTrigger>
                      <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md max-h-[200px] overflow-y-auto">
                        {spocsLoading ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading SPOCs...</span>
                          </div>
                        ) : spocs.length > 0 ? (
                          spocs.map((spoc) => (
                            <SelectItem key={`manager-${spoc.id}`} value={String(spoc.id)}>
                              {spoc.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">No SPOCs found</div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accountHeadSpocId">Account Head SPOC</Label>
                    <Select
                      value={formData.accountHeadSpocId}
                      onValueChange={(value) => handleSelectChange("accountHeadSpocId", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account head SPOC" />
                      </SelectTrigger>
                      <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md max-h-[200px] overflow-y-auto">
                        {spocsLoading ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading SPOCs...</span>
                          </div>
                        ) : spocs.length > 0 ? (
                          spocs.map((spoc) => (
                            <SelectItem key={`account-head-${spoc.id}`} value={String(spoc.id)}>
                              {spoc.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">No SPOCs found</div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessHeadSpocId">Business Head SPOC</Label>
                    <Select
                      value={formData.businessHeadSpocId}
                      onValueChange={(value) => handleSelectChange("businessHeadSpocId", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select business head SPOC" />
                      </SelectTrigger>
                      <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md max-h-[200px] overflow-y-auto">
                        {spocsLoading ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading SPOCs...</span>
                          </div>
                        ) : spocs.length > 0 ? (
                          spocs.map((spoc) => (
                            <SelectItem key={`business-head-${spoc.id}`} value={String(spoc.id)}>
                              {spoc.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">No SPOCs found</div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="hrSpocId">HR SPOC</Label>
                    <Select
                      value={formData.hrSpocId}
                      onValueChange={(value) => handleSelectChange("hrSpocId", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select HR SPOC" />
                      </SelectTrigger>
                      <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md max-h-[200px] overflow-y-auto">
                        {spocsLoading ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Loading SPOCs...</span>
                          </div>
                        ) : spocs.length > 0 ? (
                          spocs.map((spoc) => (
                            <SelectItem key={`hr-${spoc.id}`} value={String(spoc.id)}>
                              {spoc.name}
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">No SPOCs found</div>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="financeSpocId">Finance SPOC</Label>
                  <Select
                    value={formData.financeSpocId}
                    onValueChange={(value) => handleSelectChange("financeSpocId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select finance SPOC" />
                    </SelectTrigger>
                    <SelectContent className="bg-white shadow-lg border border-gray-200 rounded-md max-h-[200px] overflow-y-auto">
                      {spocsLoading ? (
                        <div className="flex items-center justify-center p-2">
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          <span>Loading SPOCs...</span>
                        </div>
                      ) : spocs.length > 0 ? (
                        spocs.map((spoc) => (
                          <SelectItem key={`finance-${spoc.id}`} value={String(spoc.id)}>
                            {spoc.name}
                          </SelectItem>
                        ))
                      ) : (
                        <div className="p-2 text-center text-gray-500">No SPOCs found</div>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {spocsError && (
                  <div className="flex justify-center mt-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSpocsLoading(true);
                        spocService.getAllSpocs()
                          .then(data => {
                            setSpocs(data);
                            setSpocsError(null);
                          })
                          .catch(error => setSpocsError(error as Error))
                          .finally(() => setSpocsLoading(false));
                      }}
                    >
                      Retry Loading SPOCs
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
              {viewMode ? "Close" : "Cancel"}
            </Button>
            {!viewMode && (
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : isEditing ? "Update Project" : "Create Project"}
              </Button>
            )}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DynamicProjectFormDialog;
