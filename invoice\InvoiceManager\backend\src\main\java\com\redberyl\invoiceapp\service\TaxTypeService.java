package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.TaxTypeDto;

import java.util.List;

public interface TaxTypeService {
    List<TaxTypeDto> getAllTaxTypes();
    TaxTypeDto getTaxTypeById(Long id);
    TaxTypeDto getTaxTypeByType(String taxType);
    TaxTypeDto createTaxType(TaxTypeDto taxTypeDto);
    TaxTypeDto updateTaxType(Long id, TaxTypeDto taxTypeDto);
    void deleteTaxType(Long id);
}
