package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.ReminderDto;

import java.util.List;

public interface ReminderService {
    List<ReminderDto> getAllReminders();
    ReminderDto getReminderById(Long id);
    List<ReminderDto> getRemindersByInvoiceId(Long invoiceId);
    List<ReminderDto> getRemindersByMethod(String method);
    List<ReminderDto> getRemindersByStatus(String status);
    ReminderDto createReminder(ReminderDto reminderDto);
    ReminderDto updateReminder(Long id, ReminderDto reminderDto);
    void deleteReminder(Long id);
}
