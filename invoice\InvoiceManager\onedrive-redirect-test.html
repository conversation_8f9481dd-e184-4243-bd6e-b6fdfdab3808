<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Redirect URI Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            display: block;
            width: 100%;
            text-align: left;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .redirect-uri {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🔗 OneDrive Redirect URI Tester</h1>
    <p>This tool helps test different redirect URIs to find the one configured in your Azure app registration.</p>

    <div class="test-section">
        <h2>📋 Current Azure App Details</h2>
        <div class="info result">
Client ID: 86756722-ad2a-4ac0-8806-e2705653949a
Tenant ID: 14158288-a340-4380-88ed-a8989a932425
Current Redirect URI: http://localhost:8091/api/onedrive/callback

Error: AADSTS50011 - Redirect URI mismatch
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Test Different Redirect URIs</h2>
        <p>Click each button to test different redirect URI configurations:</p>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:8091/api/onedrive/callback')">
            Test: http://localhost:8091/api/onedrive/callback (Current)
        </button>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:8091/auth/callback')">
            Test: http://localhost:8091/auth/callback
        </button>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:8091/callback')">
            Test: http://localhost:8091/callback
        </button>
        
        <button class="test-button" onclick="testRedirectUri('http://localhost:3060/auth/callback')">
            Test: http://localhost:3060/auth/callback (Frontend)
        </button>
        
        <button class="test-button" onclick="testRedirectUri('https://localhost:8091/api/onedrive/callback')">
            Test: https://localhost:8091/api/onedrive/callback (HTTPS)
        </button>
        
        <div id="test-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔧 Azure Portal Configuration Steps</h2>
        <div class="info result">
To fix this issue permanently:

1. Go to Azure Portal: https://portal.azure.com
2. Navigate to: Azure Active Directory → App registrations
3. Search for app with Client ID: 86756722-ad2a-4ac0-8806-e2705653949a
4. Click on the app name
5. Go to "Authentication" in the left sidebar
6. Under "Redirect URIs", click "Add URI"
7. Select platform: "Web"
8. Add this URI: http://localhost:8091/api/onedrive/callback
9. Click "Save"

Alternative URIs to try:
• http://localhost:8091/api/onedrive/callback
• http://localhost:8091/auth/callback  
• http://localhost:8091/callback
• http://localhost:3060/auth/callback
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 Quick Fix Options</h2>
        
        <button class="test-button" onclick="generateAuthUrl('http://localhost:8091/api/onedrive/callback')">
            Option 1: Test Current Configuration
        </button>
        
        <button class="test-button" onclick="showManualSteps()">
            Option 2: Show Manual Configuration Steps
        </button>
        
        <button class="test-button" onclick="testBackendConnection()">
            Option 3: Test Backend Connection
        </button>
        
        <div id="fix-result" class="result"></div>
    </div>

    <script>
        async function testRedirectUri(redirectUri) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = `Testing redirect URI: ${redirectUri}...`;

            try {
                // Test if we can reach the callback endpoint
                const response = await fetch(`${redirectUri.replace('/callback', '')}/test`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Backend reachable at: ${redirectUri}\n\nThis redirect URI might work. Try updating your Azure app registration with this URI.`;
                } else {
                    resultDiv.className = 'result warning';
                    resultDiv.textContent = `⚠️ Backend not reachable at: ${redirectUri}\n\nStatus: ${response.status}\n\nThis URI might not work.`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Cannot reach: ${redirectUri}\n\nError: ${error.message}\n\nThis URI will not work.`;
            }
        }

        async function generateAuthUrl(redirectUri) {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing current OneDrive configuration...';

            try {
                const response = await fetch('/api/onedrive/auth-url', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.authUrl) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `✅ OneDrive auth URL generated successfully!\n\nURL: ${data.authUrl}\n\nYou can try clicking this URL to test authentication.`;
                        
                        // Create a clickable link
                        const link = document.createElement('a');
                        link.href = data.authUrl;
                        link.target = '_blank';
                        link.textContent = 'Click here to test OneDrive authentication';
                        link.style.display = 'block';
                        link.style.marginTop = '10px';
                        link.style.color = '#007bff';
                        resultDiv.appendChild(link);
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `❌ Failed to generate auth URL\n\nResponse: ${JSON.stringify(data, null, 2)}`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Backend error: ${response.status}\n\nMake sure the backend is running on localhost:8091`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Connection error: ${error.message}\n\nMake sure:\n1. Backend is running on localhost:8091\n2. Frontend is running on localhost:3060\n3. No firewall blocking the connection`;
            }
        }

        function showManualSteps() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.className = 'result info';
            resultDiv.innerHTML = `
<strong>📋 Manual Configuration Steps:</strong>

<strong>Step 1: Update Backend Configuration</strong>
1. Stop the backend server (Ctrl+C)
2. Edit: backend/src/main/resources/application.properties
3. Try different redirect URIs:
   • onedrive.redirect.uri=http://localhost:8091/api/onedrive/callback
   • onedrive.redirect.uri=http://localhost:8091/auth/callback
   • onedrive.redirect.uri=http://localhost:3060/auth/callback

<strong>Step 2: Restart Backend</strong>
cd backend
mvn spring-boot:run

<strong>Step 3: Test Again</strong>
Try the OneDrive button after each configuration change.

<strong>Step 4: If Still Failing</strong>
Contact the Azure administrator to add the correct redirect URI to the app registration.
            `;
        }

        async function testBackendConnection() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing backend connection...';

            const endpoints = [
                '/api/onedrive/test',
                'http://localhost:8091/api/onedrive/test',
                '/api/dashboard/metrics',
                'http://localhost:8091/api/dashboard/metrics'
            ];

            let results = [];
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    results.push(`✅ ${endpoint}: ${response.status}`);
                } catch (error) {
                    results.push(`❌ ${endpoint}: ${error.message}`);
                }
            }

            resultDiv.className = 'result info';
            resultDiv.textContent = `Backend Connection Test Results:\n\n${results.join('\n')}\n\nIf all endpoints are failing, the backend is not running.`;
        }
    </script>
</body>
</html>
