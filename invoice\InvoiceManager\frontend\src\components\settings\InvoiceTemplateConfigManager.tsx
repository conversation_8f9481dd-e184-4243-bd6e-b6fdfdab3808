import React, { useState, useEffect } from 'react';
import { invoiceTemplateConfigService, InvoiceTemplateConfig } from '@/services/invoiceTemplateConfigService';

interface InvoiceTemplateConfigManagerProps {
  onClose?: () => void;
}

const InvoiceTemplateConfigManager: React.FC<InvoiceTemplateConfigManagerProps> = ({ onClose }) => {
  const [configurations, setConfigurations] = useState<InvoiceTemplateConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('ALL');
  const [editingConfig, setEditingConfig] = useState<InvoiceTemplateConfig | null>(null);

  const categories = ['ALL', 'COMPANY', 'BANK', 'TEMPLATE'];

  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = async () => {
    try {
      setLoading(true);
      setError(null);
      const configs = await invoiceTemplateConfigService.getAllConfigurations();
      setConfigurations(configs);
    } catch (err) {
      setError('Failed to load configurations');
      console.error('Error loading configurations:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfiguration = async (config: InvoiceTemplateConfig) => {
    try {
      setSaving(true);
      setError(null);
      
      if (config.id) {
        await invoiceTemplateConfigService.updateConfiguration(config.id, config);
      } else {
        await invoiceTemplateConfigService.createConfiguration(config);
      }
      
      await loadConfigurations();
      setEditingConfig(null);
    } catch (err) {
      setError('Failed to save configuration');
      console.error('Error saving configuration:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteConfiguration = async (id: number) => {
    if (!confirm('Are you sure you want to delete this configuration?')) {
      return;
    }

    try {
      setSaving(true);
      setError(null);
      await invoiceTemplateConfigService.deleteConfiguration(id);
      await loadConfigurations();
    } catch (err) {
      setError('Failed to delete configuration');
      console.error('Error deleting configuration:', err);
    } finally {
      setSaving(false);
    }
  };

  const filteredConfigurations = selectedCategory === 'ALL' 
    ? configurations 
    : configurations.filter(config => config.category === selectedCategory);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-lg">Loading configurations...</div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Invoice Template Configuration</h1>
        {onClose && (
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            ✕ Close
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Category Filter */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Filter by Category:
        </label>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {categories.map(category => (
            <option key={category} value={category}>
              {category === 'ALL' ? 'All Categories' : category}
            </option>
          ))}
        </select>
      </div>

      {/* Add New Configuration Button */}
      <div className="mb-6">
        <button
          onClick={() => setEditingConfig({
            configKey: '',
            configValue: '',
            configType: 'TEXT',
            category: 'COMPANY',
            description: '',
            isActive: true,
            displayOrder: configurations.length + 1
          })}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add New Configuration
        </button>
      </div>

      {/* Configurations Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Key
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Value
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredConfigurations.map((config) => (
              <tr key={config.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {config.configKey}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                  {config.configValue}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    {config.category}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                  {config.description}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => setEditingConfig(config)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => config.id && handleDeleteConfiguration(config.id)}
                    className="text-red-600 hover:text-red-900"
                    disabled={saving}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredConfigurations.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No configurations found for the selected category.
          </div>
        )}
      </div>

      {/* Edit Configuration Modal */}
      {editingConfig && (
        <ConfigurationEditModal
          config={editingConfig}
          onSave={handleSaveConfiguration}
          onCancel={() => setEditingConfig(null)}
          saving={saving}
        />
      )}
    </div>
  );
};

interface ConfigurationEditModalProps {
  config: InvoiceTemplateConfig;
  onSave: (config: InvoiceTemplateConfig) => void;
  onCancel: () => void;
  saving: boolean;
}

const ConfigurationEditModal: React.FC<ConfigurationEditModalProps> = ({
  config,
  onSave,
  onCancel,
  saving
}) => {
  const [formData, setFormData] = useState<InvoiceTemplateConfig>(config);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">
          {config.id ? 'Edit Configuration' : 'Add New Configuration'}
        </h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Configuration Key
            </label>
            <input
              type="text"
              name="configKey"
              value={formData.configKey}
              onChange={handleChange}
              required
              disabled={!!config.id} // Don't allow editing key for existing configs
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Configuration Value
            </label>
            <textarea
              name="configValue"
              value={formData.configValue}
              onChange={handleChange}
              required
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="COMPANY">Company</option>
              <option value="BANK">Bank</option>
              <option value="TEMPLATE">Template</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <input
              type="text"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              name="isActive"
              checked={formData.isActive}
              onChange={handleChange}
              className="mr-2"
            />
            <label className="text-sm font-medium text-gray-700">
              Active
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default InvoiceTemplateConfigManager;
