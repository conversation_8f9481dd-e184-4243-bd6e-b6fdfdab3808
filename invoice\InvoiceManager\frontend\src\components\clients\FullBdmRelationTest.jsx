import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  Code,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  useToast,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge
} from '@chakra-ui/react';
import BdmDetails from '../BdmDetails';

const FullBdmRelationTest = () => {
  const toast = useToast();
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch all clients
  useEffect(() => {
    const fetchClients = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Create basic auth header if needed
        const authHeader = 'Basic ' + btoa('admin:admin123');
        
        const response = await fetch('{import.meta.env.VITE_API_URL}/clients', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch clients: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Clients fetched:', data);
        setClients(data);
      } catch (err) {
        console.error('Error fetching clients:', err);
        setError(err.message);
        
        toast({
          title: 'Error fetching clients',
          description: err.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchClients();
  }, [toast]);

  // Fetch client details
  const fetchClientDetails = async (clientId) => {
    try {
      setLoading(true);
      setError(null);
      
      // Create basic auth header if needed
      const authHeader = 'Basic ' + btoa('admin:admin123');
      
      const response = await fetch(`{import.meta.env.VITE_API_URL}/clients/${clientId}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': authHeader
        },
        credentials: 'include'
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch client details: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Client details fetched:', data);
      setSelectedClient(data);
      
      // Check if the client has a complete BDM object
      if (data.bdm) {
        console.log('Client has complete BDM object:', data.bdm);
      } else {
        console.warn('Client does not have a complete BDM object, only bdmId and bdmName');
      }
    } catch (err) {
      console.error('Error fetching client details:', err);
      setError(err.message);
      
      toast({
        title: 'Error fetching client details',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  };

  const handleViewClient = (clientId) => {
    fetchClientDetails(clientId);
  };

  const handleBack = () => {
    setSelectedClient(null);
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">Full BDM Relationship Test</Heading>
          <Text mt={2} color="gray.600">
            This page demonstrates how to display the complete BDM data in the client response.
          </Text>
        </Box>
        
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle mr={2}>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {loading ? (
          <Box textAlign="center" py={10}>
            <Spinner size="xl" />
            <Text mt={4}>Loading data...</Text>
          </Box>
        ) : selectedClient ? (
          <Card>
            <CardHeader>
              <HStack justify="space-between">
                <Heading size="md">Client Details: {selectedClient.name}</Heading>
                <Button size="sm" onClick={handleBack}>Back to List</Button>
              </HStack>
            </CardHeader>
            <CardBody>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                {/* Client Information */}
                <Box>
                  <Heading size="sm" mb={3}>Client Information</Heading>
                  <VStack align="stretch" spacing={2}>
                    <HStack>
                      <Text fontWeight="bold" width="150px">ID:</Text>
                      <Text>{selectedClient.id}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">Name:</Text>
                      <Text>{selectedClient.name}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">Email:</Text>
                      <Text>{selectedClient.email}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">Phone:</Text>
                      <Text>{selectedClient.phone}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">Contact Person:</Text>
                      <Text>{selectedClient.contactPerson}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">BDM ID:</Text>
                      <Text>{selectedClient.bdmId || 'None'}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="bold" width="150px">BDM Name:</Text>
                      <Text>{selectedClient.bdmName || 'None'}</Text>
                    </HStack>
                  </VStack>
                </Box>
                
                {/* BDM Information */}
                <Box>
                  <Heading size="sm" mb={3}>BDM Information</Heading>
                  {selectedClient.bdm ? (
                    <BdmDetails bdm={selectedClient.bdm} showHeading={false} />
                  ) : selectedClient.bdmId ? (
                    <Alert status="warning">
                      <AlertIcon />
                      <AlertTitle>Partial BDM Data</AlertTitle>
                      <AlertDescription>
                        Only bdmId ({selectedClient.bdmId}) and bdmName ({selectedClient.bdmName}) are available.
                        The complete BDM object is not included in the response.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <Alert status="info">
                      <AlertIcon />
                      <AlertTitle>No BDM Assigned</AlertTitle>
                      <AlertDescription>
                        This client does not have a BDM assigned.
                      </AlertDescription>
                    </Alert>
                  )}
                </Box>
              </SimpleGrid>
              
              <Divider my={6} />
              
              <Heading size="sm" mb={3}>Raw Client Data</Heading>
              <Box overflowX="auto" maxH="300px" overflowY="auto">
                <Code p={2} display="block" whiteSpace="pre">
                  {JSON.stringify(selectedClient, null, 2)}
                </Code>
              </Box>
            </CardBody>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <Heading size="md">Client List</Heading>
            </CardHeader>
            <CardBody>
              {clients.length === 0 ? (
                <Alert status="info">
                  <AlertIcon />
                  <AlertTitle>No Clients</AlertTitle>
                  <AlertDescription>
                    There are no clients in the system.
                  </AlertDescription>
                </Alert>
              ) : (
                <Box overflowX="auto">
                  <Table variant="simple">
                    <Thead>
                      <Tr>
                        <Th>ID</Th>
                        <Th>Name</Th>
                        <Th>Contact Person</Th>
                        <Th>BDM</Th>
                        <Th>Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {clients.map(client => (
                        <Tr key={client.id}>
                          <Td>{client.id}</Td>
                          <Td fontWeight="medium">{client.name}</Td>
                          <Td>{client.contactPerson}</Td>
                          <Td>
                            {client.bdmName ? (
                              <Badge colorScheme="green">{client.bdmName}</Badge>
                            ) : (
                              <Badge colorScheme="gray">No BDM</Badge>
                            )}
                          </Td>
                          <Td>
                            <Button 
                              size="sm" 
                              colorScheme="blue" 
                              onClick={() => handleViewClient(client.id)}
                            >
                              View Details
                            </Button>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              )}
            </CardBody>
          </Card>
        )}
      </VStack>
    </Container>
  );
};

export default FullBdmRelationTest;
