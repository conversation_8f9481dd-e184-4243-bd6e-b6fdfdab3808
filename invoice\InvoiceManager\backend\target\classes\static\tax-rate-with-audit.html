<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Rate with Audit Fields</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
            display: none;
        }
        .code-block {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .audit-fields {
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Tax Rate with Audit Fields</h1>
    
    <div class="form-group">
        <label for="taxTypeId">Tax Type ID:</label>
        <input type="number" id="taxTypeId" value="1">
    </div>
    
    <div class="form-group">
        <label for="rate">Rate (%):</label>
        <input type="number" id="rate" step="0.01" value="18.0">
    </div>
    
    <div class="form-group">
        <label for="effectiveFrom">Effective From:</label>
        <input type="date" id="effectiveFrom" value="2025-04-17">
    </div>
    
    <div class="form-group">
        <label for="effectiveTo">Effective To:</label>
        <input type="date" id="effectiveTo" value="2025-06-22">
    </div>
    
    <button onclick="createTaxRate()">Create Tax Rate</button>
    <button onclick="getAllTaxRates()">Get All Tax Rates</button>
    <button onclick="getExampleResponse()">Show Example Response</button>
    
    <div class="result" id="apiResult">
        <h2>API Response:</h2>
        <pre id="apiResponse"></pre>
    </div>
    
    <div class="result" id="tableResult">
        <h2>Tax Rates:</h2>
        <table id="taxRatesTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Tax Type</th>
                    <th>Rate</th>
                    <th>Effective From</th>
                    <th>Effective To</th>
                    <th>Created At</th>
                    <th>Updated At</th>
                </tr>
            </thead>
            <tbody id="taxRatesBody">
            </tbody>
        </table>
    </div>
    
    <div class="code-block">
        <h2>Curl Command:</h2>
        <pre id="curlCommand">curl -X POST "http://localhost:8081/api/tax-rates" \
-H "Content-Type: application/json" \
-d "{\"taxTypeId\": 1, \"rate\": 18.0, \"effectiveFrom\": \"2025-04-17\", \"effectiveTo\": \"2025-06-22\"}"</pre>
    </div>
    
    <script>
        async function createTaxRate() {
            const taxTypeId = parseInt(document.getElementById('taxTypeId').value);
            const rate = parseFloat(document.getElementById('rate').value);
            const effectiveFrom = document.getElementById('effectiveFrom').value;
            const effectiveTo = document.getElementById('effectiveTo').value;
            
            const payload = {
                taxTypeId: taxTypeId,
                rate: rate,
                effectiveFrom: effectiveFrom,
                effectiveTo: effectiveTo
            };
            
            try {
                const response = await fetch('/api/tax-rates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                document.getElementById('apiResult').style.display = 'block';
                document.getElementById('tableResult').style.display = 'none';
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
                document.getElementById('tableResult').style.display = 'none';
            }
        }
        
        async function getAllTaxRates() {
            try {
                const response = await fetch('/api/tax-rates');
                const data = await response.json();
                
                // Display in table format
                const tableBody = document.getElementById('taxRatesBody');
                tableBody.innerHTML = '';
                
                data.forEach(taxRate => {
                    const row = document.createElement('tr');
                    
                    // ID
                    const idCell = document.createElement('td');
                    idCell.textContent = taxRate.id;
                    row.appendChild(idCell);
                    
                    // Tax Type
                    const taxTypeCell = document.createElement('td');
                    taxTypeCell.textContent = taxRate.taxType ? taxRate.taxType.taxType : '';
                    row.appendChild(taxTypeCell);
                    
                    // Rate
                    const rateCell = document.createElement('td');
                    rateCell.textContent = taxRate.rate;
                    row.appendChild(rateCell);
                    
                    // Effective From
                    const effectiveFromCell = document.createElement('td');
                    effectiveFromCell.textContent = taxRate.effectiveFrom;
                    row.appendChild(effectiveFromCell);
                    
                    // Effective To
                    const effectiveToCell = document.createElement('td');
                    effectiveToCell.textContent = taxRate.effectiveTo;
                    row.appendChild(effectiveToCell);
                    
                    // Created At
                    const createdAtCell = document.createElement('td');
                    createdAtCell.textContent = taxRate.created_at;
                    row.appendChild(createdAtCell);
                    
                    // Updated At
                    const updatedAtCell = document.createElement('td');
                    updatedAtCell.textContent = taxRate.updated_at;
                    row.appendChild(updatedAtCell);
                    
                    tableBody.appendChild(row);
                });
                
                document.getElementById('tableResult').style.display = 'block';
                document.getElementById('apiResult').style.display = 'none';
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
                document.getElementById('tableResult').style.display = 'none';
            }
        }
        
        async function getExampleResponse() {
            try {
                const response = await fetch('/api/examples/tax-rate-response');
                const data = await response.json();
                document.getElementById('apiResponse').textContent = JSON.stringify(data, null, 2);
                document.getElementById('apiResult').style.display = 'block';
                document.getElementById('tableResult').style.display = 'none';
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error: ' + error.message;
                document.getElementById('apiResult').style.display = 'block';
                document.getElementById('tableResult').style.display = 'none';
            }
        }
    </script>
</body>
</html>
