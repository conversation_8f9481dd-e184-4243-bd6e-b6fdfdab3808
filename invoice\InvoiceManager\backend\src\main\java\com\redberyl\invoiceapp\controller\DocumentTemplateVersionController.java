package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.DocumentTemplateVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController

@Tag(name = "Document Template Version", description = "Document Template Version management API")
public class DocumentTemplateVersionController {

        @Autowired
        private DocumentTemplateVersionService documentTemplateVersionService;

        @GetMapping("/document-template-versions/getAll")
        @Operation(summary = "Get all document template versions", description = "Get all document template versions")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template versions found"),
                        @ApiResponse(responseCode = "204", description = "No document template versions found", content = @Content)
        })
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<List<DocumentTemplateVersionDto>> getAllDocumentTemplateVersions() {
                try {
                        List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                                        .getAllDocumentTemplateVersions();
                        return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
                } catch (NoContentException e) {
                        return ResponseEntity.noContent().build();
                }
        }

        @GetMapping("/document-template-versions/getById/{id}")
        @Operation(summary = "Get document template version by ID", description = "Get document template version by ID")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template version found"),
                        @ApiResponse(responseCode = "404", description = "Document template version not found"),
                        @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
        })
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> getDocumentTemplateVersionById(@PathVariable Long id) {
                DocumentTemplateVersionDto documentTemplateVersion = documentTemplateVersionService
                                .getDocumentTemplateVersionById(id);
                return new ResponseEntity<>(documentTemplateVersion, HttpStatus.OK);
        }

        @GetMapping("/document-template-versions/getByTemplateId/{templateId}")
        @Operation(summary = "Get document template versions by template ID", description = "Get document template versions by template ID")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template versions found"),
                        @ApiResponse(responseCode = "204", description = "No document template versions found for this template"),
                        @ApiResponse(responseCode = "404", description = "Template not found"),
                        @ApiResponse(responseCode = "400", description = "Invalid template ID supplied")
        })
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<List<DocumentTemplateVersionDto>> getDocumentTemplateVersionsByTemplateId(
                        @PathVariable Long templateId) {
                try {
                        List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                                        .getDocumentTemplateVersionsByTemplateId(templateId);
                        return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
                } catch (NoContentException e) {
                        return ResponseEntity.noContent().build();
                }
        }

        @GetMapping("/document-template-versions/getByTemplateIdAndVersionNumber/{templateId}/{versionNumber}")
        @Operation(summary = "Get document template version by template ID and version number", description = "Get document template version by template ID and version number")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template version found"),
                        @ApiResponse(responseCode = "404", description = "Document template version not found or template not found"),
                        @ApiResponse(responseCode = "400", description = "Invalid template ID or version number supplied")
        })
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> getDocumentTemplateVersionByTemplateIdAndVersionNumber(
                        @PathVariable Long templateId, @PathVariable Integer versionNumber) {
                return documentTemplateVersionService
                                .getDocumentTemplateVersionByTemplateIdAndVersionNumber(templateId, versionNumber)
                                .map(version -> new ResponseEntity<>(version, HttpStatus.OK))
                                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
        }

        @GetMapping("/document-template-versions/getActiveByTemplateId/{templateId}")
        @Operation(summary = "Get active document template versions by template ID", description = "Get active document template versions by template ID")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Active document template versions found"),
                        @ApiResponse(responseCode = "204", description = "No active document template versions found for this template"),
                        @ApiResponse(responseCode = "404", description = "Template not found"),
                        @ApiResponse(responseCode = "400", description = "Invalid template ID supplied")
        })
        @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<List<DocumentTemplateVersionDto>> getActiveDocumentTemplateVersionsByTemplateId(
                        @PathVariable Long templateId) {
                try {
                        List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                                        .getActiveDocumentTemplateVersionsByTemplateId(templateId);
                        return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
                } catch (NoContentException e) {
                        return ResponseEntity.noContent().build();
                }
        }

        @PostMapping("/document-template-versions/create")
        @Operation(summary = "Create document template version", description = "Create document template version. The templateId field supports flexible formats:\n\n" +
                "1. Simple string: \"templateId\": \"1\"\n" +
                "2. Simple number: \"templateId\": 1\n" +
                "3. Nested object: \"templateId\": {\"id\": 1, ...}")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "201", description = "Document template version created successfully"),
                        @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
                        @ApiResponse(responseCode = "700", description = "Null constraint violation"),
                        @ApiResponse(responseCode = "701", description = "Unique constraint violation")
        })
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> createDocumentTemplateVersion(
                        @Valid @RequestBody DocumentTemplateVersionDto documentTemplateVersionDto) {
                DocumentTemplateVersionDto createdDocumentTemplateVersion = documentTemplateVersionService
                                .createDocumentTemplateVersion(documentTemplateVersionDto);
                return new ResponseEntity<>(createdDocumentTemplateVersion, HttpStatus.CREATED);
        }

        @PutMapping("/document-template-versions/update/{id}")
        @Operation(summary = "Update document template version", description = "Update document template version")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template version updated successfully"),
                        @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
                        @ApiResponse(responseCode = "404", description = "Document template version not found"),
                        @ApiResponse(responseCode = "700", description = "Null constraint violation"),
                        @ApiResponse(responseCode = "701", description = "Unique constraint violation")
        })
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> updateDocumentTemplateVersion(@PathVariable Long id,
                        @Valid @RequestBody DocumentTemplateVersionDto documentTemplateVersionDto) {
                DocumentTemplateVersionDto updatedDocumentTemplateVersion = documentTemplateVersionService
                                .updateDocumentTemplateVersion(id, documentTemplateVersionDto);
                return new ResponseEntity<>(updatedDocumentTemplateVersion, HttpStatus.OK);
        }

        @PutMapping("/document-template-versions/activate/{id}")
        @Operation(summary = "Activate document template version", description = "Activate document template version")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template version activated successfully"),
                        @ApiResponse(responseCode = "404", description = "Document template version not found"),
                        @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
        })
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> activateDocumentTemplateVersion(@PathVariable Long id) {
                DocumentTemplateVersionDto activatedDocumentTemplateVersion = documentTemplateVersionService
                                .activateDocumentTemplateVersion(id);
                return new ResponseEntity<>(activatedDocumentTemplateVersion, HttpStatus.OK);
        }

        @PutMapping("/document-template-versions/deactivate/{id}")
        @Operation(summary = "Deactivate document template version", description = "Deactivate document template version")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "200", description = "Document template version deactivated successfully"),
                        @ApiResponse(responseCode = "404", description = "Document template version not found"),
                        @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
        })
        @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
        public ResponseEntity<DocumentTemplateVersionDto> deactivateDocumentTemplateVersion(@PathVariable Long id) {
                DocumentTemplateVersionDto deactivatedDocumentTemplateVersion = documentTemplateVersionService
                                .deactivateDocumentTemplateVersion(id);
                return new ResponseEntity<>(deactivatedDocumentTemplateVersion, HttpStatus.OK);
        }

        @DeleteMapping("/document-template-versions/deleteById/{id}")
        @Operation(summary = "Delete document template version", description = "Delete document template version")
        @ApiResponses(value = {
                        @ApiResponse(responseCode = "204", description = "Document template version deleted successfully"),
                        @ApiResponse(responseCode = "400", description = "Invalid ID supplied or document template version is referenced by other entities"),
                        @ApiResponse(responseCode = "404", description = "Document template version not found")
        })
        @PreAuthorize("hasRole('ADMIN')")
        public ResponseEntity<Void> deleteDocumentTemplateVersion(@PathVariable Long id) {
                documentTemplateVersionService.deleteDocumentTemplateVersion(id);
                return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
}
