package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.TaxTypeDto;
import com.redberyl.invoiceapp.entity.TaxType;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.TaxTypeRepository;
import com.redberyl.invoiceapp.service.TaxTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TaxTypeServiceImpl implements TaxTypeService {

    @Autowired
    private TaxTypeRepository taxTypeRepository;

    @Override
    public List<TaxTypeDto> getAllTaxTypes() {
        List<TaxType> taxTypes = taxTypeRepository.findAll();
        if (taxTypes.isEmpty()) {
            throw new NoContentException("No tax types found");
        }
        return taxTypes.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public TaxTypeDto getTaxTypeById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Tax type ID cannot be null");
        }

        TaxType taxType = taxTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Type not found with id: " + id));
        return convertToDto(taxType);
    }

    @Override
    public TaxTypeDto getTaxTypeByType(String taxType) {
        if (!StringUtils.hasText(taxType)) {
            throw new NullConstraintViolationException("taxType", "Tax type cannot be empty");
        }

        TaxType type = taxTypeRepository.findByTaxType(taxType)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Type not found with type: " + taxType));
        return convertToDto(type);
    }

    private void validateTaxTypeDto(TaxTypeDto taxTypeDto) {
        if (taxTypeDto == null) {
            throw new NullConstraintViolationException("taxTypeDto", "Tax type data cannot be null");
        }

        if (!StringUtils.hasText(taxTypeDto.getTaxType())) {
            throw new NullConstraintViolationException("taxType", "Tax type cannot be empty");
        }

        // Check for duplicate tax type if it's a new tax type
        if (taxTypeDto.getId() == null &&
                taxTypeRepository.findByTaxType(taxTypeDto.getTaxType()).isPresent()) {
            throw new UniqueConstraintViolationException("taxType",
                    "Tax type already exists: " + taxTypeDto.getTaxType());
        }
    }

    @Override
    @Transactional
    public TaxTypeDto createTaxType(TaxTypeDto taxTypeDto) {
        validateTaxTypeDto(taxTypeDto);

        try {
            TaxType taxType = convertToEntity(taxTypeDto);
            TaxType savedTaxType = taxTypeRepository.save(taxType);
            return convertToDto(savedTaxType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("taxType", "Tax type already exists");
            } else {
                throw new CustomException("Error creating tax type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating tax type", e);
        }
    }

    @Override
    @Transactional
    public TaxTypeDto updateTaxType(Long id, TaxTypeDto taxTypeDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Tax type ID cannot be null");
        }

        if (taxTypeDto == null) {
            throw new NullConstraintViolationException("taxTypeDto", "Tax type data cannot be null");
        }

        TaxType existingTaxType = taxTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Type not found with id: " + id));

        // Check for duplicate tax type if it's being changed
        if (StringUtils.hasText(taxTypeDto.getTaxType()) &&
                !taxTypeDto.getTaxType().equals(existingTaxType.getTaxType()) &&
                taxTypeRepository.findByTaxType(taxTypeDto.getTaxType()).isPresent()) {
            throw new UniqueConstraintViolationException("taxType",
                    "Tax type already exists: " + taxTypeDto.getTaxType());
        }

        try {
            if (StringUtils.hasText(taxTypeDto.getTaxType())) {
                existingTaxType.setTaxType(taxTypeDto.getTaxType());
            }

            if (StringUtils.hasText(taxTypeDto.getTaxTypeDescription())) {
                existingTaxType.setTaxTypeDescription(taxTypeDto.getTaxTypeDescription());
            }

            TaxType updatedTaxType = taxTypeRepository.save(existingTaxType);
            return convertToDto(updatedTaxType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("taxType", "Tax type already exists");
            } else {
                throw new CustomException("Error updating tax type: " + e.getMessage(), e);
            }
        } catch (ResourceNotFoundException | NullConstraintViolationException | UniqueConstraintViolationException e) {
            throw e;
        } catch (Exception e) {
            throw new CustomException("Error updating tax type", e);
        }
    }

    @Override
    @Transactional
    public void deleteTaxType(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Tax type ID cannot be null");
        }

        TaxType taxType = taxTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax Type not found with id: " + id));

        try {
            // Clear the tax rates collection to avoid orphan issues
            taxType.getTaxRates().clear();

            taxTypeRepository.delete(taxType);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete tax type because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting tax type: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting tax type", e);
        }
    }

    private TaxTypeDto convertToDto(TaxType taxType) {
        TaxTypeDto dto = TaxTypeDto.builder()
                .id(taxType.getId())
                .taxType(taxType.getTaxType())
                .taxTypeDescription(taxType.getTaxTypeDescription())
                .build();

        // Set the audit fields
        dto.setCreatedAt(taxType.getCreatedAt());
        dto.setUpdatedAt(taxType.getModifiedAt());

        return dto;
    }

    private TaxType convertToEntity(TaxTypeDto taxTypeDto) {
        // If updating an existing tax type, fetch it first to preserve relationships
        if (taxTypeDto.getId() != null) {
            TaxType existingTaxType = taxTypeRepository.findById(taxTypeDto.getId())
                    .orElse(new TaxType());

            existingTaxType.setTaxType(taxTypeDto.getTaxType());
            existingTaxType.setTaxTypeDescription(taxTypeDto.getTaxTypeDescription());

            return existingTaxType;
        } else {
            // For new tax types
            return TaxType.builder()
                    .id(taxTypeDto.getId())
                    .taxType(taxTypeDto.getTaxType())
                    .taxTypeDescription(taxTypeDto.getTaxTypeDescription())
                    .taxRates(new java.util.HashSet<>())
                    .build();
        }
    }
}
