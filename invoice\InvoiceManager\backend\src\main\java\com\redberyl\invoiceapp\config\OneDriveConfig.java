package com.redberyl.invoiceapp.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * Configuration class for OneDrive integration
 */
@Configuration
@Component
public class OneDriveConfig {

    @Value("${onedrive.client.id}")
    private String clientId;

    @Value("${onedrive.tenant.id}")
    private String tenantId;

    @Value("${onedrive.client.secret}")
    private String clientSecret;

    @Value("${onedrive.redirect.uri}")
    private String redirectUri;

    @Value("${onedrive.scope}")
    private String scope;

    @Value("${onedrive.base.path}")
    private String basePath;

    // Getters
    public String getClientId() {
        return clientId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public String getScope() {
        return scope;
    }

    public String getBasePath() {
        return basePath;
    }

    public String getAuthority() {
        return "https://login.microsoftonline.com/" + tenantId;
    }

    public String getGraphEndpoint() {
        return "https://graph.microsoft.com/v1.0";
    }
}
