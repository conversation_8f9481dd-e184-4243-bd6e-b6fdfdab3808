package com.redberyl.invoiceapp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.Collections;

@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins(
                    "http://localhost:3060",
                    "http://127.0.0.1:3060",
                    "http://************:3060"
                ) // Allow specific origins for development
                .allowedOriginPatterns("http://192.168.*.*:3060") // Allow pattern for local network IPs
                .allowedMethods("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD")
                .allowedHeaders("*") // Allow all headers
                .exposedHeaders("Access-Control-Allow-Origin", "Access-Control-Allow-Methods",
                               "Access-Control-Allow-Headers", "Access-Control-Max-Age",
                               "Authorization", "X-Auth-Token")
                .allowCredentials(true) // Set to true since we're using the proxy
                .maxAge(3600);
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();

        // Allow specific origins for development
        config.addAllowedOrigin("http://localhost:3060");
        config.addAllowedOrigin("http://127.0.0.1:3060");
        config.addAllowedOrigin("http://************:3060");
        // Also add a pattern for any other local network IPs
        config.addAllowedOriginPattern("http://192.168.*.*:3060");

        // Allow all common HTTP methods
        config.setAllowedMethods(Arrays.asList(
                "GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"));

        // Allow all headers
        config.addAllowedHeader("*");

        // Expose headers that the client might need to access
        config.setExposedHeaders(Arrays.asList(
                "Access-Control-Allow-Origin",
                "Access-Control-Allow-Methods",
                "Access-Control-Allow-Headers",
                "Access-Control-Max-Age",
                "Authorization",
                "X-Auth-Token"));

        // Set allowCredentials to true since we're using the proxy
        // The proxy will handle the CORS issues
        config.setAllowCredentials(true);

        // How long the response from a pre-flight request can be cached (in seconds)
        config.setMaxAge(3600L);

        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
