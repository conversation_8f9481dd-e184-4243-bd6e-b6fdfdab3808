package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ApiResponseDto;
import com.redberyl.invoiceapp.dto.EntityTableInfoDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controller to provide information about all entity tables in the database
 */
@RestController
@RequestMapping("/entity-tables")
@Tag(name = "Entity Tables", description = "API to get information about all entity tables in the database")
public class EntityTableController {

    @Autowired
    private DataSource dataSource;

    /**
     * Get all entity tables in the database
     */
    @GetMapping
    @Operation(summary = "Get all entity tables", description = "Get information about all entity tables in the database")
    public ResponseEntity<ApiResponseDto<List<EntityTableInfoDto>>> getAllEntityTables() {
        try {
            List<EntityTableInfoDto> tables = new ArrayList<>();
            
            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                // Get all tables
                ResultSet rs = metaData.getTables(null, "PUBLIC", null, new String[]{"TABLE"});
                
                while (rs.next()) {
                    String tableName = rs.getString("TABLE_NAME");
                    
                    // Skip system tables
                    if (tableName.startsWith("DATABASECHANGELOG") || 
                        tableName.startsWith("FLYWAY_") ||
                        tableName.equals("DUAL")) {
                        continue;
                    }
                    
                    EntityTableInfoDto tableInfo = new EntityTableInfoDto();
                    tableInfo.setTableName(tableName);
                    
                    // Get columns for this table
                    List<Map<String, Object>> columns = new ArrayList<>();
                    ResultSet columnsRs = metaData.getColumns(null, "PUBLIC", tableName, null);
                    
                    while (columnsRs.next()) {
                        Map<String, Object> column = new HashMap<>();
                        column.put("name", columnsRs.getString("COLUMN_NAME"));
                        column.put("type", columnsRs.getString("TYPE_NAME"));
                        column.put("size", columnsRs.getInt("COLUMN_SIZE"));
                        column.put("nullable", columnsRs.getBoolean("IS_NULLABLE"));
                        columns.add(column);
                    }
                    
                    tableInfo.setColumns(columns);
                    
                    // Get primary keys
                    List<String> primaryKeys = new ArrayList<>();
                    ResultSet pkRs = metaData.getPrimaryKeys(null, "PUBLIC", tableName);
                    
                    while (pkRs.next()) {
                        primaryKeys.add(pkRs.getString("COLUMN_NAME"));
                    }
                    
                    tableInfo.setPrimaryKeys(primaryKeys);
                    
                    // Get foreign keys
                    List<Map<String, Object>> foreignKeys = new ArrayList<>();
                    ResultSet fkRs = metaData.getImportedKeys(null, "PUBLIC", tableName);
                    
                    while (fkRs.next()) {
                        Map<String, Object> foreignKey = new HashMap<>();
                        foreignKey.put("columnName", fkRs.getString("FKCOLUMN_NAME"));
                        foreignKey.put("referencedTable", fkRs.getString("PKTABLE_NAME"));
                        foreignKey.put("referencedColumn", fkRs.getString("PKCOLUMN_NAME"));
                        foreignKeys.add(foreignKey);
                    }
                    
                    tableInfo.setForeignKeys(foreignKeys);
                    
                    // Add the table info to the list
                    tables.add(tableInfo);
                }
            }
            
            ApiResponseDto<List<EntityTableInfoDto>> response = new ApiResponseDto<>();
            response.setSuccess(true);
            response.setMessage("Entity tables retrieved successfully");
            response.setData(tables);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            ApiResponseDto<List<EntityTableInfoDto>> response = new ApiResponseDto<>();
            response.setSuccess(false);
            response.setMessage("Error retrieving entity tables: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
