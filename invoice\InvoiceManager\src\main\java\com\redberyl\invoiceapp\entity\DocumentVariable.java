package com.redberyl.invoiceapp.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "document_variables")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentVariable extends BaseEntity {

    @Id
    @SequenceGenerator(name = "document_variable_seq", sequenceName = "document_variable_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "document_variable_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_version_id")
    private DocumentTemplateVersion version;

    @Column(name = "variable_name")
    private String variableName;

    @Column(name = "description")
    private String description;

    @Column(name = "sample_value")
    private String sampleValue;
}
