package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class ProjectDto extends BaseDto {
    private Long id;
    
    @NotNull(message = "Client ID is required")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long clientId;
    
    // Include the full client object
    private ClientDto client;
    
    @NotBlank(message = "Project name is required")
    private String name;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long hsnCodeId;
    
    // Include the full HSN code object
    private HsnCodeDto hsnCode;
    
    private String description;
    
    @Email(message = "Invalid email format")
    private String email;
    
    private String phone;
    private String gstNumber;
    private String billingAddress;
    private String shippingAddress;
    private String engagementCode;
    private String clientPartnerName;
    
    @Email(message = "Invalid client partner email format")
    private String clientPartnerEmail;
    
    private String clientPartnerPhone;
    
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long bdmId;
    
    // Include the full BDM object
    private BdmDto bdm;
    
    private BigDecimal commissionPercentage;
    private BigDecimal commissionAmount;
}
