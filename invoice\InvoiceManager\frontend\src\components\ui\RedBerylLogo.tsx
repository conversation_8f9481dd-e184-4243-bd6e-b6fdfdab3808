import React from 'react';

interface RedBerylLogoProps {
  width?: number;
  height?: number;
  className?: string;
  useImage?: boolean; // Option to use actual image file instead of SVG
  imageUrl?: string; // URL to the actual logo image
}

const RedBerylLogo: React.FC<RedBerylLogoProps> = ({
  width = 300,
  height = 120,
  className = "",
  useImage = false,
  imageUrl = "/assets/redberyl-logo.png" // Default path for logo image
}) => {

  // If useImage is true, render the actual image
  if (useImage) {
    return (
      <img
        src={imageUrl}
        alt="RedBeryl Tech Solutions"
        width={width}
        height={height}
        className={className}
        style={{
          objectFit: 'contain',
          maxWidth: '100%',
          height: 'auto'
        }}
        onError={(e) => {
          // Fallback to SVG if image fails to load
          console.warn('RedBeryl logo image failed to load, falling back to SVG');
          e.currentTarget.style.display = 'none';
        }}
      />
    );
  }
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 600 240"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Background */}
      <rect width="600" height="240" fill="white"/>

      {/* Cloud-like interconnected shapes representing the actual RedBeryl logo */}
      <g transform="translate(30, 60)">
        {/* Left blue cloud shape */}
        <path
          d="M0 60 C0 27, 27 0, 60 0 C93 0, 120 27, 120 60 C120 93, 93 120, 60 120 C27 120, 0 93, 0 60 Z"
          fill="#4A90E2"
          opacity="0.9"
        />

        {/* Right pink/magenta cloud shape */}
        <path
          d="M80 40 C80 18, 98 0, 120 0 C142 0, 160 18, 160 40 C160 62, 142 80, 120 80 C98 80, 80 62, 80 40 Z"
          fill="#E91E63"
          opacity="0.9"
        />

        {/* Bottom connecting cloud */}
        <path
          d="M40 80 C40 62, 54 48, 72 48 C90 48, 104 62, 104 80 C104 98, 90 112, 72 112 C54 112, 40 98, 40 80 Z"
          fill="#8E44AD"
          opacity="0.8"
        />

        {/* Small connecting dots/elements */}
        <circle cx="90" cy="60" r="6" fill="#3498DB"/>
        <circle cx="70" cy="70" r="4" fill="#E74C3C"/>
        <circle cx="110" cy="50" r="3" fill="#F39C12"/>
      </g>

      {/* RedBeryl Text */}
      <g transform="translate(220, 60)">
        {/* Red text */}
        <text x="0" y="45" fontFamily="Arial, sans-serif" fontSize="48" fontWeight="bold" fill="#E91E63">Red</text>

        {/* Beryl text */}
        <text x="105" y="45" fontFamily="Arial, sans-serif" fontSize="48" fontWeight="bold" fill="#1565C0">Beryl</text>

        {/* TECH SOLUTIONS */}
        <text x="0" y="75" fontFamily="Arial, sans-serif" fontSize="18" fontWeight="600" fill="#666" letterSpacing="3px">TECH SOLUTIONS</text>

        {/* Tagline */}
        <text x="0" y="100" fontFamily="Arial, sans-serif" fontSize="14" fill="#888" fontStyle="italic">Integrates Business With Technology</text>
      </g>
    </svg>
  );
};

export default RedBerylLogo;
