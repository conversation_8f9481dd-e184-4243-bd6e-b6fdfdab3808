package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.DealDto;
import com.redberyl.invoiceapp.dto.LeadDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Deal;
import com.redberyl.invoiceapp.entity.Lead;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.DealRepository;
import com.redberyl.invoiceapp.repository.LeadRepository;
import com.redberyl.invoiceapp.service.DealService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DealServiceImpl implements DealService {

    @Autowired
    private DealRepository dealRepository;

    @Autowired
    private LeadRepository leadRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Override
    public List<DealDto> getAllDeals() {
        List<Deal> deals = dealRepository.findAll();
        if (deals.isEmpty()) {
            throw new NoContentException("No deals found");
        }
        return deals.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public DealDto getDealById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Deal ID cannot be null");
        }

        Deal deal = dealRepository.findByIdWithLeadAndClient(id)
                .orElseThrow(() -> new ResourceNotFoundException("Deal not found with id: " + id));
        return convertToDto(deal);
    }

    @Override
    public List<DealDto> getDealsByLeadId(Long leadId) {
        if (leadId == null) {
            throw new NullConstraintViolationException("leadId", "Lead ID cannot be null");
        }

        // Check if lead exists
        if (!leadRepository.existsById(leadId)) {
            throw new ResourceNotFoundException("Lead not found with id: " + leadId);
        }

        List<Deal> deals = dealRepository.findByLeadId(leadId);
        if (deals.isEmpty()) {
            throw new NoContentException("No deals found for lead with id: " + leadId);
        }

        return deals.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<DealDto> getDealsByClientId(Long clientId) {
        if (clientId == null) {
            throw new NullConstraintViolationException("clientId", "Client ID cannot be null");
        }

        // Check if client exists
        if (!clientRepository.existsById(clientId)) {
            throw new ResourceNotFoundException("Client not found with id: " + clientId);
        }

        List<Deal> deals = dealRepository.findByClientId(clientId);
        if (deals.isEmpty()) {
            throw new NoContentException("No deals found for client with id: " + clientId);
        }

        return deals.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<DealDto> getDealsByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new NullConstraintViolationException("status", "Status cannot be empty");
        }

        List<Deal> deals = dealRepository.findByStatus(status);
        if (deals.isEmpty()) {
            throw new NoContentException("No deals found with status: " + status);
        }

        return deals.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateDealDto(DealDto dealDto) {
        if (dealDto == null) {
            throw new NullConstraintViolationException("dealDto", "Deal data cannot be null");
        }

        if (!StringUtils.hasText(dealDto.getProjectName())) {
            throw new NullConstraintViolationException("projectName", "Project name cannot be empty");
        }

        // At least one of lead or client must be provided
        if (dealDto.getLeadId() == null && dealDto.getClientId() == null) {
            throw new NullConstraintViolationException("relationship",
                    "At least one of leadId or clientId must be provided");
        }

        // Validate lead ID if provided
        if (dealDto.getLeadId() != null && !leadRepository.existsById(dealDto.getLeadId())) {
            throw new ForeignKeyViolationException("leadId",
                    "Lead not found with id: " + dealDto.getLeadId());
        }

        // Validate client ID if provided
        if (dealDto.getClientId() != null && !clientRepository.existsById(dealDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + dealDto.getClientId());
        }
    }

    @Override
    @Transactional
    public DealDto createDeal(DealDto dealDto) {
        validateDealDto(dealDto);

        try {
            Deal deal = convertToEntity(dealDto);
            Deal savedDeal = dealRepository.save(deal);
            // Fetch the saved deal with lead and client to ensure they are loaded
            return getDealById(savedDeal.getId());
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field", "Deal with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating deal: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating deal", e);
        }
    }

    @Override
    @Transactional
    public DealDto updateDeal(Long id, DealDto dealDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Deal ID cannot be null");
        }

        Deal existingDeal = dealRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Deal not found with id: " + id));

        // Validate the DTO fields that are being updated
        if (dealDto.getLeadId() != null && !leadRepository.existsById(dealDto.getLeadId())) {
            throw new ForeignKeyViolationException("leadId",
                    "Lead not found with id: " + dealDto.getLeadId());
        }

        if (dealDto.getClientId() != null && !clientRepository.existsById(dealDto.getClientId())) {
            throw new ForeignKeyViolationException("clientId",
                    "Client not found with id: " + dealDto.getClientId());
        }

        try {
            updateDealFromDto(existingDeal, dealDto);
            Deal updatedDeal = dealRepository.save(existingDeal);
            // Fetch the updated deal with lead and client to ensure they are loaded
            return getDealById(updatedDeal.getId());
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("field", "Deal with these details already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating deal: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating deal", e);
        }
    }

    @Override
    @Transactional
    public void deleteDeal(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Deal ID cannot be null");
        }

        if (!dealRepository.existsById(id)) {
            throw new ResourceNotFoundException("Deal not found with id: " + id);
        }

        try {
            dealRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete deal because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting deal: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting deal", e);
        }
    }

    private DealDto convertToDto(Deal deal) {
        // Start building the DTO with common fields
        DealDto.DealDtoBuilder builder = DealDto.builder()
                .id(deal.getId())
                .projectName(deal.getProjectName())
                .valueEstimate(deal.getValueEstimate())
                .expectedClosureDate(deal.getExpectedClosureDate())
                .status(deal.getStatus())
                .notes(deal.getNotes());

        // Set lead ID and lead object if available
        if (deal.getLead() != null) {
            builder.leadId(deal.getLead().getId());

            // Create and set the lead DTO with essential fields only
            LeadDto leadDto = LeadDto.builder()
                    .id(deal.getLead().getId())
                    .name(deal.getLead().getName())
                    .email(deal.getLead().getEmail())
                    .phone(deal.getLead().getPhone())
                    .source(deal.getLead().getSource())
                    .status(deal.getLead().getStatus())
                    .build();

            // Set audit fields for lead
            leadDto.setCreatedAt(deal.getLead().getCreatedAt());
            leadDto.setUpdatedAt(deal.getLead().getModifiedAt());

            builder.lead(leadDto);
        }

        // Set client ID and client object if available
        if (deal.getClient() != null) {
            builder.clientId(deal.getClient().getId());

            // Create and set the client DTO with essential fields only
            ClientDto clientDto = ClientDto.builder()
                    .id(deal.getClient().getId())
                    .name(deal.getClient().getName())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(deal.getClient().getCreatedAt());
            clientDto.setUpdatedAt(deal.getClient().getModifiedAt());

            builder.client(clientDto);
        }

        // Build the DTO
        DealDto dto = builder.build();

        // Set the audit fields from BaseEntity
        dto.setCreatedAt(deal.getCreatedAt());
        dto.setUpdatedAt(deal.getModifiedAt());

        return dto;
    }

    private Deal convertToEntity(DealDto dealDto) {
        Deal deal = new Deal();
        deal.setId(dealDto.getId());

        updateDealFromDto(deal, dealDto);

        return deal;
    }

    private void updateDealFromDto(Deal deal, DealDto dealDto) {
        if (dealDto.getLeadId() != null) {
            Lead lead = leadRepository.findById(dealDto.getLeadId())
                    .orElseThrow(() -> new ForeignKeyViolationException("leadId",
                            "Lead not found with id: " + dealDto.getLeadId()));
            deal.setLead(lead);
        }

        if (dealDto.getClientId() != null) {
            Client client = clientRepository.findById(dealDto.getClientId())
                    .orElseThrow(() -> new ForeignKeyViolationException("clientId",
                            "Client not found with id: " + dealDto.getClientId()));
            deal.setClient(client);
        }

        if (StringUtils.hasText(dealDto.getProjectName())) {
            deal.setProjectName(dealDto.getProjectName());
        }

        deal.setValueEstimate(dealDto.getValueEstimate());
        deal.setExpectedClosureDate(dealDto.getExpectedClosureDate());
        deal.setStatus(dealDto.getStatus());
        deal.setNotes(dealDto.getNotes());
    }
}
