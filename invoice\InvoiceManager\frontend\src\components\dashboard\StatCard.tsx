
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import { ArrowUpRight, TrendingUp, TrendingDown } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
  linkTo?: string;
  onClick?: () => void;
}

const StatCard = ({
  title,
  value,
  description,
  icon,
  trend,
  className,
  linkTo,
  onClick
}: StatCardProps) => {
  const cardContent = (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-foreground transition-colors duration-300">
          {title}
        </CardTitle>
        <div className="relative">
          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center group-hover:from-primary/20 group-hover:to-primary/30 transition-all duration-300 group-hover:scale-110">
            <div className="h-5 w-5 text-primary group-hover:text-primary transition-colors duration-300">
              {icon}
            </div>
          </div>
          <div className="absolute -top-1 -right-1 h-3 w-3 bg-primary/20 rounded-full animate-pulse group-hover:bg-primary/40 transition-colors duration-300"></div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-baseline gap-2">
          <div className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent group-hover:from-primary group-hover:to-primary/80 transition-all duration-300">
            {value}
          </div>
          {trend && (
            <div className={cn(
              "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium transition-all duration-300",
              trend.isPositive
                ? "bg-green-50 text-green-700 group-hover:bg-green-100"
                : "bg-red-50 text-red-700 group-hover:bg-red-100"
            )}>
              {trend.isPositive ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              {trend.value}%
            </div>
          )}
        </div>

        {trend && (
          <p className="text-xs text-muted-foreground group-hover:text-muted-foreground/80 transition-colors duration-300">
            <span className={cn(
              "font-medium",
              trend.isPositive ? "text-green-600" : "text-red-600"
            )}>
              {trend.isPositive ? "+" : ""}{trend.value}%
            </span>
            {" "}from last month
          </p>
        )}

        {description && (
          <p className="text-xs text-muted-foreground group-hover:text-muted-foreground/80 transition-colors duration-300 leading-relaxed">
            {description}
          </p>
        )}

        {(linkTo || onClick) && (
          <div className="flex items-center justify-between pt-2 border-t border-border/50 group-hover:border-primary/20 transition-colors duration-300">
            <span className="text-xs font-medium text-primary/70 group-hover:text-primary transition-colors duration-300">
              View details
            </span>
            <ArrowUpRight className="h-3 w-3 text-primary/50 group-hover:text-primary group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-all duration-300" />
          </div>
        )}
      </CardContent>
    </>
  );

  // If linkTo is provided, wrap the card in a Link component
  if (linkTo) {
    return (
      <Link to={linkTo} className="block group">
        <Card className={cn(
          "relative overflow-hidden cursor-pointer transition-all duration-300 ease-out",
          "hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 hover:-translate-y-1",
          "bg-gradient-to-br from-card to-card/95",
          "border border-border/50 hover:border-primary/50",
          "backdrop-blur-sm",
          "group",
          className
        )}>
          {cardContent}

          {/* Animated background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none"></div>

          {/* Shimmer effect */}
          <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out bg-gradient-to-r from-transparent via-white/10 to-transparent pointer-events-none"></div>

          {/* Border glow */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none blur-sm -z-10"></div>
        </Card>
      </Link>
    );
  }

  // If onClick is provided, make the card clickable
  if (onClick) {
    return (
      <Card
        className={cn(
          "relative overflow-hidden cursor-pointer transition-all duration-300 ease-out",
          "hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 hover:-translate-y-1",
          "bg-gradient-to-br from-card to-card/95",
          "border border-border/50 hover:border-primary/50",
          "backdrop-blur-sm",
          "group",
          className
        )}
        onClick={onClick}
      >
        {cardContent}

        {/* Animated background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none"></div>

        {/* Shimmer effect */}
        <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out bg-gradient-to-r from-transparent via-white/10 to-transparent pointer-events-none"></div>

        {/* Border glow */}
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none blur-sm -z-10"></div>
      </Card>
    );
  }

  // Default case: non-clickable card
  return (
    <Card className={cn(
      "relative overflow-hidden bg-gradient-to-br from-card to-card/95 border border-border/50 backdrop-blur-sm",
      className
    )}>
      {cardContent}
    </Card>
  );
};

export default StatCard;
