export interface Spoc {
  id?: number;
  name: string;
  emailId: string;
  contactNo: string;
  createdAt?: string;
  updatedAt?: string;
}

class SpocService {
  private baseUrl = 'http://localhost:8091';

  // Helper method to get auth headers
  private getAuthHeaders(): Record<string, string> {
    // For basic auth (used in development)
    return {
      'Authorization': 'Basic ' + btoa('admin:admin123')
    };
  }

  async getAllSpocs(): Promise<Spoc[]> {
    // Import service utilities for dynamic IP configuration
    const { getServiceEndpoints } = await import('@/utils/serviceUtils');

    // Get endpoints with target IP configuration
    const endpoints = getServiceEndpoints('spocs', 'getAll');

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Trying SPOC endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...this.getAuthHeaders()
          },
          credentials: 'omit'
        });

        console.log(`Response status for ${endpoint}: ${response.status}`);

        // Handle 204 No Content as success with empty array
        if (response.status === 204) {
          console.log(`✅ No SPOCs found from ${endpoint} (204 No Content)`);
          return [];
        }

        if (response.ok) {
          // Try to parse the response as JSON
          let data;
          try {
            const text = await response.text();
            console.log(`Raw API response from ${endpoint}:`, text);
            data = text ? JSON.parse(text) : [];
          } catch (parseError) {
            console.error(`Error parsing JSON from ${endpoint}:`, parseError);
            continue; // Try next endpoint
          }

          // Ensure we always return an array
          if (!Array.isArray(data)) {
            console.warn(`API did not return an array from ${endpoint}, converting:`, data);
            data = data ? [data] : [];
          }

          console.log(`✅ Successfully fetched SPOCs from ${endpoint}:`, data);
          return data;
        } else {
          console.log(`❌ Failed to fetch from ${endpoint}: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error with endpoint ${endpoint}:`, error);
      }
    }

    console.error('❌ All SPOC endpoints failed');
    throw new Error('Failed to fetch SPOCs from all available endpoints');
  }

  async getSpocById(id: number): Promise<Spoc> {
    // Try multiple endpoints for getting SPOC by ID
    const endpoints = [
      `http://localhost:8091/api/spocs/${id}`,        // Direct to main controller - primary
      `http://localhost:8091/api/spocs/getById/${id}`, // Direct to backend controller
      `/api/spocs/${id}`,                             // Proxied main controller
      `/api/spocs/getById/${id}`                      // Proxied backend controller
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Trying SPOC by ID endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...this.getAuthHeaders()
          },
          credentials: 'omit'
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Successfully fetched SPOC from ${endpoint}:`, data);
          return data;
        } else {
          console.log(`❌ Failed to fetch from ${endpoint}: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error with endpoint ${endpoint}:`, error);
      }
    }

    throw new Error(`Failed to fetch SPOC with ID ${id} from all available endpoints`);
  }

  async createSpoc(spoc: Omit<Spoc, 'id'>): Promise<Spoc> {
    // Try multiple endpoints for creating SPOC
    const endpoints = [
      'http://localhost:8091/api/spocs',        // Direct to main controller - primary
      'http://localhost:8091/api/spocs/create', // Direct to backend controller
      '/api/spocs',                             // Proxied main controller
      '/api/spocs/create'                       // Proxied backend controller
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Trying SPOC create endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...this.getAuthHeaders()
          },
          body: JSON.stringify(spoc),
          credentials: 'omit'
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Successfully created SPOC via ${endpoint}:`, data);
          return data;
        } else {
          console.log(`❌ Failed to create via ${endpoint}: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error with endpoint ${endpoint}:`, error);
      }
    }

    throw new Error('Failed to create SPOC via all available endpoints');
  }

  async updateSpoc(id: number, spoc: Spoc): Promise<Spoc> {
    // Try multiple endpoints for updating SPOC
    const endpoints = [
      `http://localhost:8091/api/spocs/${id}`,        // Direct to main controller - primary
      `http://localhost:8091/api/spocs/update/${id}`, // Direct to backend controller
      `/api/spocs/${id}`,                             // Proxied main controller
      `/api/spocs/update/${id}`                       // Proxied backend controller
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Trying SPOC update endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: 'PUT',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...this.getAuthHeaders()
          },
          body: JSON.stringify(spoc),
          credentials: 'omit'
        });

        if (response.ok) {
          const data = await response.json();
          console.log(`✅ Successfully updated SPOC via ${endpoint}:`, data);
          return data;
        } else {
          console.log(`❌ Failed to update via ${endpoint}: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error with endpoint ${endpoint}:`, error);
      }
    }

    throw new Error(`Failed to update SPOC with ID ${id} via all available endpoints`);
  }

  async deleteSpoc(id: number): Promise<void> {
    // Try multiple endpoints for deleting SPOC
    const endpoints = [
      `http://localhost:8091/api/spocs/${id}`,           // Direct to main controller - primary
      `http://localhost:8091/api/spocs/deleteById/${id}`, // Direct to backend controller
      `/api/spocs/${id}`,                                // Proxied main controller
      `/api/spocs/deleteById/${id}`                      // Proxied backend controller
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`🔄 Trying SPOC delete endpoint: ${endpoint}`);

        const response = await fetch(endpoint, {
          method: 'DELETE',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...this.getAuthHeaders()
          },
          credentials: 'omit'
        });

        if (response.ok || response.status === 204) {
          console.log(`✅ Successfully deleted SPOC via ${endpoint}`);
          return;
        } else {
          console.log(`❌ Failed to delete via ${endpoint}: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ Error with endpoint ${endpoint}:`, error);
      }
    }

    throw new Error(`Failed to delete SPOC with ID ${id} via all available endpoints`);
  }
}

export const spocService = new SpocService();
