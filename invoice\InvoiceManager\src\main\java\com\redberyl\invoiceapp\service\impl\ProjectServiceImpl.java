package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.HsnCodeDto;
import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.entity.*;
import com.redberyl.invoiceapp.repository.BdmRepository;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import com.redberyl.invoiceapp.repository.ProjectRepository;
import com.redberyl.invoiceapp.service.ProjectService;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProjectServiceImpl implements ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    @Autowired
    private BdmRepository bdmRepository;

    @Override
    public List<ProjectDto> getAllProjects() {
        return projectRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ProjectDto getProjectById(Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Project not found with id: " + id));
        return convertToDto(project);
    }

    @Override
    public List<ProjectDto> getProjectsByClientId(Long clientId) {
        return projectRepository.findByClientId(clientId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProjectDto> getProjectsByBdmId(Long bdmId) {
        return projectRepository.findByBdmId(bdmId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public ProjectDto createProject(ProjectDto projectDto) {
        Project project = convertToEntity(projectDto);
        Project savedProject = projectRepository.save(project);
        return convertToDto(savedProject);
    }

    @Override
    @Transactional
    public ProjectDto updateProject(Long id, ProjectDto projectDto) {
        Project existingProject = projectRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Project not found with id: " + id));

        if (projectDto.getClientId() != null) {
            Client client = clientRepository.findById(projectDto.getClientId())
                    .orElseThrow(
                            () -> new EntityNotFoundException("Client not found with id: " + projectDto.getClientId()));
            existingProject.setClient(client);
        }

        updateProjectFromDto(existingProject, projectDto);

        Project updatedProject = projectRepository.save(existingProject);
        return convertToDto(updatedProject);
    }

    @Override
    @Transactional
    public void deleteProject(Long id) {
        if (!projectRepository.existsById(id)) {
            throw new EntityNotFoundException("Project not found with id: " + id);
        }
        projectRepository.deleteById(id);
    }

    private ProjectDto convertToDto(Project project) {
        // Start building the DTO with common fields
        ProjectDto.ProjectDtoBuilder builder = ProjectDto.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .email(project.getEmail())
                .phone(project.getPhone())
                .gstNumber(project.getGstNumber())
                .billingAddress(project.getBillingAddress())
                .shippingAddress(project.getShippingAddress())
                .state(project.getState()) // Add state field to DTO conversion
                .engagementCode(project.getEngagementCode())
                .clientPartnerName(project.getClientPartnerName())
                .clientPartnerEmail(project.getClientPartnerEmail())
                .clientPartnerPhone(project.getClientPartnerPhone())
                .commissionPercentage(project.getCommissionPercentage())
                .commissionAmount(project.getCommissionAmount());

        // Set client if available
        if (project.getClient() != null) {
            builder.clientId(project.getClient().getId());

            // Create and set the client DTO with essential fields only
            ClientDto clientDto = ClientDto.builder()
                    .id(project.getClient().getId())
                    .name(project.getClient().getName())
                    .build();

            // Set audit fields for client
            clientDto.setCreatedAt(project.getClient().getCreatedAt());
            clientDto.setUpdatedAt(project.getClient().getModifiedAt());

            builder.client(clientDto);
        }

        // Set HSN code if available
        if (project.getHsnCode() != null) {
            builder.hsnCodeId(project.getHsnCode().getId());

            // Create and set the HSN code DTO
            HsnCodeDto hsnCodeDto = HsnCodeDto.builder()
                    .id(project.getHsnCode().getId())
                    .code(project.getHsnCode().getCode())
                    .description(project.getHsnCode().getDescription())
                    .gstRate(project.getHsnCode().getGstRate())
                    .build();

            builder.hsnCode(hsnCodeDto);
        }

        // Set BDM if available
        if (project.getBdm() != null) {
            builder.bdmId(project.getBdm().getId());

            // Create and set the BDM DTO with essential fields only
            BdmDto bdmDto = BdmDto.builder()
                    .id(project.getBdm().getId())
                    .name(project.getBdm().getName())
                    .email(project.getBdm().getEmail())
                    .phone(project.getBdm().getPhone())
                    .build();

            // Set audit fields for BDM
            bdmDto.setCreatedAt(project.getBdm().getCreatedAt());
            bdmDto.setUpdatedAt(project.getBdm().getModifiedAt());

            builder.bdm(bdmDto);
        }

        // Build the DTO
        ProjectDto dto = builder.build();

        // Set the audit fields
        dto.setCreatedAt(project.getCreatedAt());
        dto.setUpdatedAt(project.getModifiedAt());

        return dto;
    }

    private Project convertToEntity(ProjectDto dto) {
        Project project = new Project();
        project.setId(dto.getId());

        if (dto.getClientId() != null) {
            Client client = clientRepository.findById(dto.getClientId())
                    .orElseThrow(() -> new EntityNotFoundException("Client not found with id: " + dto.getClientId()));
            project.setClient(client);
        }

        updateProjectFromDto(project, dto);

        return project;
    }

    private void updateProjectFromDto(Project project, ProjectDto dto) {
        project.setName(dto.getName());

        if (dto.getHsnCodeId() != null) {
            HsnCode hsnCode = hsnCodeRepository.findById(dto.getHsnCodeId())
                    .orElseThrow(
                            () -> new EntityNotFoundException("HSN Code not found with id: " + dto.getHsnCodeId()));
            project.setHsnCode(hsnCode);
        }

        project.setDescription(dto.getDescription());
        project.setEmail(dto.getEmail());
        project.setPhone(dto.getPhone());
        project.setGstNumber(dto.getGstNumber());
        project.setBillingAddress(dto.getBillingAddress());
        project.setShippingAddress(dto.getShippingAddress());
        project.setState(dto.getState()); // Add state field update
        project.setEngagementCode(dto.getEngagementCode());
        project.setClientPartnerName(dto.getClientPartnerName());
        project.setClientPartnerEmail(dto.getClientPartnerEmail());
        project.setClientPartnerPhone(dto.getClientPartnerPhone());

        if (dto.getBdmId() != null) {
            Bdm bdm = bdmRepository.findById(dto.getBdmId())
                    .orElseThrow(() -> new EntityNotFoundException("BDM not found with id: " + dto.getBdmId()));
            project.setBdm(bdm);
        }

        project.setCommissionPercentage(dto.getCommissionPercentage());
        project.setCommissionAmount(dto.getCommissionAmount());
    }
}

