<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Integration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #106ebe;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .token-display {
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OneDrive Integration Test</h1>
        <p>This page helps test and debug the OneDrive integration for the Invoice Manager.</p>

        <!-- Step 1: Test Backend Connection -->
        <div class="test-section">
            <h2>Step 1: Test Backend Connection</h2>
            <button onclick="testBackend()">Test Backend API</button>
            <div id="backend-result"></div>
        </div>

        <!-- Step 2: Get Authorization URL -->
        <div class="test-section">
            <h2>Step 2: Get Authorization URL</h2>
            <button onclick="getAuthUrl()">Get Auth URL</button>
            <div id="auth-url-result"></div>
        </div>

        <!-- Step 3: Device Code Authentication -->
        <div class="test-section">
            <h2>Step 3: Device Code Authentication</h2>
            <button onclick="startDeviceAuth()">Start Device Code Flow</button>
            <div id="device-auth-result"></div>
        </div>

        <!-- Step 4: Test Authentication -->
        <div class="test-section">
            <h2>Step 4: Test Access Token</h2>
            <input type="text" id="access-token" placeholder="Paste access token here..." style="width: 100%; padding: 8px; margin: 10px 0;">
            <button onclick="testAuth()">Test Authentication</button>
            <div id="auth-test-result"></div>
        </div>

        <!-- Step 5: Test PDF Generation -->
        <div class="test-section">
            <h2>Step 5: Test PDF Generation</h2>
            <input type="number" id="invoice-id" placeholder="Enter invoice ID (e.g., 1)" style="width: 200px; padding: 8px; margin: 10px 0;">
            <button onclick="testPdfGeneration()">Test PDF Generation</button>
            <button onclick="testBackendConnection()">Test Backend Connection</button>
            <div id="pdf-test-result"></div>
        </div>

        <!-- Step 6: Test File Upload -->
        <div class="test-section">
            <h2>Step 6: Test File Upload</h2>
            <button onclick="testUpload()" id="upload-btn" disabled>Test PDF Upload</button>
            <div id="upload-result"></div>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h2>Debug Information</h2>
            <div id="debug-info">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>Backend URL:</strong> <span id="backend-url"></span></p>
                <p><strong>Stored Token:</strong> <span id="stored-token"></span></p>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const BACKEND_URL = 'http://localhost:8091/api';
        let currentAccessToken = null;

        // Update debug info
        function updateDebugInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('backend-url').textContent = BACKEND_URL;
            const storedToken = localStorage.getItem('onedrive_access_token');
            document.getElementById('stored-token').textContent = storedToken ? 
                storedToken.substring(0, 20) + '...' : 'None';
        }

        // Test backend connection
        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '<div class="info">Testing backend connection...</div>';

            try {
                const response = await fetch(`${BACKEND_URL}/onedrive/test`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✓ Backend is working!<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Backend error: ${data.message || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Connection failed: ${error.message}</div>`;
            }
        }

        // Get authorization URL
        async function getAuthUrl() {
            const resultDiv = document.getElementById('auth-url-result');
            resultDiv.innerHTML = '<div class="info">Getting authorization URL...</div>';

            try {
                const response = await fetch(`${BACKEND_URL}/onedrive/auth-url`);
                const data = await response.json();
                
                if (response.ok && data.authUrl) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Authorization URL generated!</div>
                        <div class="token-display">${data.authUrl}</div>
                        <button onclick="window.open('${data.authUrl}', '_blank')">Open Auth URL</button>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Failed to get auth URL: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Request failed: ${error.message}</div>`;
            }
        }

        // Start device code authentication
        async function startDeviceAuth() {
            const resultDiv = document.getElementById('device-auth-result');
            resultDiv.innerHTML = '<div class="info">Starting device code flow...</div>';

            try {
                const response = await fetch(`${BACKEND_URL}/onedrive/device-code`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Device code generated!</div>
                        <div class="info">
                            <strong>User Code:</strong> <span style="font-size: 18px; font-weight: bold;">${data.user_code}</span><br>
                            <strong>Verification URL:</strong> <a href="${data.verification_uri}" target="_blank">${data.verification_uri}</a><br>
                            <strong>Message:</strong> ${data.message}
                        </div>
                        <button onclick="pollForToken('${data.device_code}')">Start Polling for Token</button>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Device code failed: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Request failed: ${error.message}</div>`;
            }
        }

        // Poll for device token
        async function pollForToken(deviceCode) {
            const resultDiv = document.getElementById('device-auth-result');
            let attempts = 0;
            const maxAttempts = 60; // 5 minutes with 5-second intervals

            const poll = async () => {
                attempts++;
                
                try {
                    const response = await fetch(`${BACKEND_URL}/onedrive/device-token`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ device_code: deviceCode })
                    });
                    const data = await response.json();
                    
                    if (data.success && data.access_token) {
                        currentAccessToken = data.access_token;
                        localStorage.setItem('onedrive_access_token', data.access_token);
                        document.getElementById('access-token').value = data.access_token;
                        document.getElementById('upload-btn').disabled = false;
                        
                        resultDiv.innerHTML += `
                            <div class="success">✓ Authentication successful!</div>
                            <div class="token-display">Access Token: ${data.access_token.substring(0, 50)}...</div>
                        `;
                        updateDebugInfo();
                        return;
                    } else if (data.error === 'authorization_pending') {
                        resultDiv.innerHTML = resultDiv.innerHTML.replace(/Polling attempt \d+\/\d+/, '') + 
                            `<div class="info">Polling attempt ${attempts}/${maxAttempts} - Waiting for user authorization...</div>`;
                        
                        if (attempts < maxAttempts) {
                            setTimeout(poll, 5000);
                        } else {
                            resultDiv.innerHTML += '<div class="error">✗ Polling timed out. Please try again.</div>';
                        }
                    } else {
                        resultDiv.innerHTML += `<div class="error">✗ Authentication failed: ${data.error || 'Unknown error'}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">✗ Polling failed: ${error.message}</div>`;
                }
            };

            poll();
        }

        // Test authentication
        async function testAuth() {
            const token = document.getElementById('access-token').value || currentAccessToken;
            const resultDiv = document.getElementById('auth-test-result');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">✗ No access token provided</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Testing authentication...</div>';

            try {
                const response = await fetch(`${BACKEND_URL}/onedrive/test-auth`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();
                
                if (response.ok && data.authenticated) {
                    currentAccessToken = token;
                    localStorage.setItem('onedrive_access_token', token);
                    document.getElementById('upload-btn').disabled = false;
                    resultDiv.innerHTML = `<div class="success">✓ Authentication valid!<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Authentication failed: ${data.message || 'Invalid token'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Request failed: ${error.message}</div>`;
            }
            
            updateDebugInfo();
        }

        // Test PDF generation
        async function testPdfGeneration() {
            const invoiceId = document.getElementById('invoice-id').value;
            const resultDiv = document.getElementById('pdf-test-result');

            if (!invoiceId) {
                resultDiv.innerHTML = '<div class="error">✗ Please enter an invoice ID</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Testing PDF generation...</div>';

            try {
                // Test the same endpoints that OneDrive button uses
                const apiUrls = [
                    `${BACKEND_URL}/invoice-generation/public/pdf/by-number/${invoiceId}`,
                    `${BACKEND_URL}/invoice-generation/public/pdf/${invoiceId}`,
                    `${BACKEND_URL}/invoice-generation/pdf/${invoiceId}`
                ];

                let success = false;
                let results = [];

                for (const url of apiUrls) {
                    try {
                        const response = await fetch(url, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/pdf'
                            }
                        });

                        if (response.ok) {
                            const blob = await response.blob();
                            results.push(`✓ ${url}: Success (${blob.size} bytes, ${blob.type})`);
                            success = true;

                            // Create download link for the PDF
                            const downloadUrl = URL.createObjectURL(blob);
                            results.push(`<a href="${downloadUrl}" download="test-invoice-${invoiceId}.pdf" style="color: blue; text-decoration: underline;">Download Generated PDF</a>`);
                            break;
                        } else {
                            const errorText = await response.text();
                            results.push(`✗ ${url}: HTTP ${response.status} - ${errorText}`);
                        }
                    } catch (error) {
                        results.push(`✗ ${url}: ${error.message}`);
                    }
                }

                if (success) {
                    resultDiv.innerHTML = `<div class="success">✓ PDF generation successful!<br>${results.join('<br>')}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ PDF generation failed from all endpoints:<br>${results.join('<br>')}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ PDF generation test failed: ${error.message}</div>`;
            }
        }

        // Test file upload
        async function testUpload() {
            const token = currentAccessToken || document.getElementById('access-token').value;
            const resultDiv = document.getElementById('upload-result');

            if (!token) {
                resultDiv.innerHTML = '<div class="error">✗ No access token available</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Creating test PDF and uploading...</div>';

            try {
                // Create a simple test PDF blob
                const testPdfContent = '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF';
                const blob = new Blob([testPdfContent], { type: 'application/pdf' });

                const formData = new FormData();
                formData.append('file', blob, 'test-invoice.pdf');
                formData.append('invoiceNumber', 'TEST-001');

                const response = await fetch(`${BACKEND_URL}/onedrive/upload-pdf`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    resultDiv.innerHTML = `<div class="success">✓ Upload successful!<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Upload failed: ${data.message || data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Upload failed: ${error.message}</div>`;
            }
        }

        // Test backend connection
        async function testBackendConnection() {
            const resultDiv = document.getElementById('pdf-test-result');
            resultDiv.innerHTML = '<div class="info">Testing backend connection...</div>';

            try {
                const response = await fetch(`${BACKEND_URL}/invoice-generation/test`);
                if (response.ok) {
                    const text = await response.text();
                    resultDiv.innerHTML = `<div class="success">✓ Backend is running: ${text}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ Backend responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Backend connection failed: ${error.message}<br><br>
                    <strong>To fix this:</strong><br>
                    1. Open terminal in backend folder<br>
                    2. Run: <code>mvn spring-boot:run</code><br>
                    3. Wait for "Started InvoiceAppApplication" message<br>
                    4. Try this test again</div>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
            
            // Check for stored token
            const storedToken = localStorage.getItem('onedrive_access_token');
            if (storedToken) {
                document.getElementById('access-token').value = storedToken;
                currentAccessToken = storedToken;
                document.getElementById('upload-btn').disabled = false;
            }
        });
    </script>
</body>
</html>
