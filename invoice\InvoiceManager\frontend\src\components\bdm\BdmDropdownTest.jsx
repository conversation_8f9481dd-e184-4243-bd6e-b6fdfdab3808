import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Divider,
  useToast,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Alert,
  AlertIcon,
  SimpleGrid,
  Code
} from '@chakra-ui/react';
import BdmDropdown from '../BdmDropdown';
import BdmManagement from './BdmManagement';

const BdmDropdownTest = () => {
  const toast = useToast();
  const [selectedBdmId, setSelectedBdmId] = useState(null);
  const [showManagement, setShowManagement] = useState(false);

  // Handle BDM selection
  const handleBdmChange = (bdmId) => {
    console.log('Selected BDM ID:', bdmId);
    setSelectedBdmId(bdmId);
    
    toast({
      title: 'BDM Selected',
      description: `You selected BDM with ID: ${bdmId}`,
      status: 'info',
      duration: 3060,
      isClosable: true
    });
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box>
          <Heading size="lg">BDM Dropdown Test</Heading>
          <Text mt={2} color="gray.600">
            This page tests the BDM dropdown component to ensure it correctly displays all BDM names from the database.
          </Text>
        </Box>
        
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          <Card>
            <CardHeader>
              <Heading size="md">BDM Dropdown</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <Text>
                  The dropdown below should display all BDM names from the database.
                  Select a BDM to see its ID.
                </Text>
                
                <BdmDropdown 
                  value={selectedBdmId} 
                  onChange={handleBdmChange}
                  required={true}
                  label="Select a BDM"
                />
                
                {selectedBdmId && (
                  <Alert status="success" mt={4}>
                    <AlertIcon />
                    <Text>Selected BDM ID: <strong>{selectedBdmId}</strong></Text>
                  </Alert>
                )}
              </VStack>
            </CardBody>
            <CardFooter>
              <Button 
                colorScheme="blue" 
                onClick={() => setShowManagement(!showManagement)}
              >
                {showManagement ? 'Hide BDM Management' : 'Show BDM Management'}
              </Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <Heading size="md">How It Works</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <Text>
                  The BDM dropdown component fetches BDM data from the following endpoints:
                </Text>
                
                <Box bg="gray.50" p={3} borderRadius="md">
                  <Text fontWeight="bold">Primary Endpoint:</Text>
                  <Code>{import.meta.env.VITE_API_URL}/v1/bdms?size=100</Code>

                  <Text fontWeight="bold" mt={2}>Fallback Endpoints:</Text>
                  <Code>{import.meta.env.VITE_API_URL}/bdms</Code>
                  <Code>{import.meta.env.VITE_API_URL}/v1/bdms/all</Code>
                </Box>
                
                <Text> without 
                  The component handles various response formats and extracts the BDM data.
                  It then sorts the BDMs by name and displays them in the dropdown.
                </Text>
                
                <Alert status="info">
                  <AlertIcon />
                  <Text>
                    If no BDMs are found or an error occurs, the component will display mock data
                    to ensure the UI remains functional.
                  </Text>
                </Alert>
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>
        
        {showManagement && (
          <>
            <Divider my={4} />
            <BdmManagement />
          </>
        )}
      </VStack>
    </Container>
  );
};

export default BdmDropdownTest;

