package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.Reminder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReminderRepository extends JpaRepository<Reminder, Long> {
    List<Reminder> findByInvoiceId(Long invoiceId);
    List<Reminder> findByMethod(String method);
    List<Reminder> findByStatus(String status);
}
