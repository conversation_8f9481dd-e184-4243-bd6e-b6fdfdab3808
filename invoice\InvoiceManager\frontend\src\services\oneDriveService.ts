import { apiRequest } from '@/utils/api';

export interface OneDriveUploadResponse {
  success: boolean;
  message: string;
  fileId?: string;
  fileName?: string;
  webUrl?: string;
  downloadUrl?: string;
  fileSize?: number;
  uploadedAt?: string;
  error?: string;
}

export interface OneDriveAuthResponse {
  success: boolean;
  authUrl?: string;
  error?: string;
}

export interface OneDriveAuthStatus {
  authenticated: boolean;
  success: boolean;
  error?: string;
}

class OneDriveService {
  private accessToken: string | null = null;

  /**
   * Get OneDrive authorization URL
   */
  async getAuthorizationUrl(): Promise<OneDriveAuthResponse> {
    try {
      const response = await apiRequest('/onedrive/auth-url', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error getting OneDrive authorization URL:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get authorization URL'
      };
    }
  }

  /**
   * Authenticate with OneDrive using device code flow (no redirect URI needed)
   */
  async authenticateWithDeviceCode(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      // Start device code flow
      const deviceCodeResponse = await apiRequest('/onedrive/device-code', {
        method: 'POST'
      });

      if (!deviceCodeResponse.success) {
        return {
          success: false,
          error: deviceCodeResponse.error || 'Failed to start device code flow'
        };
      }

      const { device_code, user_code, verification_uri, interval = 5 } = deviceCodeResponse;

      // Create a more user-friendly modal-like experience
      return new Promise((resolve) => {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 10000;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        // Create modal content
        const modal = document.createElement('div');
        modal.style.cssText = `
          background: white;
          padding: 30px;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          max-width: 500px;
          width: 90%;
          text-align: center;
        `;

        modal.innerHTML = `
          <h2 style="margin: 0 0 20px 0; color: #333;">OneDrive Authentication</h2>
          <p style="margin: 0 0 20px 0; color: #666; line-height: 1.5;">
            To save files to OneDrive, please complete the authentication process:
          </p>
          <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <p style="margin: 0 0 10px 0; font-weight: bold; color: #333;">Your verification code:</p>
            <div style="font-size: 24px; font-weight: bold; color: #0078d4; letter-spacing: 2px; margin: 10px 0;">
              ${user_code}
            </div>
            <button id="copyCode" style="
              background: #0078d4;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 4px;
              cursor: pointer;
              margin-top: 10px;
            ">Copy Code</button>
          </div>
          <div style="margin: 20px 0;">
            <button id="openAuth" style="
              background: #0078d4;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 16px;
              margin-right: 10px;
            ">Open Authentication Page</button>
            <button id="cancelAuth" style="
              background: #666;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 16px;
            ">Cancel</button>
          </div>
          <p style="margin: 20px 0 0 0; color: #666; font-size: 14px;">
            Waiting for authentication... This window will close automatically once you complete the process.
          </p>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // Add event listeners
        const copyButton = modal.querySelector('#copyCode') as HTMLButtonElement;
        const openButton = modal.querySelector('#openAuth') as HTMLButtonElement;
        const cancelButton = modal.querySelector('#cancelAuth') as HTMLButtonElement;

        copyButton.addEventListener('click', async () => {
          try {
            await navigator.clipboard.writeText(user_code);
            copyButton.textContent = 'Copied!';
            copyButton.style.background = '#107c10';
            setTimeout(() => {
              copyButton.textContent = 'Copy Code';
              copyButton.style.background = '#0078d4';
            }, 2000);
          } catch (e) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = user_code;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            copyButton.textContent = 'Copied!';
            copyButton.style.background = '#107c10';
          }
        });

        openButton.addEventListener('click', () => {
          window.open(verification_uri, '_blank');
        });

        cancelButton.addEventListener('click', () => {
          document.body.removeChild(overlay);
          resolve({
            success: false,
            error: 'Authentication cancelled by user'
          });
        });

        // Auto-copy code to clipboard
        copyButton.click();

        // Poll for token
        const pollInterval = setInterval(async () => {
          try {
            const tokenResponse = await apiRequest('/onedrive/device-token', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ device_code })
            });

            if (tokenResponse.success && tokenResponse.access_token) {
              clearInterval(pollInterval);
              document.body.removeChild(overlay);
              this.accessToken = tokenResponse.access_token;
              localStorage.setItem('onedrive_access_token', tokenResponse.access_token);
              resolve({
                success: true,
                accessToken: tokenResponse.access_token
              });
            } else if (tokenResponse.error === 'authorization_declined') {
              clearInterval(pollInterval);
              document.body.removeChild(overlay);
              resolve({
                success: false,
                error: 'Authorization was declined by the user'
              });
            } else if (tokenResponse.error === 'expired_token') {
              clearInterval(pollInterval);
              document.body.removeChild(overlay);
              resolve({
                success: false,
                error: 'The device code has expired. Please try again.'
              });
            } else if (tokenResponse.error !== 'authorization_pending') {
              clearInterval(pollInterval);
              document.body.removeChild(overlay);
              resolve({
                success: false,
                error: tokenResponse.error || 'Failed to get access token'
              });
            }
            // If authorization_pending, continue polling
          } catch (error) {
            clearInterval(pollInterval);
            document.body.removeChild(overlay);
            resolve({
              success: false,
              error: error instanceof Error ? error.message : 'Failed to poll for token'
            });
          }
        }, interval * 1000);

        // Timeout after 15 minutes
        setTimeout(() => {
          clearInterval(pollInterval);
          if (document.body.contains(overlay)) {
            document.body.removeChild(overlay);
          }
          resolve({
            success: false,
            error: 'Authentication timed out. Please try again.'
          });
        }, 900000); // 15 minutes
      });
    } catch (error) {
      console.error('Error during device code authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Authenticate with OneDrive using multiple fallback methods
   */
  async authenticate(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      // First, try device code flow (most reliable)
      console.log('Attempting device code authentication...');
      const deviceResult = await this.authenticateWithDeviceCode();

      if (deviceResult.success) {
        return deviceResult;
      }

      // If device code fails, try popup method
      console.log('Device code failed, trying popup authentication...');
      return await this.authenticateWithPopup();

    } catch (error) {
      console.error('Error during OneDrive authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Authenticate with OneDrive using popup window (fallback method)
   */
  async authenticateWithPopup(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      const authResponse = await this.getAuthorizationUrl();

      if (!authResponse.success || !authResponse.authUrl) {
        return {
          success: false,
          error: authResponse.error || 'Failed to get authorization URL'
        };
      }

      return new Promise((resolve) => {
        const popup = window.open(
          authResponse.authUrl,
          'onedrive-auth',
          'width=600,height=700,scrollbars=yes,resizable=yes,left=' +
          (window.screen.width / 2 - 300) + ',top=' + (window.screen.height / 2 - 350)
        );

        if (!popup) {
          resolve({
            success: false,
            error: 'Failed to open popup window. Please allow popups for this site or try the device code method.'
          });
          return;
        }

        let resolved = false;

        // Listen for messages from the popup
        const messageListener = (event: MessageEvent) => {
          // Allow messages from the backend callback (different origin)
          const allowedOrigins = [
            window.location.origin,
            'http://localhost:8091',
            'http://localhost:3000',
            'http://localhost:3060'
          ];

          if (!allowedOrigins.includes(event.origin)) {
            console.log('Ignoring message from origin:', event.origin);
            return;
          }

          // Check if this is a OneDrive auth message
          if (!event.data || typeof event.data !== 'object') {
            return;
          }

          // Look for OneDrive auth response
          if (event.data.source === 'onedrive-auth' || event.data.success !== undefined) {
            if (resolved) return;
            resolved = true;

            if (event.data.success) {
              this.accessToken = event.data.accessToken;
              localStorage.setItem('onedrive_access_token', event.data.accessToken);
              window.removeEventListener('message', messageListener);
              resolve({
                success: true,
                accessToken: event.data.accessToken
              });
            } else {
              window.removeEventListener('message', messageListener);
              resolve({
                success: false,
                error: event.data.error || 'Authentication failed'
              });
            }
          }
        };

        window.addEventListener('message', messageListener);

        // Check if popup was closed manually
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            if (!resolved) {
              resolved = true;
              window.removeEventListener('message', messageListener);
              resolve({
                success: false,
                error: 'Authentication was cancelled. Please try again or use the device code method.'
              });
            }
          }
        }, 1000);

        // Timeout after 5 minutes
        setTimeout(() => {
          if (!resolved) {
            resolved = true;
            clearInterval(checkClosed);
            window.removeEventListener('message', messageListener);
            if (!popup.closed) {
              popup.close();
            }
            resolve({
              success: false,
              error: 'Authentication timed out. Please try again.'
            });
          }
        }, 300000); // 5 minutes
      });
    } catch (error) {
      console.error('Error during popup authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Popup authentication failed'
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  async checkAuthentication(): Promise<OneDriveAuthStatus> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return { authenticated: false, success: true };
      }

      const response = await apiRequest('/onedrive/check-auth', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.authenticated) {
        this.accessToken = token;
      } else {
        this.accessToken = null;
        localStorage.removeItem('onedrive_access_token');
      }

      return response;
    } catch (error) {
      console.error('Error checking OneDrive authentication:', error);
      this.accessToken = null;
      localStorage.removeItem('onedrive_access_token');
      return {
        authenticated: false,
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check authentication'
      };
    }
  }

  /**
   * Upload PDF to OneDrive
   */
  async uploadPdf(pdfBlob: Blob, invoiceNumber?: string): Promise<OneDriveUploadResponse> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return {
          success: false,
          message: 'Not authenticated with OneDrive',
          error: 'Please authenticate with OneDrive first'
        };
      }

      const formData = new FormData();
      formData.append('file', pdfBlob, `invoice_${invoiceNumber || 'document'}.pdf`);
      
      if (invoiceNumber) {
        formData.append('invoiceNumber', invoiceNumber);
      }

      const response = await fetch('/api/onedrive/upload-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading PDF to OneDrive:', error);
      return {
        success: false,
        message: 'Failed to upload PDF to OneDrive',
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload invoice PDF with base64 content
   */
  async uploadInvoicePdf(pdfBase64: string, invoiceNumber: string): Promise<OneDriveUploadResponse> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return {
          success: false,
          message: 'Not authenticated with OneDrive',
          error: 'Please authenticate with OneDrive first'
        };
      }

      const response = await apiRequest('/onedrive/upload-invoice-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pdfContent: pdfBase64,
          invoiceNumber: invoiceNumber
        })
      });

      return response;
    } catch (error) {
      console.error('Error uploading invoice PDF to OneDrive:', error);
      return {
        success: false,
        message: 'Failed to upload invoice PDF to OneDrive',
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return this.accessToken || localStorage.getItem('onedrive_access_token');
  }

  /**
   * Clear authentication
   */
  clearAuthentication(): void {
    this.accessToken = null;
    localStorage.removeItem('onedrive_access_token');
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    return !!(this.accessToken || localStorage.getItem('onedrive_access_token'));
  }
}

export const oneDriveService = new OneDriveService();
export default oneDriveService;
