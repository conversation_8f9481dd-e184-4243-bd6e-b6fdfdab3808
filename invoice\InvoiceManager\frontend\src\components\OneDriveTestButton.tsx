import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Cloud } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService from '@/services/oneDriveService';
import OneDriveAuthModal from '@/components/OneDriveAuthModal';

const OneDriveTestButton: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authStatus, setAuthStatus] = useState<'idle' | 'checking' | 'success' | 'error'>('idle');

  const checkAuthentication = async () => {
    setIsChecking(true);
    setAuthStatus('checking');
    
    try {
      const status = await oneDriveService.checkAuthentication();
      
      if (status.authenticated) {
        setIsAuthenticated(true);
        setAuthStatus('success');
        toast.success('Already authenticated with OneDrive!');
      } else {
        setIsAuthenticated(false);
        setAuthStatus('error');
        toast.info('Not authenticated with OneDrive');
      }
    } catch (error) {
      setIsAuthenticated(false);
      setAuthStatus('error');
      toast.error('Failed to check authentication status');
    } finally {
      setIsChecking(false);
    }
  };

  const handleAuthenticate = () => {
    setShowAuthModal(true);
  };

  const handleAuthSuccess = (accessToken: string) => {
    setIsAuthenticated(true);
    setAuthStatus('success');
    setShowAuthModal(false);
    toast.success('Successfully authenticated with OneDrive!');
  };

  const handleAuthError = (error: string) => {
    setShowAuthModal(false);
    toast.error(`Authentication failed: ${error}`);
  };

  const testUpload = async () => {
    if (!isAuthenticated) {
      toast.error('Please authenticate first');
      return;
    }

    try {
      // Create a simple test PDF content
      const testContent = 'Test PDF content for OneDrive upload';
      const blob = new Blob([testContent], { type: 'application/pdf' });
      
      toast.info('Testing file upload...');
      
      const response = await oneDriveService.uploadPdf(blob, 'test-invoice-' + Date.now());
      
      if (response.success) {
        toast.success('Test upload successful!', {
          description: response.fileName ? `File: ${response.fileName}` : undefined
        });
      } else {
        toast.error('Test upload failed', {
          description: response.error || response.message
        });
      }
    } catch (error) {
      toast.error('Test upload failed', {
        description: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cloud className="h-5 w-5" />
          OneDrive Test
        </CardTitle>
        <CardDescription>
          Test OneDrive authentication and file upload functionality
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Authentication Status:</span>
          {authStatus === 'idle' && <Badge variant="secondary">Unknown</Badge>}
          {authStatus === 'checking' && (
            <Badge variant="secondary">
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              Checking...
            </Badge>
          )}
          {authStatus === 'success' && (
            <Badge variant="default" className="bg-green-500">
              <CheckCircle className="h-3 w-3 mr-1" />
              Authenticated
            </Badge>
          )}
          {authStatus === 'error' && (
            <Badge variant="destructive">
              <XCircle className="h-3 w-3 mr-1" />
              Not Authenticated
            </Badge>
          )}
        </div>

        <div className="space-y-2">
          <Button
            onClick={checkAuthentication}
            disabled={isChecking}
            variant="outline"
            className="w-full"
          >
            {isChecking ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              'Check Authentication'
            )}
          </Button>

          <Button
            onClick={handleAuthenticate}
            disabled={isAuthenticated}
            className="w-full"
          >
            {isAuthenticated ? 'Already Authenticated' : 'Authenticate with OneDrive'}
          </Button>

          <Button
            onClick={testUpload}
            disabled={!isAuthenticated}
            variant="secondary"
            className="w-full"
          >
            Test File Upload
          </Button>
        </div>

        <OneDriveAuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          onSuccess={handleAuthSuccess}
          onError={handleAuthError}
        />
      </CardContent>
    </Card>
  );
};

export default OneDriveTestButton;
