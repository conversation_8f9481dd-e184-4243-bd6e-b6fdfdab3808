package com.redberyl.invoiceapp.config;

import com.redberyl.invoiceapp.entity.auth.ERole;
import com.redberyl.invoiceapp.entity.auth.Role;
import com.redberyl.invoiceapp.entity.auth.User;
import com.redberyl.invoiceapp.repository.RoleRepository;
import com.redberyl.invoiceapp.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.HashSet;
import java.util.Set;

@Configuration
public class DataInitializer {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Bean
    public CommandLineRunner initData() {
        return args -> {
            // Initialize roles
            initializeRoles();

            // Check if admin user already exists
            if (userRepository.findByUsername("admin").isEmpty()) {
                try {
                    // Create admin user
                    User adminUser = new User();
                    adminUser.setUsername("admin");
                    adminUser.setEmail("<EMAIL>");
                    adminUser.setPassword(passwordEncoder.encode("admin123"));

                    Set<Role> roles = new HashSet<>();
                    Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                            .orElseThrow(() -> new RuntimeException("Error: Role ADMIN is not found."));
                    roles.add(adminRole);
                    adminUser.setRoles(roles);

                    userRepository.save(adminUser);
                    System.out.println("Admin user created successfully!");
                } catch (Exception e) {
                    System.err.println("Error creating admin user: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                System.out.println("Admin user already exists, skipping creation.");
            }
        };
    }

    private void initializeRoles() {
        System.out.println("Initializing roles...");

        // Check and create USER role if it doesn't exist
        if (roleRepository.findByName(ERole.ROLE_USER).isEmpty()) {
            Role userRole = new Role();
            userRole.setName(ERole.ROLE_USER);
            roleRepository.save(userRole);
            System.out.println("ROLE_USER created successfully!");
        } else {
            System.out.println("ROLE_USER already exists.");
        }

        // Check and create MODERATOR role if it doesn't exist
        if (roleRepository.findByName(ERole.ROLE_MODERATOR).isEmpty()) {
            Role modRole = new Role();
            modRole.setName(ERole.ROLE_MODERATOR);
            roleRepository.save(modRole);
            System.out.println("ROLE_MODERATOR created successfully!");
        } else {
            System.out.println("ROLE_MODERATOR already exists.");
        }

        // Check and create ADMIN role if it doesn't exist
        if (roleRepository.findByName(ERole.ROLE_ADMIN).isEmpty()) {
            Role adminRole = new Role();
            adminRole.setName(ERole.ROLE_ADMIN);
            roleRepository.save(adminRole);
            System.out.println("ROLE_ADMIN created successfully!");
        } else {
            System.out.println("ROLE_ADMIN already exists.");
        }

        // Print all roles for verification
        System.out.println("All roles in database:");
        roleRepository.findAll()
                .forEach(role -> System.out.println("Role ID: " + role.getId() + ", Name: " + role.getName()));
    }
}
