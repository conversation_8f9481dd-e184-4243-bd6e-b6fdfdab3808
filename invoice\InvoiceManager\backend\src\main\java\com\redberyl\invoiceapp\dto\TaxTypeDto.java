package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
public class TaxTypeDto extends BaseDto {
    private Long id;
    
    @NotBlank(message = "Tax type is required")
    private String taxType;
    
    private String taxTypeDescription;
}
