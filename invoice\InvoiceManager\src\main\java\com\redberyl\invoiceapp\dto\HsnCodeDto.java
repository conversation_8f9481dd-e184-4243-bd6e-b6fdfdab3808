package com.redberyl.invoiceapp.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class HsnCodeDto extends BaseDto {
    private Long id;
    
    @NotBlank(message = "HSN code is required")
    private String code;
    
    private String description;
    private BigDecimal gstRate;
}
