import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0", // Listen on all interfaces
    port: 3060,
    strictPort: true,
    open: true,
    cors: true,
    // Enable history API fallback for client-side routing
    // This ensures that refreshing pages like /clients, /invoices works correctly
    historyApiFallback: true,
    proxy: {
      '/api': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        // Don't strip /api prefix - backend expects it
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Proxy error:', err.message);
        },
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      },
      // Public project endpoints - these don't have /api prefix in backend
      '/projects': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying Project ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Project Proxy error:', err.message);
        },
      },
      // Public client endpoints - these don't have /api prefix in backend
      '/clients': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying Client ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Client Proxy error:', err.message);
        },
      },
      // Public candidate endpoints - these don't have /api prefix in backend
      '/candidates': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying Candidate ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('Candidate Proxy error:', err.message);
        },
      },
      // Public SPOC endpoints - these don't have /api prefix in backend
      '/spocs': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying SPOC ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('SPOC Proxy error:', err.message);
        },
      },
      // BDM endpoints - these don't have /api prefix in backend
      '/bdms': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying BDM ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('BDM Proxy error:', err.message);
        },
      },
      // v1 endpoints - these don't have /api prefix in backend
      '/v1': {
        // target: 'http://localhost:8091',
        target: 'http://localhost:8091',
        changeOrigin: true,
        secure: false,
        onProxyReq: (_proxyReq:any, req:any, _res:any) => {
          console.log(`Proxying v1 ${req.method} request to: ${req.url}`);
        },
        onError: (err:any, _req:any, _res:any) => {
          console.error('v1 Proxy error:', err.message);
        },
      },
      // NOTE: These proxies are for API calls to public controllers (no /api prefix)
      // Frontend routes like /clients-projects/projects, /candidates, etc. are handled by React Router
      // Only actual API calls to these endpoints will be proxied to the backend
      // For example: fetch('/projects') will be proxied, but navigating to /clients-projects/projects will not
    }
  },
  base: "/",
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
