package com.redberyl.invoiceapp.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HtmlController {

    @GetMapping("/tax-rate-form")
    public String taxRateForm() {
        return "tax-rate-form.html";
    }

    @GetMapping("/fixed-tax-rate-form")
    public String fixedTaxRateForm() {
        return "fixed-tax-rate-form.html";
    }

    @GetMapping("/tax-rate-with-audit")
    public String taxRateWithAudit() {
        return "tax-rate-with-audit.html";
    }
}
