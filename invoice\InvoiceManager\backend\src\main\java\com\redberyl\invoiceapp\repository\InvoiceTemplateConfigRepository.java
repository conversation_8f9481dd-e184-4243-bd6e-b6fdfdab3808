package com.redberyl.invoiceapp.repository;

import com.redberyl.invoiceapp.entity.InvoiceTemplateConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for invoice template configuration
 */
@Repository
public interface InvoiceTemplateConfigRepository extends JpaRepository<InvoiceTemplateConfig, Long> {
    
    /**
     * Find configuration by key
     */
    Optional<InvoiceTemplateConfig> findByConfigKey(String configKey);
    
    /**
     * Find all configurations by category
     */
    List<InvoiceTemplateConfig> findByCategoryOrderByDisplayOrder(String category);
    
    /**
     * Find all active configurations
     */
    List<InvoiceTemplateConfig> findByIsActiveTrueOrderByDisplayOrder();
    
    /**
     * Find all active configurations by category
     */
    List<InvoiceTemplateConfig> findByCategoryAndIsActiveTrueOrderByDisplayOrder(String category);
    
    /**
     * Check if configuration key exists
     */
    boolean existsByConfigKey(String configKey);
    
    /**
     * Get configuration value by key
     */
    @Query("SELECT c.configValue FROM InvoiceTemplateConfig c WHERE c.configKey = :configKey AND c.isActive = true")
    Optional<String> findConfigValueByKey(@Param("configKey") String configKey);
}
