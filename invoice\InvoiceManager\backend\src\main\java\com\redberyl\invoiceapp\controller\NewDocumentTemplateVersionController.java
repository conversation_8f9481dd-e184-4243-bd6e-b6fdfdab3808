package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;
import com.redberyl.invoiceapp.service.DocumentTemplateVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/new-document-template-versions")
@Tag(name = "New Document Template Version", description = "New Document Template Version management API")
public class NewDocumentTemplateVersionController {

    @Autowired
    private DocumentTemplateVersionService documentTemplateVersionService;

    @GetMapping
    @Operation(summary = "Get all document template versions", description = "Retrieve a list of all document template versions")
    public ResponseEntity<List<DocumentTemplateVersionDto>> getAllDocumentTemplateVersions() {
        List<DocumentTemplateVersionDto> documentTemplateVersions = documentTemplateVersionService
                .getAllDocumentTemplateVersions();
        return new ResponseEntity<>(documentTemplateVersions, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get document template version by ID", description = "Retrieve a document template version by its ID")
    public ResponseEntity<DocumentTemplateVersionDto> getDocumentTemplateVersionById(@PathVariable Long id) {
        DocumentTemplateVersionDto documentTemplateVersion = documentTemplateVersionService
                .getDocumentTemplateVersionById(id);
        return new ResponseEntity<>(documentTemplateVersion, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create document template version", description = "Create a new document template version")
    public ResponseEntity<DocumentTemplateVersionDto> createDocumentTemplateVersion(
            @Valid @RequestBody DocumentTemplateVersionDto documentTemplateVersionDto) {
        DocumentTemplateVersionDto createdDocumentTemplateVersion = documentTemplateVersionService
                .createDocumentTemplateVersion(documentTemplateVersionDto);
        return new ResponseEntity<>(createdDocumentTemplateVersion, HttpStatus.CREATED);
    }
}
