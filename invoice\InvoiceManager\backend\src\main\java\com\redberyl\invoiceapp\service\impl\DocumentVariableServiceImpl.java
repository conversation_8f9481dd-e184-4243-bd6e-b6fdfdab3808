package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.DocumentTemplateDto;
import com.redberyl.invoiceapp.dto.DocumentTemplateVersionDto;
import com.redberyl.invoiceapp.dto.DocumentVariableDto;
import com.redberyl.invoiceapp.entity.DocumentTemplateVersion;
import com.redberyl.invoiceapp.entity.DocumentVariable;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ForeignKeyViolationException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.DocumentTemplateVersionRepository;
import com.redberyl.invoiceapp.repository.DocumentVariableRepository;
import com.redberyl.invoiceapp.service.DocumentVariableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class DocumentVariableServiceImpl implements DocumentVariableService {

    @Autowired
    private DocumentVariableRepository documentVariableRepository;

    @Autowired
    private DocumentTemplateVersionRepository documentTemplateVersionRepository;

    @Override
    public List<DocumentVariableDto> getAllDocumentVariables() {
        List<DocumentVariable> documentVariables = documentVariableRepository.findAll();
        if (documentVariables.isEmpty()) {
            throw new NoContentException("No document variables found");
        }
        return documentVariables.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public DocumentVariableDto getDocumentVariableById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document variable ID cannot be null");
        }

        DocumentVariable documentVariable = documentVariableRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Variable not found with id: " + id));
        return convertToDto(documentVariable);
    }

    @Override
    public List<DocumentVariableDto> getDocumentVariablesByTemplateVersionId(Long templateVersionId) {
        if (templateVersionId == null) {
            throw new NullConstraintViolationException("templateVersionId", "Template version ID cannot be null");
        }

        // Check if template version exists
        if (!documentTemplateVersionRepository.existsById(templateVersionId)) {
            throw new ResourceNotFoundException("Document Template Version not found with id: " + templateVersionId);
        }

        List<DocumentVariable> documentVariables = documentVariableRepository.findByVersionId(templateVersionId);
        if (documentVariables.isEmpty()) {
            throw new NoContentException(
                    "No document variables found for template version with id: " + templateVersionId);
        }

        return documentVariables.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private void validateDocumentVariableDto(DocumentVariableDto documentVariableDto) {
        if (documentVariableDto == null) {
            throw new NullConstraintViolationException("documentVariableDto", "Document variable data cannot be null");
        }

        if (!StringUtils.hasText(documentVariableDto.getVariableName())) {
            throw new NullConstraintViolationException("variableName", "Variable name cannot be empty");
        }

        if (documentVariableDto.getTemplateVersionId() == null) {
            throw new NullConstraintViolationException("templateVersionId", "Template version ID cannot be null");
        }

        // Validate template version ID
        if (!documentTemplateVersionRepository.existsById(documentVariableDto.getTemplateVersionId())) {
            throw new ForeignKeyViolationException("templateVersionId",
                    "Document template version not found with id: " + documentVariableDto.getTemplateVersionId());
        }
    }

    @Override
    @Transactional
    public DocumentVariableDto createDocumentVariable(DocumentVariableDto documentVariableDto) {
        validateDocumentVariableDto(documentVariableDto);

        try {
            DocumentVariable documentVariable = convertToEntity(documentVariableDto);
            DocumentVariable savedDocumentVariable = documentVariableRepository.save(documentVariable);
            return convertToDto(savedDocumentVariable);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("variableName",
                        "Document variable with this name already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error creating document variable: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error creating document variable", e);
        }
    }

    @Override
    @Transactional
    public DocumentVariableDto updateDocumentVariable(Long id, DocumentVariableDto documentVariableDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document variable ID cannot be null");
        }

        DocumentVariable existingDocumentVariable = documentVariableRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document Variable not found with id: " + id));

        try {
            // Update template version if provided
            if (documentVariableDto.getTemplateVersionId() != null &&
                    !documentVariableDto.getTemplateVersionId().equals(existingDocumentVariable.getVersion().getId())) {

                DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository
                        .findById(documentVariableDto.getTemplateVersionId())
                        .orElseThrow(() -> new ForeignKeyViolationException("templateVersionId",
                                "Document template version not found with id: "
                                        + documentVariableDto.getTemplateVersionId()));
                existingDocumentVariable.setVersion(documentTemplateVersion);
            }

            if (StringUtils.hasText(documentVariableDto.getVariableName())) {
                existingDocumentVariable.setVariableName(documentVariableDto.getVariableName());
            }

            existingDocumentVariable.setDescription(documentVariableDto.getDescription());
            existingDocumentVariable.setSampleValue(documentVariableDto.getSampleValue());

            DocumentVariable updatedDocumentVariable = documentVariableRepository.save(existingDocumentVariable);
            return convertToDto(updatedDocumentVariable);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("field", "Required field cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("variableName",
                        "Document variable with this name already exists");
            } else if (message.contains("foreign key") || message.contains("reference")) {
                throw new ForeignKeyViolationException("foreignKey", "Referenced entity does not exist");
            } else {
                throw new CustomException("Error updating document variable: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating document variable", e);
        }
    }

    @Override
    @Transactional
    public void deleteDocumentVariable(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Document variable ID cannot be null");
        }

        if (!documentVariableRepository.existsById(id)) {
            throw new ResourceNotFoundException("Document Variable not found with id: " + id);
        }

        try {
            documentVariableRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete document variable because it is referenced by other entities",
                        e);
            } else {
                throw new CustomException("Error deleting document variable: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting document variable", e);
        }
    }

    private DocumentVariableDto convertToDto(DocumentVariable documentVariable) {
        // Convert the template version to DTO
        DocumentTemplateVersionDto templateVersionDto = null;
        if (documentVariable.getVersion() != null) {
            DocumentTemplateDto templateDto = null;
            if (documentVariable.getVersion().getTemplate() != null) {
                templateDto = DocumentTemplateDto.builder()
                        .id(documentVariable.getVersion().getTemplate().getId())
                        .name(documentVariable.getVersion().getTemplate().getName())
                        .templateType(documentVariable.getVersion().getTemplate().getTemplateType())
                        .filePath(documentVariable.getVersion().getTemplate().getFilePath())
                        .build();
            }

            templateVersionDto = DocumentTemplateVersionDto.builder()
                    .id(documentVariable.getVersion().getId())
                    .versionNumber(documentVariable.getVersion().getVersionNumber())
                    .content(documentVariable.getVersion().getContent())
                    .createdBy(documentVariable.getVersion().getCreatedBy())
                    .isActive(documentVariable.getVersion().getIsActive())
                    .template(templateDto)
                    .build();
        }

        return DocumentVariableDto.builder()
                .id(documentVariable.getId())
                .templateVersionId(documentVariable.getVersion().getId())
                .templateVersion(templateVersionDto)
                .variableName(documentVariable.getVariableName())
                .description(documentVariable.getDescription())
                .sampleValue(documentVariable.getSampleValue())
                .build();
    }

    private DocumentVariable convertToEntity(DocumentVariableDto documentVariableDto) {
        DocumentVariable documentVariable = new DocumentVariable();
        documentVariable.setId(documentVariableDto.getId());

        if (documentVariableDto.getTemplateVersionId() != null) {
            DocumentTemplateVersion documentTemplateVersion = documentTemplateVersionRepository
                    .findById(documentVariableDto.getTemplateVersionId())
                    .orElseThrow(() -> new ForeignKeyViolationException("templateVersionId",
                            "Document Template Version not found with id: "
                                    + documentVariableDto.getTemplateVersionId()));
            documentVariable.setVersion(documentTemplateVersion);
        }

        documentVariable.setVariableName(documentVariableDto.getVariableName());
        documentVariable.setDescription(documentVariableDto.getDescription());
        documentVariable.setSampleValue(documentVariableDto.getSampleValue());

        return documentVariable;
    }
}
