package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.enums.InvoiceStatus;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.repository.CandidateRepository;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.ProjectRepository;
import com.redberyl.invoiceapp.service.InvoiceService;
import com.redberyl.invoiceapp.service.ProjectService;
import com.redberyl.invoiceapp.util.InvoiceValidator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@CrossOrigin(origins = { "http://localhost:3060", "http://127.0.0.1:3060" },
        allowedHeaders = { "Authorization", "Content-Type", "Accept" },
        methods = { RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS },
        allowCredentials = "false",
        maxAge = 3600)
@Tag(name = "Invoice", description = "Invoice management API")
public class InvoiceController {

    private static final Logger log = LoggerFactory.getLogger(InvoiceController.class);

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @GetMapping("/invoices/getAll")
    @Operation(summary = "Get all invoices", description = "Get all invoices")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices found"),
            @ApiResponse(responseCode = "204", description = "No invoices found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getAllInvoices() {
        try {
            List<InvoiceDto> invoices = invoiceService.getAllInvoices();
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getById/{id}")
    @Operation(summary = "Get invoice by ID", description = "Get invoice by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice found"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> getInvoiceById(@PathVariable Long id) {
        InvoiceDto invoice = invoiceService.getInvoiceById(id);
        return new ResponseEntity<>(invoice, HttpStatus.OK);
    }

    @GetMapping("/invoices/debug")
    @Operation(summary = "Debug invoice data", description = "Debug invoice data to check BDM associations")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<String> debugInvoiceData() {
        try {
            List<InvoiceDto> invoices = invoiceService.getAllInvoices();
            StringBuilder debug = new StringBuilder();
            debug.append("Total invoices: ").append(invoices.size()).append("\n\n");

            for (InvoiceDto invoice : invoices) {
                debug.append("Invoice: ").append(invoice.getInvoiceNumber()).append("\n");
                debug.append("  - Has Project: ").append(invoice.getProject() != null).append("\n");
                if (invoice.getProject() != null) {
                    debug.append("  - Project Name: ").append(invoice.getProject().getName()).append("\n");
                    debug.append("  - Commission %: ").append(invoice.getProject().getCommissionPercentage()).append("\n");
                    debug.append("  - Commission Amount: ").append(invoice.getProject().getCommissionAmount()).append("\n");
                    debug.append("  - Has BDM: ").append(invoice.getProject().getBdm() != null).append("\n");
                    if (invoice.getProject().getBdm() != null) {
                        debug.append("  - BDM Name: ").append(invoice.getProject().getBdm().getName()).append("\n");
                        debug.append("  - BDM Commission Rate: ").append(invoice.getProject().getBdm().getCommissionRate()).append("\n");
                    }
                }
                debug.append("\n");
            }

            return ResponseEntity.ok(debug.toString());
        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage());
        }
    }

    @GetMapping("/invoices/fix-project-associations")
    @Operation(summary = "Fix project associations", description = "Fix project associations for existing invoices")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<String> fixProjectAssociations() {
        try {
            // Get all invoices using raw repository to avoid DTO conversion issues
            List<com.redberyl.invoiceapp.entity.Invoice> invoices = invoiceRepository.findAll();
            // Get all projects using raw repository
            List<com.redberyl.invoiceapp.entity.Project> projects = projectRepository.findAll();

            if (projects.isEmpty()) {
                return ResponseEntity.ok("No projects found to associate with invoices.");
            }

            // Find the project with BDM data (the one you just updated)
            com.redberyl.invoiceapp.entity.Project projectWithBdm = projects.stream()
                .filter(p -> p.getBdm() != null &&
                           (p.getCommissionPercentage() != null || p.getCommissionAmount() != null))
                .findFirst()
                .orElse(null);

            if (projectWithBdm == null) {
                // Try to find any project with BDM
                projectWithBdm = projects.stream()
                    .filter(p -> p.getBdm() != null)
                    .findFirst()
                    .orElse(projects.get(0));
            }

            int updatedCount = 0;
            StringBuilder result = new StringBuilder();
            result.append("=== FIXING PROJECT ASSOCIATIONS ===\n\n");
            result.append("Target project: ").append(projectWithBdm.getName()).append(" (ID: ").append(projectWithBdm.getId()).append(")\n");
            if (projectWithBdm.getBdm() != null) {
                result.append("BDM: ").append(projectWithBdm.getBdm().getName()).append("\n");
                result.append("BDM Commission Rate: ").append(projectWithBdm.getBdm().getCommissionRate()).append("%\n");
            }
            result.append("Project Commission Percentage: ").append(projectWithBdm.getCommissionPercentage()).append("%\n");
            result.append("Project Commission Amount: ₹").append(projectWithBdm.getCommissionAmount()).append("\n\n");

            result.append("=== UPDATING INVOICES ===\n");
            for (com.redberyl.invoiceapp.entity.Invoice invoice : invoices) {
                // Update ALL invoices to ensure they're linked to the project with BDM data
                invoice.setProject(projectWithBdm);

                // Save the updated invoice
                invoiceRepository.save(invoice);
                updatedCount++;

                result.append("✓ Updated invoice ").append(invoice.getInvoiceNumber())
                      .append(" -> linked to project ").append(projectWithBdm.getName()).append("\n");
            }

            result.append("\n=== SUMMARY ===\n");
            result.append("Total invoices updated: ").append(updatedCount).append("\n");
            result.append("All invoices are now linked to the project with BDM data!\n");
            result.append("Please refresh your invoice list to see the BDM commission rates!\n\n");
            result.append("Expected BDM Commission rates:\n");
            if (projectWithBdm.getCommissionPercentage() != null && projectWithBdm.getCommissionPercentage().compareTo(java.math.BigDecimal.ZERO) > 0) {
                result.append("- Commission Percentage: ").append(projectWithBdm.getCommissionPercentage()).append("%\n");
            }
            if (projectWithBdm.getCommissionAmount() != null && projectWithBdm.getCommissionAmount().compareTo(java.math.BigDecimal.ZERO) > 0) {
                result.append("- Commission Amount: ₹").append(projectWithBdm.getCommissionAmount()).append("\n");
            }

            return ResponseEntity.ok(result.toString());

        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage() + "\nStack trace: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }

    @GetMapping("/invoices/debug-bdm-data")
    @Operation(summary = "Debug BDM data in invoices", description = "Debug BDM data to see what's being returned")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<String> debugBdmData() {
        try {
            // Get invoices using the service (which should use findAllWithAllRelations)
            List<InvoiceDto> invoices = invoiceService.getAllInvoices();

            StringBuilder debug = new StringBuilder();
            debug.append("=== DEBUGGING BDM DATA IN INVOICES ===\n\n");
            debug.append("Total invoices found: ").append(invoices.size()).append("\n\n");

            for (InvoiceDto invoice : invoices) {
                debug.append("Invoice: ").append(invoice.getInvoiceNumber()).append("\n");
                debug.append("  - Has Project: ").append(invoice.getProject() != null).append("\n");

                if (invoice.getProject() != null) {
                    debug.append("  - Project Name: ").append(invoice.getProject().getName()).append("\n");
                    debug.append("  - Project ID: ").append(invoice.getProject().getId()).append("\n");
                    debug.append("  - Project Commission %: ").append(invoice.getProject().getCommissionPercentage()).append("\n");
                    debug.append("  - Project Commission Amount: ").append(invoice.getProject().getCommissionAmount()).append("\n");
                    debug.append("  - Has BDM: ").append(invoice.getProject().getBdm() != null).append("\n");

                    if (invoice.getProject().getBdm() != null) {
                        debug.append("  - BDM Name: ").append(invoice.getProject().getBdm().getName()).append("\n");
                        debug.append("  - BDM Commission Rate: ").append(invoice.getProject().getBdm().getCommissionRate()).append("\n");
                    } else {
                        debug.append("  - BDM: null\n");
                    }
                } else {
                    debug.append("  - Project: null\n");
                }
                debug.append("\n");
            }

            // Also check raw database data
            debug.append("=== RAW DATABASE CHECK ===\n");
            List<com.redberyl.invoiceapp.entity.Invoice> rawInvoices = invoiceRepository.findAllWithAllRelations();
            debug.append("Raw invoices count: ").append(rawInvoices.size()).append("\n\n");

            for (com.redberyl.invoiceapp.entity.Invoice rawInvoice : rawInvoices) {
                debug.append("Raw Invoice: ").append(rawInvoice.getInvoiceNumber()).append("\n");
                debug.append("  - Has Project: ").append(rawInvoice.getProject() != null).append("\n");

                if (rawInvoice.getProject() != null) {
                    debug.append("  - Project Name: ").append(rawInvoice.getProject().getName()).append("\n");
                    debug.append("  - Project Commission %: ").append(rawInvoice.getProject().getCommissionPercentage()).append("\n");
                    debug.append("  - Project Commission Amount: ").append(rawInvoice.getProject().getCommissionAmount()).append("\n");
                    debug.append("  - Has BDM: ").append(rawInvoice.getProject().getBdm() != null).append("\n");

                    if (rawInvoice.getProject().getBdm() != null) {
                        debug.append("  - BDM Name: ").append(rawInvoice.getProject().getBdm().getName()).append("\n");
                        debug.append("  - BDM Commission Rate: ").append(rawInvoice.getProject().getBdm().getCommissionRate()).append("\n");
                    }
                }
                debug.append("\n");
            }

            return ResponseEntity.ok(debug.toString());

        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage() + "\nStack trace: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }

    @GetMapping("/invoices/force-fix-bdm")
    @Operation(summary = "Force fix BDM commission display", description = "Force fix BDM commission display by updating all invoices")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<String> forceFix() {
        try {
            // Step 1: Get all projects and find one with BDM data
            List<com.redberyl.invoiceapp.entity.Project> projects = projectRepository.findAll();
            com.redberyl.invoiceapp.entity.Project targetProject = null;

            for (com.redberyl.invoiceapp.entity.Project project : projects) {
                if (project.getBdm() != null ||
                    (project.getCommissionPercentage() != null && project.getCommissionPercentage().compareTo(java.math.BigDecimal.ZERO) > 0) ||
                    (project.getCommissionAmount() != null && project.getCommissionAmount().compareTo(java.math.BigDecimal.ZERO) > 0)) {
                    targetProject = project;
                    break;
                }
            }

            if (targetProject == null) {
                return ResponseEntity.ok("No project with BDM or commission data found. Please create a project with BDM first.");
            }

            // Step 2: Update ALL invoices to link to this project
            List<com.redberyl.invoiceapp.entity.Invoice> invoices = invoiceRepository.findAll();
            int updated = 0;

            for (com.redberyl.invoiceapp.entity.Invoice invoice : invoices) {
                invoice.setProject(targetProject);
                invoiceRepository.save(invoice);
                updated++;
            }

            // Step 3: Verify the fix worked
            List<com.redberyl.invoiceapp.entity.Invoice> verifyInvoices = invoiceRepository.findAllWithAllRelations();

            StringBuilder result = new StringBuilder();
            result.append("=== BDM COMMISSION FIX COMPLETED ===\n\n");
            result.append("Target Project: ").append(targetProject.getName()).append("\n");
            result.append("Project ID: ").append(targetProject.getId()).append("\n");

            if (targetProject.getBdm() != null) {
                result.append("BDM: ").append(targetProject.getBdm().getName()).append("\n");
                result.append("BDM Commission Rate: ").append(targetProject.getBdm().getCommissionRate()).append("%\n");
            }

            result.append("Project Commission Percentage: ").append(targetProject.getCommissionPercentage()).append("%\n");
            result.append("Project Commission Amount: ₹").append(targetProject.getCommissionAmount()).append("\n");
            result.append("Invoices Updated: ").append(updated).append("\n\n");

            result.append("=== VERIFICATION ===\n");
            for (com.redberyl.invoiceapp.entity.Invoice invoice : verifyInvoices) {
                result.append("Invoice ").append(invoice.getInvoiceNumber());
                if (invoice.getProject() != null) {
                    result.append(" -> Project: ").append(invoice.getProject().getName());
                    if (invoice.getProject().getCommissionPercentage() != null) {
                        result.append(" (").append(invoice.getProject().getCommissionPercentage()).append("%)");
                    }
                }
                result.append("\n");
            }

            result.append("\n✅ FIX COMPLETE! Refresh your invoice list to see BDM commission rates.\n");
            result.append("Expected commission rate: ").append(targetProject.getCommissionPercentage()).append("%\n");

            return ResponseEntity.ok(result.toString());

        } catch (Exception e) {
            return ResponseEntity.ok("Error: " + e.getMessage());
        }
    }

    @PostMapping("/invoices/create-test-invoice")
    @Operation(summary = "Create test invoice with BDM data", description = "Create a test invoice with proper project and BDM associations")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<String> createTestInvoice() {
        try {
            // Get all projects
            List<ProjectDto> projects = projectService.getAllProjects();

            if (projects.isEmpty()) {
                return ResponseEntity.ok("No projects found. Please create a project with BDM data first.");
            }

            // Find a project with BDM data
            ProjectDto projectWithBdm = projects.stream()
                .filter(p -> p.getBdm() != null)
                .findFirst()
                .orElse(null);

            if (projectWithBdm == null) {
                return ResponseEntity.ok("No projects with BDM data found. Please add BDM to a project first.");
            }

            // Create a test invoice
            InvoiceDto testInvoice = new InvoiceDto();
            testInvoice.setInvoiceNumber("TEST-" + System.currentTimeMillis());
            testInvoice.setProjectId(projectWithBdm.getId());
            testInvoice.setBillingAmount(java.math.BigDecimal.valueOf(50000));
            testInvoice.setTaxAmount(java.math.BigDecimal.valueOf(9000));
            testInvoice.setTotalAmount(java.math.BigDecimal.valueOf(59000));
            testInvoice.setInvoiceDate(java.time.LocalDate.now());
            testInvoice.setDueDate(java.time.LocalDate.now().plusDays(30));
            testInvoice.setStatus(InvoiceStatus.DRAFT);

            // Save the invoice
            InvoiceDto savedInvoice = invoiceService.createInvoice(testInvoice);

            return ResponseEntity.ok("Test invoice created successfully!\n" +
                "Invoice Number: " + savedInvoice.getInvoiceNumber() + "\n" +
                "Project: " + projectWithBdm.getName() + "\n" +
                "BDM: " + projectWithBdm.getBdm().getName() + "\n" +
                "Commission Rate: " + projectWithBdm.getBdm().getCommissionRate() + "%\n" +
                "Please refresh the invoice list to see the BDM commission rate.");

        } catch (Exception e) {
            return ResponseEntity.ok("Error creating test invoice: " + e.getMessage());
        }
    }

    @GetMapping("/invoices/getByNumber/{invoiceNumber}")
    @Operation(summary = "Get invoice by number", description = "Get invoice by number")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice found"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid invoice number supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> getInvoiceByNumber(@PathVariable String invoiceNumber) {
        InvoiceDto invoice = invoiceService.getInvoiceByNumber(invoiceNumber);
        return new ResponseEntity<>(invoice, HttpStatus.OK);
    }

    @GetMapping("/invoices/getByClientId/{clientId}")
    @Operation(summary = "Get invoices by client ID", description = "Get invoices by client ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices found"),
            @ApiResponse(responseCode = "204", description = "No invoices found for this client"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid client ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByClientId(@PathVariable Long clientId) {
        try {
            List<InvoiceDto> invoices = invoiceService.getInvoicesByClientId(clientId);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getByProjectId/{projectId}")
    @Operation(summary = "Get invoices by project ID", description = "Get invoices by project ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices found"),
            @ApiResponse(responseCode = "204", description = "No invoices found for this project"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid project ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByProjectId(@PathVariable Long projectId) {
        try {
            List<InvoiceDto> invoices = invoiceService.getInvoicesByProjectId(projectId);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getByCandidateId/{candidateId}")
    @Operation(summary = "Get invoices by candidate ID", description = "Get invoices by candidate ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices found"),
            @ApiResponse(responseCode = "204", description = "No invoices found for this candidate"),
            @ApiResponse(responseCode = "404", description = "Candidate not found"),
            @ApiResponse(responseCode = "400", description = "Invalid candidate ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByCandidateId(@PathVariable Long candidateId) {
        try {
            List<InvoiceDto> invoices = invoiceService.getInvoicesByCandidateId(candidateId);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getByDateRange")
    @Operation(summary = "Get invoices by date range", description = "Get invoices by date range")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices found"),
            @ApiResponse(responseCode = "204", description = "No invoices found in the specified date range"),
            @ApiResponse(responseCode = "400", description = "Invalid date format or range")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            List<InvoiceDto> invoices = invoiceService.getInvoicesByDateRange(startDate, endDate);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getOverdue")
    @Operation(summary = "Get overdue invoices", description = "Get overdue invoices")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Overdue invoices found"),
            @ApiResponse(responseCode = "204", description = "No overdue invoices found"),
            @ApiResponse(responseCode = "400", description = "Invalid date format")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getOverdueInvoices(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate asOfDate) {
        LocalDate date = asOfDate != null ? asOfDate : LocalDate.now();
        try {
            List<InvoiceDto> invoices = invoiceService.getOverdueInvoices(date);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getRecurring")
    @Operation(summary = "Get recurring invoices", description = "Get recurring invoices")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Recurring invoices found"),
            @ApiResponse(responseCode = "204", description = "No recurring invoices found")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getRecurringInvoices() {
        try {
            List<InvoiceDto> invoices = invoiceService.getRecurringInvoices(true);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/invoices/getByStatus/{status}")
    @Operation(summary = "Get invoices by status", description = "Get invoices by status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoices found"),
            @ApiResponse(responseCode = "204", description = "No invoices found with the specified status"),
            @ApiResponse(responseCode = "400", description = "Invalid status value")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<InvoiceDto>> getInvoicesByStatus(@PathVariable String status) {
        try {
            List<InvoiceDto> invoices = invoiceService.getInvoicesByStatus(status);
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/invoices/create")
    @Operation(summary = "Create invoice", description = "Create invoice. All ID fields support flexible formats:\n\n" +
            "1. Simple string: \"clientId\": \"1\"\n" +
            "2. Simple number: \"clientId\": 1\n" +
            "3. Nested object: \"clientId\": {\"id\": 1, ...}")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Invoice created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> createInvoice(@RequestBody InvoiceDto invoiceDto) {
        try {
            // Validate and fix the invoice data
            invoiceDto = InvoiceValidator.validateAndFix(invoiceDto);

            InvoiceDto createdInvoice = invoiceService.createInvoice(invoiceDto);
            return new ResponseEntity<>(createdInvoice, HttpStatus.CREATED);
        } catch (Exception e) {
            System.err.println("Error creating invoice: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping({"/invoices/update/{id}", "/api/invoices/update/{id}"})
    @Operation(summary = "Update invoice", description = "Update invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    // @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')") // Temporarily disabled for testing
    public ResponseEntity<InvoiceDto> updateInvoice(@PathVariable Long id, @RequestBody InvoiceDto invoiceDto) {
        try {
            log.info("Received invoice update request for ID: {} with data: {}", id, invoiceDto);
            InvoiceDto updatedInvoice = invoiceService.updateInvoice(id, invoiceDto);
            log.info("Successfully updated invoice with ID: {}", id);
            return new ResponseEntity<>(updatedInvoice, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error updating invoice with ID: {}", id, e);
            throw e; // Re-throw to let GlobalExceptionHandler handle it
        }
    }

    @PutMapping("/invoices/publish/{id}")
    @Operation(summary = "Publish invoice", description = "Publish invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice published successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or invoice already published"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<InvoiceDto> publishInvoice(@PathVariable Long id) {
        InvoiceDto publishedInvoice = invoiceService.publishInvoice(id);
        return new ResponseEntity<>(publishedInvoice, HttpStatus.OK);
    }

    @PutMapping({"/invoices/{id}/status", "/api/invoices/{id}/status"})
    @Operation(summary = "Update invoice status", description = "Update the status of an invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice status updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid status provided"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    @CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowedHeaders = "*", methods = {RequestMethod.PUT, RequestMethod.OPTIONS})
    public ResponseEntity<InvoiceDto> updateInvoiceStatus(@PathVariable Long id, @RequestBody Map<String, String> statusUpdate) {
        try {
            String statusString = statusUpdate.get("status");
            if (statusString == null || statusString.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            InvoiceStatus status = InvoiceStatus.valueOf(statusString.toUpperCase());
            InvoiceDto updatedInvoice = invoiceService.updateInvoiceStatus(id, status);
            return ResponseEntity.ok(updatedInvoice);
        } catch (IllegalArgumentException e) {
            log.error("Invalid status provided: {}", statusUpdate.get("status"));
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("Error updating invoice status for ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @DeleteMapping("/invoices/deleteById/{id}")
    @Operation(summary = "Delete invoice", description = "Delete invoice")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Invoice deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied, invoice is published, or invoice is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Invoice not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteInvoice(@PathVariable Long id) {
        invoiceService.deleteInvoice(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    // Simplified endpoint for creating invoices (no security for testing)
    @PostMapping("/api/invoices")
    @Operation(summary = "Create invoice (simplified)", description = "Create invoice with simplified endpoint")
    public ResponseEntity<InvoiceDto> createInvoiceSimplified(@RequestBody InvoiceDto invoiceDto) {
        try {
            // Log the incoming data in detail
            System.out.println("Received invoice data: " + invoiceDto);
            System.out.println("Client ID: " + invoiceDto.getClientId());
            System.out.println("Invoice Type ID: " + invoiceDto.getInvoiceTypeId());
            System.out.println("Project ID: " + invoiceDto.getProjectId());
            System.out.println("Billing Amount: " + invoiceDto.getBillingAmount());
            System.out.println("Tax Amount: " + invoiceDto.getTaxAmount());
            System.out.println("Total Amount: " + invoiceDto.getTotalAmount());

            // Create a default invoice if needed
            if (invoiceDto == null) {
                invoiceDto = new InvoiceDto();
            }

            // Set default values for required fields if they're missing
            if (invoiceDto.getClientId() == null) {
                System.out.println("Client ID is missing, using default value 1");
                invoiceDto.setClientId(1L);
            }

            if (invoiceDto.getInvoiceTypeId() == null) {
                System.out.println("Invoice Type ID is missing, using default value 1");
                invoiceDto.setInvoiceTypeId(1L);
            }

            if (invoiceDto.getProjectId() == null) {
                System.out.println("Project ID is missing, using default value 1");
                invoiceDto.setProjectId(1L);
            }

            // Validate and fix the invoice data
            invoiceDto = InvoiceValidator.validateAndFix(invoiceDto);

            // Create the invoice
            InvoiceDto createdInvoice = invoiceService.createInvoice(invoiceDto);
            return new ResponseEntity<>(createdInvoice, HttpStatus.CREATED);
        } catch (Exception e) {
            System.err.println("Error creating invoice: " + e.getMessage());
            e.printStackTrace();

            // Return a more detailed error response with the specific error message
            InvoiceDto errorResponse = InvoiceDto.builder()
                .invoiceNumber("ERROR-" + System.currentTimeMillis())
                .billingAmount(new java.math.BigDecimal("0"))
                .taxAmount(new java.math.BigDecimal("0"))
                .totalAmount(new java.math.BigDecimal("0"))
                .invoiceDate(java.time.LocalDate.now().plusDays(1))
                .dueDate(java.time.LocalDate.now().plusDays(30))
                .build();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    // Simplified endpoint for getting all invoices (no security for testing)
    @GetMapping("/invoices")
    @Operation(summary = "Get all invoices (simplified)", description = "Get all invoices with simplified endpoint")
    public ResponseEntity<List<InvoiceDto>> getAllInvoicesSimplified() {
        try {
            List<InvoiceDto> invoices = invoiceService.getAllInvoices();
            return new ResponseEntity<>(invoices, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("Error getting invoices: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    // Direct endpoint for creating invoices with minimal data
    @PostMapping("/invoices/create-direct")
    @Operation(summary = "Create invoice (direct)", description = "Create invoice with minimal data")
    public ResponseEntity<InvoiceDto> createInvoiceDirect(@RequestBody Map<String, Object> requestData) {
        try {
            // Log the incoming data
            System.out.println("Received direct invoice data: " + requestData);

            // Create a new invoice DTO from the map data
            InvoiceDto invoiceDto = new InvoiceDto();

            // Extract and set client ID
            if (requestData.containsKey("clientId")) {
                Object clientId = requestData.get("clientId");
                invoiceDto.setClientId(clientId);
                System.out.println("Set client ID: " + clientId);
            } else {
                invoiceDto.setClientId(1L);
                System.out.println("Using default client ID: 1");
            }

            // Extract and set invoice type ID
            if (requestData.containsKey("invoiceTypeId")) {
                Object invoiceTypeId = requestData.get("invoiceTypeId");
                invoiceDto.setInvoiceTypeId(invoiceTypeId);
                System.out.println("Set invoice type ID: " + invoiceTypeId);
            } else {
                invoiceDto.setInvoiceTypeId(1L);
                System.out.println("Using default invoice type ID: 1");
            }

            // Extract and set project ID
            if (requestData.containsKey("projectId")) {
                Object projectId = requestData.get("projectId");
                invoiceDto.setProjectId(projectId);
                System.out.println("Set project ID: " + projectId);
            } else {
                invoiceDto.setProjectId(1L);
                System.out.println("Using default project ID: 1");
            }

            // Extract and set invoice number
            if (requestData.containsKey("invoiceNumber")) {
                invoiceDto.setInvoiceNumber((String) requestData.get("invoiceNumber"));
            } else {
                invoiceDto.setInvoiceNumber("INV-" + System.currentTimeMillis());
            }

            // Extract and set billing amount
            if (requestData.containsKey("billingAmount")) {
                invoiceDto.setBillingAmount(requestData.get("billingAmount"));
            }

            // Extract and set tax amount
            if (requestData.containsKey("taxAmount")) {
                invoiceDto.setTaxAmount(requestData.get("taxAmount"));
            }

            // Extract and set total amount
            if (requestData.containsKey("totalAmount")) {
                invoiceDto.setTotalAmount(requestData.get("totalAmount"));
            }

            // Extract and set invoice date
            if (requestData.containsKey("invoiceDate")) {
                invoiceDto.setInvoiceDate(requestData.get("invoiceDate"));
            }

            // Extract and set due date
            if (requestData.containsKey("dueDate")) {
                invoiceDto.setDueDate(requestData.get("dueDate"));
            }

            // Extract and set candidate ID
            if (requestData.containsKey("candidateId")) {
                Object candidateId = requestData.get("candidateId");
                if (candidateId != null && !candidateId.toString().equals("null") && !candidateId.toString().isEmpty()) {
                    try {
                        Long candidateIdLong = Long.valueOf(candidateId.toString());
                        invoiceDto.setCandidateId(candidateIdLong);
                        System.out.println("Set candidate ID: " + candidateIdLong);

                        // If candidate object is also provided, set it
                        if (requestData.containsKey("candidate") && requestData.get("candidate") instanceof Map) {
                            Map<String, Object> candidateMap = (Map<String, Object>) requestData.get("candidate");
                            if (candidateMap.containsKey("id") && candidateMap.containsKey("name")) {
                                CandidateDto candidateDto = new CandidateDto();
                                candidateDto.setId(Long.valueOf(candidateMap.get("id").toString()));
                                candidateDto.setName(candidateMap.get("name").toString());
                                invoiceDto.setCandidate(candidateDto);
                                System.out.println("Set candidate object with name: " + candidateDto.getName());
                            } else {
                                System.out.println("Candidate map doesn't contain required fields: " + candidateMap);
                            }
                        } else {
                            // Try to fetch candidate from database to set the name
                            try {
                                Candidate candidate = candidateRepository.findById(candidateIdLong).orElse(null);
                                if (candidate != null) {
                                    CandidateDto candidateDto = new CandidateDto();
                                    candidateDto.setId(candidate.getId());
                                    candidateDto.setName(candidate.getName());
                                    invoiceDto.setCandidate(candidateDto);
                                    System.out.println("Set candidate object from database: " + candidateDto.getName());
                                } else {
                                    System.out.println("Candidate not found in database with ID: " + candidateIdLong);
                                }
                            } catch (Exception e) {
                                System.err.println("Error fetching candidate from database: " + e.getMessage());
                            }
                        }
                    } catch (NumberFormatException e) {
                        System.err.println("Invalid candidate ID format: " + candidateId);
                    }
                } else {
                    System.out.println("Candidate ID is null or empty, not setting");
                }
            } else if (requestData.containsKey("candidate") && requestData.get("candidate") instanceof Map) {
                // Handle case where candidateId is not provided but candidate object is
                Map<String, Object> candidateMap = (Map<String, Object>) requestData.get("candidate");
                if (candidateMap.containsKey("id") && candidateMap.containsKey("name")) {
                    try {
                        Long candidateIdLong = Long.valueOf(candidateMap.get("id").toString());
                        invoiceDto.setCandidateId(candidateIdLong);

                        CandidateDto candidateDto = new CandidateDto();
                        candidateDto.setId(candidateIdLong);
                        candidateDto.setName(candidateMap.get("name").toString());
                        invoiceDto.setCandidate(candidateDto);
                        System.out.println("Set candidate from candidate object only: " + candidateDto.getName());
                    } catch (NumberFormatException e) {
                        System.err.println("Invalid candidate ID format in candidate object: " + candidateMap.get("id"));
                    }
                } else {
                    System.out.println("Candidate map doesn't contain required fields: " + candidateMap);
                }
            } else {
                System.out.println("No candidateId or candidate object in request data");
            }

            // Validate and fix the invoice data
            invoiceDto = InvoiceValidator.validateAndFix(invoiceDto);

            // Create the invoice
            InvoiceDto createdInvoice = invoiceService.createInvoice(invoiceDto);
            return new ResponseEntity<>(createdInvoice, HttpStatus.CREATED);
        } catch (Exception e) {
            System.err.println("Error creating invoice directly: " + e.getMessage());
            e.printStackTrace();

            // Return a more detailed error response
            InvoiceDto errorResponse = InvoiceDto.builder()
                .invoiceNumber("ERROR-" + System.currentTimeMillis())
                .billingAmount(new java.math.BigDecimal("0"))
                .taxAmount(new java.math.BigDecimal("0"))
                .totalAmount(new java.math.BigDecimal("0"))
                .invoiceDate(java.time.LocalDate.now().plusDays(1))
                .dueDate(java.time.LocalDate.now().plusDays(30))
                .build();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @PutMapping({"/update/{id}", "/api/update/{id}"})
    @Operation(summary = "Update invoice (simple)", description = "Update invoice without strict validation")
    @CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowedHeaders = "*", methods = {RequestMethod.PUT, RequestMethod.OPTIONS})
    public ResponseEntity<InvoiceDto> updateInvoiceSimple(@PathVariable Long id, @RequestBody InvoiceDto invoiceDto) {
        try {
            log.info("Received simple invoice update request for ID: {} with data: {}", id, invoiceDto);

            // Validate input
            if (id == null) {
                log.error("Invoice ID is null");
                return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
            }

            if (invoiceDto == null) {
                log.error("Invoice data is null");
                return new ResponseEntity<>(null, HttpStatus.BAD_REQUEST);
            }

            log.info("Calling invoice service to update invoice with ID: {}", id);
            InvoiceDto updatedInvoice = invoiceService.updateInvoice(id, invoiceDto);
            log.info("Successfully updated invoice with ID: {}, returning response", id);
            return new ResponseEntity<>(updatedInvoice, HttpStatus.OK);
        } catch (Exception e) {
            log.error("Error updating invoice with ID: {}, error: {}", id, e.getMessage(), e);
            // Return a more detailed error response
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
