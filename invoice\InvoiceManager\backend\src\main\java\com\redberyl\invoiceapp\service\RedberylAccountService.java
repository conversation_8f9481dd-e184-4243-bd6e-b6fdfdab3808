package com.redberyl.invoiceapp.service;

import com.redberyl.invoiceapp.dto.RedberylAccountDto;

import java.util.List;

public interface RedberylAccountService {
    List<RedberylAccountDto> getAllRedberylAccounts();
    RedberylAccountDto getRedberylAccountById(Long id);
    RedberylAccountDto getRedberylAccountByAccountNo(String accountNo);
    RedberylAccountDto getRedberylAccountByGstn(String gstn);
    RedberylAccountDto getRedberylAccountByPanNo(String panNo);
    RedberylAccountDto createRedberylAccount(RedberylAccountDto redberylAccountDto);
    RedberylAccountDto updateRedberylAccount(Long id, RedberylAccountDto redberylAccountDto);
    void deleteRedberylAccount(Long id);
}
