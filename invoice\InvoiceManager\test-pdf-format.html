<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Format</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>PDF Format Test</h1>
    
    <div class="info">
        <strong>Test Purpose:</strong> This page tests the updated PDF generation format to match your reference screenshot.
    </div>
    
    <div class="test-container">
        <h2>Generate Test Invoice PDF</h2>
        <p>Click the button below to generate a test PDF with the exact format from your screenshot:</p>
        <button onclick="generateTestPDF()">Generate Test PDF</button>
    </div>

    <script>
        // Mock invoice data for testing
        const testInvoice = {
            id: 1,
            issueDate: '2025-07-21',
            joiningDate: '2025-07-21',
            amount: '₹50,000',
            candidate: 'Prathamesh Kadam',
            client: 'saurabh',
            project: 'Website Development',
            invoiceType: 'credit note',
            hsnCode: '998313'
        };

        // Copy the exact PDF generation functions from pdfUtils.ts
        function addRedBerylLogo(pdf) {
            const logoX = 140;
            const logoY = 20;
            
            pdf.setFillColor(233, 30, 99);
            pdf.circle(logoX, logoY, 4, 'F');
            
            pdf.setFillColor(21, 101, 192);
            pdf.circle(logoX + 10, logoY, 4, 'F');
            
            pdf.setFontSize(16);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(233, 30, 99);
            pdf.text('Red', logoX + 20, logoY + 2);
            
            pdf.setTextColor(21, 101, 192);
            pdf.text('Beryl', logoX + 40, logoY + 2);
            
            pdf.setFontSize(9);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(66, 66, 66);
            pdf.text('TECH SOLUTIONS', logoX + 20, logoY + 10);
            
            pdf.setFontSize(7);
            pdf.setFont('helvetica', 'italic');
            pdf.setTextColor(102, 102, 102);
            pdf.text('Integrates Business With Technology', logoX + 20, logoY + 16);
            
            pdf.setTextColor(0, 0, 0);
        }

        function addBillingTableExact(pdf, invoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount, joiningDate) {
            const tableX = 20;
            const tableWidth = 170;
            
            pdf.setLineWidth(0.5);
            pdf.rect(tableX, yPos, tableWidth, 15);
            
            pdf.line(tableX + 35, yPos, tableX + 35, yPos + 15);
            pdf.line(tableX + 65, yPos, tableX + 65, yPos + 15);
            pdf.line(tableX + 85, yPos, tableX + 85, yPos + 15);
            pdf.line(tableX + 115, yPos, tableX + 115, yPos + 15);
            pdf.line(tableX + 145, yPos, tableX + 145, yPos + 15);
            
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(8);
            pdf.text('Employee Name', tableX + 2, yPos + 9);
            pdf.text('Joining Date', tableX + 37, yPos + 9);
            pdf.text('Rate', tableX + 67, yPos + 9);
            pdf.text('Bill Amount', tableX + 87, yPos + 9);
            pdf.text('GST', tableX + 125, yPos + 9);
            pdf.text('Total Bill Amount', tableX + 147, yPos + 9);
            
            yPos += 15;
            pdf.rect(tableX + 115, yPos, 75, 10);
            
            pdf.line(tableX + 130, yPos, tableX + 130, yPos + 10);
            pdf.line(tableX + 145, yPos, tableX + 145, yPos + 10);
            pdf.line(tableX + 160, yPos, tableX + 160, yPos + 10);
            
            pdf.setFontSize(7);
            pdf.text('CGST @9%', tableX + 117, yPos + 6);
            pdf.text('SGST @9%', tableX + 132, yPos + 6);
            pdf.text('IGST @18%', tableX + 147, yPos + 6);
            
            yPos += 10;
            pdf.rect(tableX, yPos, tableWidth, 15);
            
            pdf.line(tableX + 35, yPos, tableX + 35, yPos + 15);
            pdf.line(tableX + 65, yPos, tableX + 65, yPos + 15);
            pdf.line(tableX + 85, yPos, tableX + 85, yPos + 15);
            pdf.line(tableX + 115, yPos, tableX + 115, yPos + 15);
            pdf.line(tableX + 130, yPos, tableX + 130, yPos + 15);
            pdf.line(tableX + 145, yPos, tableX + 145, yPos + 15);
            pdf.line(tableX + 160, yPos, tableX + 160, yPos + 15);
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            pdf.text('Prathamesh Kadam', tableX + 2, yPos + 9);
            pdf.text('21/07/2025', tableX + 37, yPos + 9);
            pdf.text('₹50,000.00', tableX + 67, yPos + 9);
            pdf.text('₹50,000.00', tableX + 87, yPos + 9);
            pdf.text('₹4,500.00', tableX + 117, yPos + 9);
            pdf.text('₹4,500.00', tableX + 132, yPos + 9);
            pdf.text('₹0.00', tableX + 147, yPos + 9);
            pdf.text('₹59,000.00', tableX + 162, yPos + 9);
        }

        function addPaymentAndSignatureSectionExact(pdf, yPos) {
            const tableX = 20;
            const tableWidth = 170;
            const leftColWidth = 85;
            const rightColWidth = 85;
            
            pdf.setLineWidth(0.5);
            pdf.rect(tableX, yPos, tableWidth, 50);
            
            pdf.line(tableX + leftColWidth, yPos, tableX + leftColWidth, yPos + 50);
            
            pdf.setFillColor(255, 255, 255);
            pdf.rect(tableX, yPos, leftColWidth, 12, 'F');
            pdf.rect(tableX, yPos, leftColWidth, 12);
            
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(9);
            pdf.setTextColor(0, 0, 0);
            pdf.text('Payment Information', tableX + leftColWidth/2, yPos + 8, { align: 'center' });
            
            pdf.setFillColor(0, 0, 0);
            pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12, 'F');
            pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12);
            pdf.setTextColor(255, 255, 255);
            pdf.text('Authorized Signatory', tableX + leftColWidth + rightColWidth/2, yPos + 8, { align: 'center' });
            
            pdf.setTextColor(0, 0, 0);
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            let leftYPos = yPos + 18;
            
            const paymentInfo = [
                'Bank Name: HDFC Bank',
                'Branch Name: MG Road Branch',
                'Account Name: Acme Corporation Pvt Ltd',
                'Account No: ***********',
                'IFSC Code: HDFC0001234',
                'Account Type: Current',
                'GSTIN: 29**********2Z5',
                'CIN: U12345KA2020PTC012345',
                'PAN No: **********'
            ];
            
            paymentInfo.forEach((info, index) => {
                pdf.text(info, tableX + 2, leftYPos + (index * 3.5));
            });
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            pdf.text('Thank you for doing business with us.', tableX + leftColWidth + 5, yPos + 25);
            pdf.text('For RedBeryl Tech Solutions Pvt. Ltd.', tableX + leftColWidth + 5, yPos + 45);
        }

        function generateTestPDF() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();
            
            const baseAmount = 50000;
            const cgstAmount = baseAmount * 0.09;
            const sgstAmount = baseAmount * 0.09;
            const totalAmount = baseAmount + cgstAmount + sgstAmount;
            
            const invoiceDate = '21/07/2025';
            const invoiceNumber = 'RB/24-25/INV-RB/25-26/001';
            const invoiceMonth = 'July 2025';
            const joiningDate = '21/07/2025';

            addRedBerylLogo(pdf);

            pdf.setFontSize(24);
            pdf.setFont('helvetica', 'bold');
            pdf.text('INVOICE', 105, 50, { align: 'center' });
            
            pdf.setLineWidth(1);
            pdf.line(85, 52, 125, 52);

            let yStart = 70;
            
            pdf.setFontSize(11);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Invoice Details :-', 20, yStart);
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
            
            let yPos = yStart + 12;
            pdf.text(`Invoice Date: ${invoiceDate}`, 20, yPos);
            yPos += 10;
            pdf.text(`Invoice No.: ${invoiceNumber}`, 20, yPos);
            yPos += 10;
            pdf.text(`Invoice Month: ${invoiceMonth}`, 20, yPos);
            yPos += 10;
            pdf.text(`Invoice For: credit note`, 20, yPos);
            yPos += 10;
            pdf.text(`HSN No.: 998313`, 20, yPos);
            yPos += 10;
            pdf.text(`Employee Name: Prathamesh Kadam`, 20, yPos);
            yPos += 10;
            pdf.text(`Employee Engagement Code: ENG-0020`, 20, yPos);

            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(11);
            pdf.text('Billed To :-', 120, yStart);
            
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
            yPos = yStart + 12;
            pdf.text('saurabh', 120, yPos);
            yPos += 10;
            pdf.text('Website Development', 120, yPos);
            yPos += 10;
            pdf.text('GST No:', 120, yPos);

            yPos = 160;
            addBillingTableExact(pdf, testInvoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount, joiningDate);

            yPos = 210;
            pdf.setFontSize(9);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(21, 101, 192);
            pdf.text('GST Type: Intra State (Maharashtra) - CGST (9%) + SGST (9%) = 18%', 20, yPos);

            yPos += 15;
            pdf.setTextColor(0, 0, 0);
            pdf.setFontSize(11);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Net Payable: ₹59,000.00 /- (Fifty Nine Thousand Only)', 20, yPos);

            yPos += 20;
            addPaymentAndSignatureSectionExact(pdf, yPos);

            pdf.setFontSize(9);
            pdf.setFont('helvetica', 'italic');
            pdf.setTextColor(0, 0, 0);
            pdf.text('Thank you for doing business with us.', 105, 280, { align: 'center' });

            pdf.save('test-invoice-format.pdf');
        }
    </script>
</body>
</html>
