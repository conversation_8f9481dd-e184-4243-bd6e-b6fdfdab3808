<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Format</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>PDF Format Test</h1>
    
    <div class="info">
        <strong>Test Purpose:</strong> This page tests the updated PDF generation format to match your reference screenshot.
    </div>
    
    <div class="test-container">
        <h2>Generate Test Invoice PDF</h2>
        <p>Click the button below to generate a test PDF with the exact format from your screenshot:</p>
        <button onclick="generateTestPDF()">Generate Test PDF</button>
    </div>

    <script>
        // Mock invoice data for testing
        const testInvoice = {
            id: 1,
            issueDate: '2025-07-21',
            joiningDate: '2025-07-21',
            amount: '₹50,000',
            candidate: 'Prathamesh Kadam',
            client: 'saurabh',
            project: 'Website Development',
            invoiceType: 'credit note',
            hsnCode: '998313'
        };

        // Copy the exact PDF generation functions from pdfUtils.ts (updated to match HTML template)
        function addRedBerylLogoFromTemplate(pdf) {
            const logoX = 150;
            const logoY = 15;

            pdf.setFillColor(233, 30, 99);
            pdf.circle(logoX, logoY, 3, 'F');

            pdf.setFillColor(21, 101, 192);
            pdf.circle(logoX + 8, logoY, 3, 'F');

            pdf.setFontSize(14);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(233, 30, 99);
            pdf.text('Red', logoX + 15, logoY + 2);

            pdf.setTextColor(21, 101, 192);
            pdf.text('Beryl', logoX + 32, logoY + 2);

            pdf.setFontSize(8);
            pdf.setFont('helvetica', 'bold');
            pdf.setTextColor(66, 66, 66);
            pdf.text('TECH SOLUTIONS', logoX + 15, logoY + 8);

            pdf.setFontSize(7);
            pdf.setFont('helvetica', 'italic');
            pdf.setTextColor(102, 102, 102);
            pdf.text('Integrates Business With Technology', logoX + 15, logoY + 13);

            pdf.setTextColor(0, 0, 0);
        }

        function addBillingTableFromTemplate(pdf, invoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount, joiningDate) {
            const tableX = 20;
            const tableWidth = 170;

            // Table header with gray background
            pdf.setFillColor(242, 242, 242);
            pdf.rect(tableX, yPos, tableWidth, 12, 'F');
            pdf.setLineWidth(0.5);
            pdf.rect(tableX, yPos, tableWidth, 12);

            // Column separators
            pdf.line(tableX + 35, yPos, tableX + 35, yPos + 12);
            pdf.line(tableX + 65, yPos, tableX + 65, yPos + 12);
            pdf.line(tableX + 85, yPos, tableX + 85, yPos + 12);
            pdf.line(tableX + 115, yPos, tableX + 115, yPos + 12);
            pdf.line(tableX + 145, yPos, tableX + 145, yPos + 12);

            // Header text
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(9);
            pdf.setTextColor(0, 0, 0);
            pdf.text('Employee Name', tableX + 2, yPos + 8);
            pdf.text('Joining Date', tableX + 37, yPos + 8);
            pdf.text('Rate', tableX + 67, yPos + 8);
            pdf.text('Bill Amount', tableX + 87, yPos + 8);
            pdf.text('GST', tableX + 125, yPos + 8);
            pdf.text('Total Bill Amount', tableX + 147, yPos + 8);

            // GST sub-header
            yPos += 12;
            pdf.setFillColor(242, 242, 242);
            pdf.rect(tableX + 115, yPos, 75, 10, 'F');
            pdf.rect(tableX + 115, yPos, 75, 10);

            pdf.line(tableX + 130, yPos, tableX + 130, yPos + 10);
            pdf.line(tableX + 145, yPos, tableX + 145, yPos + 10);
            pdf.line(tableX + 160, yPos, tableX + 160, yPos + 10);

            pdf.setFontSize(8);
            pdf.text('CGST @9%', tableX + 117, yPos + 6);
            pdf.text('SGST @9%', tableX + 132, yPos + 6);
            pdf.text('IGST @18%', tableX + 147, yPos + 6);

            // Data row
            yPos += 10;
            pdf.setFillColor(255, 255, 255);
            pdf.rect(tableX, yPos, tableWidth, 12, 'F');
            pdf.rect(tableX, yPos, tableWidth, 12);

            pdf.line(tableX + 35, yPos, tableX + 35, yPos + 12);
            pdf.line(tableX + 65, yPos, tableX + 65, yPos + 12);
            pdf.line(tableX + 85, yPos, tableX + 85, yPos + 12);
            pdf.line(tableX + 115, yPos, tableX + 115, yPos + 12);
            pdf.line(tableX + 130, yPos, tableX + 130, yPos + 12);
            pdf.line(tableX + 145, yPos, tableX + 145, yPos + 12);
            pdf.line(tableX + 160, yPos, tableX + 160, yPos + 12);

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(8);
            pdf.text('Prathamesh Kadam', tableX + 2, yPos + 8);
            pdf.text('21/07/2025', tableX + 37, yPos + 8);
            pdf.text('₹50,000.00', tableX + 67, yPos + 8);
            pdf.text('₹50,000.00', tableX + 87, yPos + 8);
            pdf.text('₹4,500.00', tableX + 117, yPos + 8);
            pdf.text('₹4,500.00', tableX + 132, yPos + 8);
            pdf.text('₹0.00', tableX + 147, yPos + 8);
            pdf.text('₹59,000.00', tableX + 162, yPos + 8);
        }

        function addGSTTypeSection(pdf, yPos) {
            // GST Type box with blue left border
            pdf.setFillColor(248, 249, 250);
            pdf.rect(20, yPos, 170, 12, 'F');

            // Blue left border
            pdf.setFillColor(0, 123, 255);
            pdf.rect(20, yPos, 4, 12, 'F');

            // GST Type text
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(10);
            pdf.setTextColor(0, 0, 0);
            pdf.text('GST Type:', 28, yPos + 8);

            pdf.setFont('helvetica', 'normal');
            pdf.setTextColor(40, 167, 69);
            pdf.text('Intra-State (Maharashtra) - CGST (9%) + SGST (9%) = 18%', 70, yPos + 8);

            pdf.setTextColor(0, 0, 0);
        }

        function addNetPayableSection(pdf, yPos, totalAmount) {
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(12);
            pdf.setTextColor(0, 0, 0);
            pdf.text(`Net Payable: ₹${totalAmount.toLocaleString('en-IN')}.00 /- (Fifty Nine Thousand Only)`, 20, yPos);
        }

        function addPaymentAndSignatureSectionFromTemplate(pdf, yPos) {
            const tableX = 20;
            const tableWidth = 170;
            const leftColWidth = 85;
            const rightColWidth = 85;

            pdf.setLineWidth(0.5);
            pdf.rect(tableX, yPos, tableWidth, 60);

            pdf.line(tableX + leftColWidth, yPos, tableX + leftColWidth, yPos + 60);

            // Headers
            pdf.setFillColor(255, 255, 255);
            pdf.rect(tableX, yPos, leftColWidth, 12, 'F');
            pdf.rect(tableX, yPos, leftColWidth, 12);

            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(10);
            pdf.setTextColor(0, 0, 0);
            pdf.text('Payment Information', tableX + leftColWidth/2, yPos + 8, { align: 'center' });

            pdf.setFillColor(255, 255, 255);
            pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12, 'F');
            pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12);
            pdf.text('Authorized Signatory', tableX + leftColWidth + rightColWidth/2, yPos + 8, { align: 'center' });

            // Payment information content
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
            let leftYPos = yPos + 20;

            const paymentInfo = [
                'Bank Name: HDFC Bank Ltd.',
                'Branch Name: Destination Centre, Magarpatta, Pune',
                'Account Name: RedBeryl Tech Solutions Pvt Ltd.',
                'Account No: **************',
                'IFSC Code: HDFC0000486',
                'Account Type: Current Account',
                'GSTN: 27**********1Z5',
                'CIN: U72900PN2022PTC213381',
                'PAN No: **********'
            ];

            paymentInfo.forEach((info, index) => {
                pdf.text(info, tableX + 2, leftYPos + (index * 4));
            });

            // Authorized signatory content
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(9);
            pdf.text('For RedBeryl Tech Solutions Pvt. Ltd.', tableX + leftColWidth + 5, yPos + 50);
        }

        function generateTestPDF() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();

            const baseAmount = 50000;
            const cgstAmount = baseAmount * 0.09;
            const sgstAmount = baseAmount * 0.09;
            const totalAmount = baseAmount + cgstAmount + sgstAmount;

            const invoiceDate = '21/07/2025';
            const invoiceNumber = 'RB/24-25/INV-RB/25-26/001';
            const invoiceMonth = 'July 2025';
            const joiningDate = '21/07/2025';

            // Add RedBeryl logo - top right corner
            addRedBerylLogoFromTemplate(pdf);

            // INVOICE header - centered and underlined (matching HTML template)
            pdf.setFontSize(18);
            pdf.setFont('helvetica', 'bold');
            pdf.text('INVOICE', 105, 40, { align: 'center' });

            // Add underline for INVOICE
            pdf.setLineWidth(0.5);
            pdf.line(85, 42, 125, 42);

            // Two-column layout for Invoice Details and Billed To (matching HTML template)
            let yStart = 60;

            // Left column - Invoice Details
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Invoice Details :-', 20, yStart);

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);

            let yPos = yStart + 10;
            pdf.text(`Invoice Date: ${invoiceDate}`, 20, yPos);
            yPos += 8;
            pdf.text(`Invoice No.: ${invoiceNumber}`, 20, yPos);
            yPos += 8;
            pdf.text(`Invoice Month: ${invoiceMonth}`, 20, yPos);
            yPos += 8;
            pdf.text(`Invoice For: credit note`, 20, yPos);
            yPos += 8;
            pdf.text(`HSN No.: 998313`, 20, yPos);
            yPos += 8;
            pdf.text(`Employee Name: Prathamesh Kadam`, 20, yPos);
            yPos += 8;
            pdf.text(`Employee Engagement Code: ENG-0020`, 20, yPos);

            // Right column - Billed To
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(12);
            pdf.text('Billed To :-', 120, yStart);

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            yPos = yStart + 10;
            pdf.text('saurabh', 120, yPos);
            yPos += 8;
            pdf.text('Website Development', 120, yPos);
            yPos += 8;
            pdf.text('GST No:', 120, yPos);

            // Billing table - exact format from HTML template
            yPos = 140;
            addBillingTableFromTemplate(pdf, testInvoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount, joiningDate);

            // GST Type information (matching HTML template styling)
            yPos = 200;
            addGSTTypeSection(pdf, yPos);

            // Net Payable (matching HTML template)
            yPos += 15;
            addNetPayableSection(pdf, yPos, totalAmount);

            // Payment Information and Authorized Signatory tables (matching HTML template)
            yPos += 20;
            addPaymentAndSignatureSectionFromTemplate(pdf, yPos);

            // Footer (matching HTML template)
            pdf.setFontSize(10);
            pdf.setFont('helvetica', 'normal');
            pdf.setTextColor(0, 0, 0);
            pdf.text('Thank you for doing business with us.', 105, 280, { align: 'center' });

            pdf.save('test-invoice-html-template-format.pdf');
        }
    </script>
</body>
</html>
