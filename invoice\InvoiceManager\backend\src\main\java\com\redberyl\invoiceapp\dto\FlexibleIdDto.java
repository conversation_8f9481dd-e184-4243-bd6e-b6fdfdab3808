package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.redberyl.invoiceapp.util.IdConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Base class for DTOs that need to handle flexible ID formats.
 * This allows API endpoints to accept IDs in different formats:
 * - Simple string: "1"
 * - Simple number: 1
 * - Nested object: {"id": 1, ...}
 */
@Getter
@Setter
public abstract class FlexibleIdDto {

    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    @Schema(description = "Creation timestamp", accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime createdAt;

    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    @Schema(description = "Last update timestamp", accessMode = Schema.AccessMode.READ_ONLY)
    private LocalDateTime updatedAt;

    /**
     * Extracts a Long ID from the flexible ID field.
     *
     * @param idObject The object that contains the ID
     * @return The extracted Long ID
     */
    @JsonIgnore
    protected Long extractId(Object idObject) {
        return IdConverter.extractId(idObject);
    }

    /**
     * Safely extracts a Long ID from the flexible ID field.
     * Returns null if the ID cannot be extracted.
     *
     * @param idObject The object that contains the ID
     * @return The extracted Long ID or null if extraction fails
     */
    @JsonIgnore
    protected Long extractIdSafely(Object idObject) {
        return IdConverter.extractIdSafely(idObject);
    }
}
