package com.redberyl.invoiceapp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class RedberylAccountDto extends BaseDto {
    private Long id;
    private String glCode;
    private String costCenter;
    private String accountingNotes;
    private String bankName;
    private String branchName;
    private String accountName;
    private String accountNo;
    private String ifscCode;
    private String accountType;
    private String gstn;
    private String cin;
    private String panNo;
}
