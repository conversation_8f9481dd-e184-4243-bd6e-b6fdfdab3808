package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DocumentVariableDto;
import com.redberyl.invoiceapp.service.DocumentVariableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/document-variables")
@Tag(name = "Document Variable", description = "Document Variable management API")
public class DocumentVariableController {

    @Autowired
    private DocumentVariableService documentVariableService;

    @GetMapping
    @Operation(summary = "Get all document variables", description = "Retrieve a list of all document variables")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DocumentVariableDto>> getAllDocumentVariables() {
        List<DocumentVariableDto> documentVariables = documentVariableService.getAllDocumentVariables();
        return new ResponseEntity<>(documentVariables, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get document variable by ID", description = "Retrieve a document variable by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentVariableDto> getDocumentVariableById(@PathVariable Long id) {
        DocumentVariableDto documentVariable = documentVariableService.getDocumentVariableById(id);
        return new ResponseEntity<>(documentVariable, HttpStatus.OK);
    }

    @GetMapping("/template-version/{templateVersionId}")
    @Operation(summary = "Get document variables by template version ID", description = "Retrieve all document variables for a specific template version")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DocumentVariableDto>> getDocumentVariablesByTemplateVersionId(@PathVariable Long templateVersionId) {
        List<DocumentVariableDto> documentVariables = documentVariableService.getDocumentVariablesByTemplateVersionId(templateVersionId);
        return new ResponseEntity<>(documentVariables, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create document variable", description = "Create a new document variable")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentVariableDto> createDocumentVariable(@Valid @RequestBody DocumentVariableDto documentVariableDto) {
        DocumentVariableDto createdDocumentVariable = documentVariableService.createDocumentVariable(documentVariableDto);
        return new ResponseEntity<>(createdDocumentVariable, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update document variable", description = "Update an existing document variable")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DocumentVariableDto> updateDocumentVariable(@PathVariable Long id, @Valid @RequestBody DocumentVariableDto documentVariableDto) {
        DocumentVariableDto updatedDocumentVariable = documentVariableService.updateDocumentVariable(id, documentVariableDto);
        return new ResponseEntity<>(updatedDocumentVariable, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete document variable", description = "Delete a document variable by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteDocumentVariable(@PathVariable Long id) {
        documentVariableService.deleteDocumentVariable(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
