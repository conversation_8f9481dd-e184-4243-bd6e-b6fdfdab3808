/**
 * Service utilities for handling API endpoints with dynamic IP configuration
 */

import { getApiBaseUrl, getTargetIP, getBackendPort } from './ipUtils';

/**
 * Replace hardcoded localhost URLs with the target IP
 * @param url The URL to process
 * @returns The URL with target IP instead of localhost
 */
export const replaceLocalhostWithTargetIP = (url: string): string => {
  const targetIP = getTargetIP();
  const backendPort = getBackendPort();
  
  // Replace localhost:8091 with target IP
  if (url.includes('localhost:8091')) {
    return url.replace('localhost:8091', `${targetIP}:${backendPort}`);
  }
  
  // Replace 127.0.0.1:8091 with target IP
  if (url.includes('127.0.0.1:8091')) {
    return url.replace('127.0.0.1:8091', `${targetIP}:${backendPort}`);
  }
  
  return url;
};

/**
 * Get API endpoints with target IP configuration
 * @param endpoints Array of endpoint URLs
 * @returns Array of endpoints with target IP
 */
export const getTargetIPEndpoints = (endpoints: string[]): string[] => {
  return endpoints.map(endpoint => {
    // If it's a relative URL, keep it as is (will be proxied)
    if (endpoint.startsWith('/')) {
      return endpoint;
    }
    
    // If it's an absolute URL with localhost, replace with target IP
    return replaceLocalhostWithTargetIP(endpoint);
  });
};

/**
 * Create a direct API URL using the target IP
 * @param endpoint The API endpoint
 * @returns Full URL with target IP
 */
export const createDirectAPIUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${formattedEndpoint}`;
};

/**
 * Get standard endpoint configurations for services
 * @param serviceName The name of the service (e.g., 'clients', 'projects')
 * @param useAuth Whether to include authenticated endpoints
 * @returns Array of endpoint configurations
 */
export const getStandardEndpoints = (serviceName: string, useAuth: boolean = true): string[] => {
  const targetIP = getTargetIP();
  const backendPort = getBackendPort();
  
  const endpoints = [
    `http://${targetIP}:${backendPort}/${serviceName}`,        // Direct to public controller
    `/${serviceName}`,                                         // Proxied public controller
  ];
  
  if (useAuth) {
    endpoints.push(
      `http://${targetIP}:${backendPort}/api/${serviceName}`,  // Direct to main controller
      `/api/${serviceName}`                                    // Proxied main controller
    );
  }
  
  return endpoints;
};

/**
 * Get endpoints for a specific service operation
 * @param serviceName The service name
 * @param operation The operation (e.g., 'getAll', 'getById')
 * @param id Optional ID for operations that require it
 * @returns Array of endpoint URLs
 */
export const getServiceEndpoints = (
  serviceName: string, 
  operation: string, 
  id?: number | string
): string[] => {
  const targetIP = getTargetIP();
  const backendPort = getBackendPort();
  
  const baseEndpoints = [
    `http://${targetIP}:${backendPort}/${serviceName}`,
    `http://${targetIP}:${backendPort}/api/${serviceName}`,
    `/${serviceName}`,
    `/api/${serviceName}`
  ];
  
  if (operation === 'getAll') {
    return [
      ...baseEndpoints,
      `http://${targetIP}:${backendPort}/${serviceName}/getAll`,
      `http://${targetIP}:${backendPort}/api/${serviceName}/getAll`,
      `/${serviceName}/getAll`,
      `/api/${serviceName}/getAll`
    ];
  }
  
  if (operation === 'getById' && id !== undefined) {
    return [
      `http://${targetIP}:${backendPort}/${serviceName}/${id}`,
      `http://${targetIP}:${backendPort}/api/${serviceName}/${id}`,
      `http://${targetIP}:${backendPort}/${serviceName}/getById/${id}`,
      `http://${targetIP}:${backendPort}/api/${serviceName}/getById/${id}`,
      `/${serviceName}/${id}`,
      `/api/${serviceName}/${id}`,
      `/${serviceName}/getById/${id}`,
      `/api/${serviceName}/getById/${id}`
    ];
  }
  
  if (operation === 'delete' && id !== undefined) {
    return [
      `http://${targetIP}:${backendPort}/${serviceName}/${id}`,
      `http://${targetIP}:${backendPort}/api/${serviceName}/${id}`,
      `http://${targetIP}:${backendPort}/${serviceName}/deleteById/${id}`,
      `http://${targetIP}:${backendPort}/api/${serviceName}/deleteById/${id}`,
      `/${serviceName}/${id}`,
      `/api/${serviceName}/${id}`,
      `/${serviceName}/deleteById/${id}`,
      `/api/${serviceName}/deleteById/${id}`
    ];
  }
  
  return baseEndpoints;
};

/**
 * Create fetch options with proper headers
 * @param method HTTP method
 * @param body Request body (optional)
 * @param includeAuth Whether to include authentication headers
 * @returns Fetch options object
 */
export const createFetchOptions = (
  method: string = 'GET',
  body?: any,
  includeAuth: boolean = true
): RequestInit => {
  const headers: Record<string, string> = {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  };
  
  if (includeAuth) {
    headers['Authorization'] = 'Basic ' + btoa('admin:admin123');
  }
  
  const options: RequestInit = {
    method,
    headers
  };
  
  if (body && method !== 'GET') {
    options.body = JSON.stringify(body);
  }
  
  return options;
};
