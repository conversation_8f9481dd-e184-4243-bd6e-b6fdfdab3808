����   = F
      java/lang/Object <init> ()V	  	 
   TaxRateExample$TaxRate id Ljava/lang/Long;	     rate Ljava/math/BigDecimal;	     
effectiveFrom Ljava/time/LocalDate;	     effectiveTo	     taxType LTaxRateExample$TaxType; RuntimeVisibleAnnotations /Lcom/fasterxml/jackson/annotation/JsonProperty; value Code LineNumberTable getId ()Ljava/lang/Long; setId (Ljava/lang/Long;)V getRate ()Ljava/math/BigDecimal; setRate (Ljava/math/BigDecimal;)V getEffectiveFrom ()Ljava/time/LocalDate; setEffectiveFrom (Ljava/time/LocalDate;)V getEffectiveTo setEffectiveTo 
getTaxType ()LTaxRateExample$TaxType; 
setTaxType (LTaxRateExample$TaxType;)V 
SourceFile TaxRateExample.java 3Lcom/fasterxml/jackson/annotation/JsonIdentityInfo; 	generator GLcom/fasterxml/jackson/annotation/ObjectIdGenerators$PropertyGenerator; property NestHost ; TaxRateExample InnerClasses TaxRate ? TaxRateExample$TaxType TaxType B Ecom/fasterxml/jackson/annotation/ObjectIdGenerators$PropertyGenerator D 3com/fasterxml/jackson/annotation/ObjectIdGenerators PropertyGenerator !                                     s               *� �            (  ! "          *� �            2  # $          *+� �            3  % &          *� 
�            5  ' (          *+� 
�            6  ) *          *� �            8  + ,          *+� �            9  - *          *� �            ;  . ,          *+� �            <  / 0          *� �            >  1 2          *+� �            ?  3    4      5  6c 7 8s  9    : <      : = 	 > : @ 	 A C E	