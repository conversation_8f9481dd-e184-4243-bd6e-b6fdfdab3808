package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.CommunicationDto;
import com.redberyl.invoiceapp.service.CommunicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/communications")
@Tag(name = "Communications", description = "API for managing communications")
public class CommunicationController {

    @Autowired
    private CommunicationService communicationService;

    @GetMapping("/communications/getAll")
    @Operation(summary = "Get all communications")
    public ResponseEntity<List<CommunicationDto>> getAllCommunications() {
        return ResponseEntity.ok(communicationService.getAllCommunications());
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get communication by ID")
    public ResponseEntity<CommunicationDto> getCommunicationById(@PathVariable Long id) {
        return ResponseEntity.ok(communicationService.getCommunicationById(id));
    }

    @GetMapping("/client/{clientId}")
    @Operation(summary = "Get communications by client ID")
    public ResponseEntity<List<CommunicationDto>> getCommunicationsByClientId(@PathVariable Long clientId) {
        return ResponseEntity.ok(communicationService.getCommunicationsByClientId(clientId));
    }

    @GetMapping("/lead/{leadId}")
    @Operation(summary = "Get communications by lead ID")
    public ResponseEntity<List<CommunicationDto>> getCommunicationsByLeadId(@PathVariable Long leadId) {
        return ResponseEntity.ok(communicationService.getCommunicationsByLeadId(leadId));
    }

    @GetMapping("/deal/{dealId}")
    @Operation(summary = "Get communications by deal ID")
    public ResponseEntity<List<CommunicationDto>> getCommunicationsByDealId(@PathVariable Long dealId) {
        return ResponseEntity.ok(communicationService.getCommunicationsByDealId(dealId));
    }

    @PostMapping
    @Operation(summary = "Create a new communication")
    public ResponseEntity<CommunicationDto> createCommunication(@Valid @RequestBody CommunicationDto communicationDto) {
        CommunicationDto createdCommunication = communicationService.createCommunication(communicationDto);
        return new ResponseEntity<>(createdCommunication, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update an existing communication")
    public ResponseEntity<CommunicationDto> updateCommunication(
            @PathVariable Long id,
            @Valid @RequestBody CommunicationDto communicationDto) {
        return ResponseEntity.ok(communicationService.updateCommunication(id, communicationDto));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a communication")
    public ResponseEntity<Void> deleteCommunication(@PathVariable Long id) {
        communicationService.deleteCommunication(id);
        return ResponseEntity.noContent().build();
    }
}
