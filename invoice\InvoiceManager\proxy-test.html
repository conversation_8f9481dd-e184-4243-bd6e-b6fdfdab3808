<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Proxy Test</h1>
    <p>This tool tests if the proxy in vite.config.ts is working correctly.</p>
    
    <div class="container">
        <h2>Test Proxy</h2>
        <button id="testProxy">Test Proxy</button>
        <button id="testDirect">Test Direct</button>
        <button id="clearResults">Clear Results</button>
        <div id="result" class="result"></div>
    </div>
    
    <script>
        document.getElementById('testProxy').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing proxy...</p>';
            
            try {
                // Use the proxy URL (relative path)
                const response = await fetch('/api/auth/test-roles', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });
                
                resultDiv.innerHTML += `<p>Proxy response status: ${response.status} ${response.statusText}</p>`;
                
                // Log all response headers for debugging
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                resultDiv.innerHTML += `<p>Response headers: ${JSON.stringify(headers)}</p>`;
                
                // Try to parse the response body
                try {
                    const text = await response.text();
                    resultDiv.innerHTML += `<p>Response body: ${text}</p>`;
                } catch (parseError) {
                    resultDiv.innerHTML += `<p class="error">Error parsing response: ${parseError.message}</p>`;
                }
                
                resultDiv.innerHTML += `<p class="success">Proxy test completed</p>`;
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">Error: ${error.message}</p>`;
            }
        });
        
        document.getElementById('testDirect').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing direct connection...</p>';
            
            try {
                // Use the direct URL
                const response = await fetch('http://192.168.1.11:8091/api/auth/test-roles', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Origin': window.location.origin
                    },
                    mode: 'cors',
                    credentials: 'include'
                });
                
                resultDiv.innerHTML += `<p>Direct response status: ${response.status} ${response.statusText}</p>`;
                
                // Log all response headers for debugging
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });
                resultDiv.innerHTML += `<p>Response headers: ${JSON.stringify(headers)}</p>`;
                
                // Try to parse the response body
                try {
                    const text = await response.text();
                    resultDiv.innerHTML += `<p>Response body: ${text}</p>`;
                } catch (parseError) {
                    resultDiv.innerHTML += `<p class="error">Error parsing response: ${parseError.message}</p>`;
                }
                
                resultDiv.innerHTML += `<p class="success">Direct test completed</p>`;
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">Error: ${error.message}</p>`;
            }
        });
        
        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('result').innerHTML = '';
        });
    </script>
</body>
</html>
