package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.TaxRateDto;
import com.redberyl.invoiceapp.service.TaxRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;

@RestController
@Tag(name = "Fixed Tax Rate", description = "Simplified Tax Rate creation API")
public class FixedTaxRateController {

    @Autowired
    private TaxRateService taxRateService;

    @PostMapping("/fixed-tax-rates/create")
    @Operation(summary = "Create tax rate with parameters", description = "Create a new tax rate using simple URL parameters instead of JSON")
    public ResponseEntity<TaxRateDto> createTaxRate(
            @RequestParam Long taxTypeId,
            @RequestParam BigDecimal rate,
            @RequestParam LocalDate effectiveFrom,
            @RequestParam(required = false) LocalDate effectiveTo) {

        TaxRateDto taxRateDto = new TaxRateDto();
        taxRateDto.setTaxTypeId(taxTypeId);
        taxRateDto.setRate(rate);
        taxRateDto.setEffectiveFrom(effectiveFrom);
        taxRateDto.setEffectiveTo(effectiveTo);

        TaxRateDto createdTaxRate = taxRateService.createTaxRate(taxRateDto);
        return new ResponseEntity<>(createdTaxRate, HttpStatus.CREATED);
    }
}
