import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, Printer, ReceiptIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { invoiceGenerationService } from "@/services/invoiceGenerationService";
import InvoiceOneDriveButton from "./InvoiceOneDriveButton";

// Flag to track if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

interface InvoiceDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: {
    id: string;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
  } | null;
  onEdit: (id: string) => void;
}

const InvoiceDetailsDialog: React.FC<InvoiceDetailsProps> = ({
  open,
  onOpenChange,
  invoice,
  onEdit,
}) => {
  const [isDownloading, setIsDownloading] = useState(false);

  // If no invoice is provided, return null
  if (!invoice) {
    console.log("No invoice data provided to InvoiceDetailsDialog");
    return null;
  }

  // Log the invoice data for debugging
  console.log("InvoiceDetailsDialog: Invoice data:", invoice);

  // Ensure all required fields are present
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "Unknown Client",
    project: invoice.project || "Unknown Project",
    candidate: invoice.candidate || "-",
    invoiceType: invoice.invoiceType || "Standard",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹0.00",
    tax: invoice.tax || "₹0.00",
    total: invoice.total || "₹0.00",
    issueDate: invoice.issueDate || new Date().toISOString().split('T')[0],
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || ""
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "draft":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Reference to the invoice PDF template
  const invoicePdfRef = useRef<HTMLDivElement>(null);

  const handleDownload = async () => {
    setIsDownloading(true);

    try {
      // Make sure we're in a browser environment
      if (!isBrowser) {
        console.error('Cannot generate PDF in non-browser environment');
        toast.error("PDF generation is only available in browser environments");
        setIsDownloading(false);
        return;
      }

      console.log("InvoiceDetailsDialog: Downloading invoice with data:", safeInvoice);

      // Create a temporary div to render the invoice template
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      tempDiv.style.width = '800px'; // Set a fixed width for better PDF quality
      document.body.appendChild(tempDiv);

      // Import and render the invoice template
      const { default: InvoicePdfTemplate } = await import('./InvoicePdfTemplate');
      const root = document.createElement('div');
      tempDiv.appendChild(root);

      // Use ReactDOM to render the template
      const ReactDOM = await import('react-dom/client');
      const reactRoot = ReactDOM.createRoot(root);
      reactRoot.render(<InvoicePdfTemplate invoice={safeInvoice} />);

      // Wait for the rendering and SVG to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      try {
        // Use html2canvas to capture the rendered template
        const canvas = await html2canvas(root, {
          scale: 2, // Higher scale for better quality
          useCORS: true,
          logging: false,
          backgroundColor: '#ffffff'
        });

        // Create a new PDF document
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        });

        // Calculate the PDF dimensions
        const imgWidth = 210; // A4 width in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // Add the canvas as an image to the PDF
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

        // Save the PDF
        pdf.save(`Invoice-${safeInvoice.id}.pdf`);

        toast.success("Invoice downloaded successfully as PDF");
      } catch (canvasError) {
        console.error("Error generating canvas:", canvasError);
        throw new Error("Failed to generate PDF: " + (canvasError instanceof Error ? canvasError.message : String(canvasError)));
      } finally {
        // Clean up
        document.body.removeChild(tempDiv);
      }
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to download invoice as PDF. Please try again.");

      // Show more detailed error message
      if (error instanceof Error) {
        console.error("PDF generation error details:", error.message);
        toast.error(`PDF generation error: ${error.message}`);
      }
    } finally {
      setIsDownloading(false);
    }
  };

  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrint = async () => {
    setIsPrinting(true);

    try {
      // Make sure we're in a browser environment
      if (!isBrowser) {
        console.error('Cannot print in non-browser environment');
        toast.error("Printing is only available in browser environments");
        setIsPrinting(false);
        return;
      }

      // Create a new window for printing
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        throw new Error('Pop-up blocked. Please allow pop-ups for this site to enable printing.');
      }

      // Write the HTML structure to the new window
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Print Invoice ${safeInvoice.id}</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: white;
            }
            @media print {
              body {
                margin: 0;
                padding: 10px;
              }
              .no-print {
                display: none;
              }
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
              padding: 10px;
              background: #f5f5f5;
              border-radius: 5px;
            }
            .print-button {
              background: #007bff;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 5px;
              cursor: pointer;
              margin-right: 10px;
            }
            .print-button:hover {
              background: #0056b3;
            }
            .close-button {
              background: #6c757d;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 5px;
              cursor: pointer;
            }
            .close-button:hover {
              background: #545b62;
            }
          </style>
        </head>
        <body>
          <div class="print-header no-print">
            <h2>Invoice ${safeInvoice.id} - Print Preview</h2>
            <button class="print-button" onclick="window.print()">🖨️ Print</button>
            <button class="close-button" onclick="window.close()">✕ Close</button>
          </div>
          <div id="invoice-content"></div>
        </body>
        </html>
      `);

      // Close the document to finish loading
      printWindow.document.close();

      // Wait for the window to load
      await new Promise(resolve => {
        printWindow.onload = resolve;
        // Fallback timeout
        setTimeout(resolve, 1000);
      });

      // Create the invoice content HTML
      const invoiceHTML = `
        <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; background: white; color: #333; font-size: 12px; line-height: 1.4;">
          <!-- Header with Logo -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px;">
            <div style="flex: 1;">
              <svg width="320" height="100" viewBox="0 0 800 250" style="margin-bottom: 10px;">
                <rect width="800" height="250" fill="white"/>
                <g transform="translate(0, 50)">
                  <text x="0" y="40" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#d32f2f">RedBeryl</text>
                  <text x="250" y="40" font-family="Arial, sans-serif" font-size="32" fill="#666666">Tech Solutions</text>
                  <text x="0" y="70" font-family="Arial, sans-serif" font-size="16" fill="#666666" font-style="italic">Integrates Business With Technology</text>
                </g>
              </svg>
            </div>
            <div style="text-align: center; flex: 1;">
              <h1 style="font-size: 32px; font-weight: bold; margin: 0; color: #333; text-decoration: underline; letter-spacing: 2px;">INVOICE</h1>
            </div>
            <div style="flex: 1;"></div>
          </div>

          <!-- Invoice Details and Billed To Section -->
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 30px;">
            <!-- Invoice Details -->
            <div>
              <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333;">Invoice Details :-</h3>
              <div style="margin-bottom: 5px;"><strong>Invoice Date:</strong> ${new Date(safeInvoice.issueDate).toLocaleDateString('en-GB')}</div>
              <div style="margin-bottom: 5px;"><strong>Invoice No.:</strong> ${safeInvoice.id}</div>
              <div style="margin-bottom: 5px;"><strong>Invoice Month:</strong> ${new Date(safeInvoice.issueDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</div>
              <div style="margin-bottom: 5px;"><strong>Due Date:</strong> ${new Date(safeInvoice.dueDate).toLocaleDateString('en-GB')}</div>
              <div style="margin-bottom: 5px;"><strong>Project:</strong> ${safeInvoice.project}</div>
            </div>

            <!-- Billed To -->
            <div>
              <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333;">Billed To :-</h3>
              <div style="margin-bottom: 5px;"><strong>Client:</strong> ${safeInvoice.client}</div>
              <div style="margin-bottom: 5px;"><strong>Invoice Type:</strong> ${safeInvoice.invoiceType || 'Standard'}</div>
              <div style="margin-bottom: 5px;"><strong>HSN Code:</strong> ${safeInvoice.hsnCode || '998313'}</div>
            </div>
          </div>

          <!-- Invoice Table -->
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px; font-size: 11px;">
            <thead>
              <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #333; padding: 8px; text-align: left;">Employee Name</th>
                <th style="border: 1px solid #333; padding: 8px; text-align: left;">Project</th>
                <th style="border: 1px solid #333; padding: 8px; text-align: right;">Rate</th>
                <th style="border: 1px solid #333; padding: 8px; text-align: right;">Amount</th>
                <th style="border: 1px solid #333; padding: 8px; text-align: right;">Tax</th>
                <th style="border: 1px solid #333; padding: 8px; text-align: right;">Total</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td style="border: 1px solid #333; padding: 8px;">${safeInvoice.candidate || 'N/A'}</td>
                <td style="border: 1px solid #333; padding: 8px;">${safeInvoice.project}</td>
                <td style="border: 1px solid #333; padding: 8px; text-align: right;">₹20,000.00</td>
                <td style="border: 1px solid #333; padding: 8px; text-align: right;">${safeInvoice.amount}</td>
                <td style="border: 1px solid #333; padding: 8px; text-align: right;">${safeInvoice.tax}</td>
                <td style="border: 1px solid #333; padding: 8px; text-align: right;">${safeInvoice.total}</td>
              </tr>
            </tbody>
          </table>

          <!-- Total Section -->
          <div style="text-align: right; margin-bottom: 30px;">
            <div style="display: inline-block; text-align: left;">
              <div style="margin-bottom: 5px;"><strong>Subtotal:</strong> ${safeInvoice.amount}</div>
              <div style="margin-bottom: 5px;"><strong>Tax:</strong> ${safeInvoice.tax}</div>
              <div style="margin-bottom: 5px; font-size: 14px; font-weight: bold;"><strong>Total:</strong> ${safeInvoice.total}</div>
            </div>
          </div>

          <!-- Footer -->
          <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #333; text-align: center; font-size: 10px; color: #666;">
            <p><strong>RedBeryl Tech Solutions</strong></p>
            <p>507-B Amanora Chambers, Amanora Mall, Hadapsar, Pune 411028</p>
            <p>Email: <EMAIL> | Phone: +91 9876543210</p>
          </div>
        </div>
      `;

      // Insert the invoice content
      printWindow.document.getElementById('invoice-content').innerHTML = invoiceHTML;

      toast.success("Invoice opened for printing");
    } catch (error) {
      console.error("Error preparing invoice for print:", error);
      toast.error("Failed to open invoice for printing");

      // Show more detailed error message
      if (error instanceof Error) {
        console.error("Print error details:", error.message);
        toast.error(`Print error: ${error.message}`);
      }
    } finally {
      setIsPrinting(false);
    }
  };

  const [isGenerating, setIsGenerating] = useState(false);

  /**
   * Handle generating an invoice PDF using the actual database invoice
   */
  const handleGenerateInvoice = async () => {
    setIsGenerating(true);
    try {
      console.log("InvoiceDetailsDialog: Generating invoice with ID:", safeInvoice.id);

      // First check if backend is available
      try {
        const testResponse = await fetch('/api/invoice-generation/test', {
          method: 'GET',
          headers: { 'Accept': 'text/plain' }
        });

        if (!testResponse.ok) {
          throw new Error('Backend not available');
        }
        console.log("Backend is available, proceeding with invoice generation");
      } catch (error) {
        console.error("Backend not available:", error);
        toast.error("Backend server is not running. Please start the backend server and try again.");
        return;
      }

      // Use the actual database ID from the invoice object
      console.log(`DEBUG: Using invoice database ID directly`);
      console.log(`DEBUG: Invoice object:`, safeInvoice);

      // Check if the invoice has a databaseId property (preferred)
      let numericId;

      if (safeInvoice.databaseId && typeof safeInvoice.databaseId === 'number') {
        numericId = safeInvoice.databaseId;
        console.log(`DEBUG: Using databaseId: ${numericId}`);
      } else if (safeInvoice.originalInvoice && safeInvoice.originalInvoice.id) {
        numericId = safeInvoice.originalInvoice.id;
        console.log(`DEBUG: Using originalInvoice.id: ${numericId}`);
      } else if (typeof safeInvoice.id === 'number') {
        numericId = safeInvoice.id;
        console.log(`DEBUG: Using numeric ID directly: ${numericId}`);
      } else if (typeof safeInvoice.id === 'string') {
        // Try to parse as number first
        const parsedId = parseInt(safeInvoice.id);
        if (!isNaN(parsedId)) {
          numericId = parsedId;
          console.log(`DEBUG: Parsed string ID to number: ${numericId}`);
        } else {
          // If it's a string like "INV-005", we need to find the actual database ID
          console.error(`DEBUG: Cannot use string ID "${safeInvoice.id}" for PDF generation`);
          console.error(`DEBUG: Invoice should have numeric database ID, not invoice number`);
          toast.error("Invalid invoice ID format. Please refresh the page and try again.");
          return;
        }
      } else {
        console.error(`DEBUG: Invalid ID type: ${typeof safeInvoice.id}`);
        toast.error("Invalid invoice ID format");
        return;
      }

      if (!numericId || isNaN(numericId)) {
        console.error(`DEBUG: Failed to get valid numeric ID from invoice`);
        toast.error("Invalid invoice ID format");
        return;
      }

      console.log(`DEBUG: Final result - Display ID: "${safeInvoice.id}", Database ID: ${numericId}`);
      console.log(`Generating PDF for database ID: ${numericId}`);

      // Use Vite proxy to generate PDF directly from the database invoice
      const apiUrls = [
        `/api/invoice-generation/pdf/${numericId}`,
        `/api/invoice-generation/public/pdf/${numericId}`
      ];

      let pdfGenerated = false;
      let lastError = null;

      for (const url of apiUrls) {
        try {
          console.log(`Trying to generate PDF from: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/pdf',
            }
          });

          console.log(`Response status: ${response.status}, statusText: ${response.statusText}`);
          console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));

          if (response.ok) {
            const blob = await response.blob();
            console.log(`PDF blob size: ${blob.size} bytes, type: ${blob.type}`);

            const pdfUrl = URL.createObjectURL(blob);

            // Open the PDF in a new tab
            window.open(pdfUrl, '_blank');

            toast.success("Invoice generated successfully");
            pdfGenerated = true;
            break;
          } else {
            const errorText = await response.text();
            console.log(`Failed to generate PDF from ${url}: ${response.status} ${response.statusText}`);
            console.log(`Error response body:`, errorText);
            lastError = new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
          }
        } catch (error) {
          console.log(`Error with ${url}:`, error);
          lastError = error;
        }
      }

      if (!pdfGenerated) {
        console.error("All PDF generation attempts failed. Last error:", lastError);
        console.error("Attempted URLs:", apiUrls);
        console.error("Invoice ID:", safeInvoice.id, "Numeric ID:", numericId);

        // Provide more specific error message
        if (lastError && lastError.message.includes('404')) {
          toast.error(`Invoice ${safeInvoice.id} not found in database. Please check if the invoice exists.`);
        } else if (lastError && lastError.message.includes('Failed to fetch')) {
          toast.error("Network error: Cannot connect to backend server. Please check if the backend is running.");
        } else {
          toast.error(`Failed to generate invoice PDF: ${lastError?.message || 'Unknown error'}`);
        }
      }

    } catch (error) {
      console.error("Error generating invoice:", error);
      toast.error("Failed to generate invoice");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[600px] bg-white">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Invoice {safeInvoice.id}</span>
            <div className="flex items-center gap-2">
              {safeInvoice.recurring && (
                <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
                  Recurring
                </Badge>
              )}
              <Badge variant="outline" className={getStatusColor(safeInvoice.status)}>
                {safeInvoice.status}
              </Badge>
            </div>
          </DialogTitle>
          <DialogDescription>
            View invoice details and perform actions
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 py-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Client</p>
            <p className="text-sm font-semibold">{safeInvoice.client}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Project</p>
            <p className="text-sm font-semibold">{safeInvoice.project}</p>
          </div>

          {safeInvoice.candidate && safeInvoice.candidate !== "-" && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Candidate</p>
              <p className="text-sm font-semibold">{safeInvoice.candidate}</p>
            </div>
          )}

          {safeInvoice.invoiceType && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Invoice Type</p>
              <p className="text-sm">{safeInvoice.invoiceType}</p>
            </div>
          )}

          {safeInvoice.staffingType && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Staffing Type</p>
              <p className="text-sm">{safeInvoice.staffingType}</p>
            </div>
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Issue Date</p>
            <p className="text-sm">{format(new Date(safeInvoice.issueDate), "MMMM d, yyyy")}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Due Date</p>
            <p className="text-sm">{format(new Date(safeInvoice.dueDate), "MMMM d, yyyy")}</p>
          </div>

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Billing Amount</p>
            <p className="text-sm">{safeInvoice.amount}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Tax Amount</p>
            <p className="text-sm">{safeInvoice.tax}</p>
          </div>
          <div className="space-y-1 col-span-2">
            <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
            <p className="text-lg font-bold">{safeInvoice.total}</p>
          </div>

          {safeInvoice.hsnCode && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">HSN Code</p>
              <p className="text-sm">{safeInvoice.hsnCode}</p>
            </div>
          )}

          {safeInvoice.redberylAccount && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Redberyl Account</p>
              <p className="text-sm">{safeInvoice.redberylAccount}</p>
            </div>
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Recurring</p>
            <p className="text-sm">{safeInvoice.recurring ? "Yes" : "No"}</p>
          </div>

          {typeof safeInvoice.publishedToFinance !== 'undefined' && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Published to Finance</p>
              <p className="text-sm">{safeInvoice.publishedToFinance ? "Yes" : "No"}</p>
            </div>
          )}

          {safeInvoice.publishedAt && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Published At</p>
              <p className="text-sm">{format(new Date(safeInvoice.publishedAt), "MMMM d, yyyy HH:mm")}</p>
            </div>
          )}

          {safeInvoice.notes && (
            <div className="space-y-1 col-span-2">
              <p className="text-sm font-medium text-muted-foreground">Notes</p>
              <p className="text-sm">{safeInvoice.notes}</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col gap-3 sm:flex-row sm:justify-between">
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={handleDownload}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Downloading...</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>Download</span>
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={handlePrint}
              disabled={isPrinting}
            >
              {isPrinting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Printing...</span>
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4" />
                  <span>Print</span>
                </>
              )}
            </Button>
            {safeInvoice.status.toLowerCase() !== "paid" && (
              <Button variant="outline" size="sm" className="gap-1" onClick={() => {
                toast.info("Payment recording functionality will be implemented soon");
              }}>
                <ReceiptIcon className="h-4 w-4" />
                <span>Record Payment</span>
              </Button>
            )}
            <InvoiceOneDriveButton
              invoice={safeInvoice}
              size="sm"
              variant="outline"
              onUploadSuccess={(response) => {
                console.log('OneDrive upload successful:', response);
              }}
              onUploadError={(error) => {
                console.error('OneDrive upload error:', error);
              }}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleGenerateInvoice}
              disabled={isGenerating}
              className="bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 border-blue-200 gap-1"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4" />
                  <span>Generate Invoice</span>
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceDetailsDialog;