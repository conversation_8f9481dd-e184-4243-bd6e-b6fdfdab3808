# 🎯 OneDrive Final Fix - AADSTS50011 Redirect URI Mismatch

## 🚨 **THE PROBLEM**
```
AADSTS50011: The redirect URI 'urn:ietf:wg:oauth:2.0:oob' specified in the request does not match the redirect URIs configured for the application '86756722-ad2a-4ac0-8806-e2705653949a'.
```

**Root Cause:** Your Azure App Registration doesn't have the redirect URI that your application is trying to use.

## ✅ **DEFINITIVE SOLUTION**

### **Option 1: Add Redirect URI to Azure (RECOMMENDED)**

**You MUST add this redirect URI to your Azure App Registration:**

1. **Go to Azure Portal**: https://portal.azure.com
2. **Navigate to**: Azure Active Directory → App registrations
3. **Find your app**: `86756722-ad2a-4ac0-8806-e2705653949a`
4. **Click**: Authentication (left sidebar)
5. **Add Redirect URI**: `http://localhost:8091/api/onedrive/callback`
6. **Click**: Save

**After adding the redirect URI, restart your backend and test.**

### **Option 2: Try Common Pre-configured URIs**

If you can't access Azure Portal, try these redirect URIs that are often pre-configured:

Edit `backend/src/main/resources/application.properties`:

```properties
# Try these one by one, restart backend after each change:

# Option 1: Backend callback (current)
onedrive.redirect.uri=http://localhost:8091/api/onedrive/callback

# Option 2: Simple localhost
onedrive.redirect.uri=http://localhost

# Option 3: Localhost with port
onedrive.redirect.uri=http://localhost:8080

# Option 4: HTTPS localhost
onedrive.redirect.uri=https://localhost:8091/api/onedrive/callback
```

**Test each one:**
1. Change the redirect URI
2. Restart backend: `cd backend && mvn spring-boot:run`
3. Test OneDrive button
4. If it fails, try the next option

### **Option 3: Contact Azure Administrator**

If none of the above work, send this to your Azure administrator:

```
Subject: Add Redirect URI to Azure App Registration

Hi,

Please add the following redirect URI to our Azure App Registration:

App ID: 86756722-ad2a-4ac0-8806-e2705653949a
Redirect URI to add: http://localhost:8091/api/onedrive/callback

Steps:
1. Go to Azure Portal → Azure Active Directory → App registrations
2. Find app: 86756722-ad2a-4ac0-8806-e2705653949a
3. Click Authentication
4. Add redirect URI: http://localhost:8091/api/onedrive/callback
5. Save

This is needed for OneDrive integration in our invoice application.

Thanks!
```

## 🧪 **Testing Tools**

I've created these tools to help you test:

1. **`onedrive-fix-complete.html`** - Comprehensive testing tool
2. **`onedrive-diagnostic.html`** - Backend connectivity test
3. **`onedrive-redirect-test.html`** - Redirect URI tester

## 🎯 **Expected Results**

### **Before Fix (Error):**
```
❌ AADSTS50011: Redirect URI mismatch
❌ Authentication fails
❌ OneDrive upload doesn't work
```

### **After Fix (Success):**
```
✅ Microsoft login popup opens
✅ User authenticates successfully
✅ Redirects to your callback URL
✅ Access token obtained
✅ OneDrive upload works
```

## 🔍 **How to Verify the Fix**

1. **Open your application**: http://localhost:3060
2. **Go to Invoices**: Click "Invoices" in sidebar
3. **Click OneDrive button**: Cloud icon on any invoice
4. **Expected flow**:
   - Popup opens with Microsoft login
   - User enters credentials
   - Redirects to callback URL (no error)
   - Popup closes
   - Upload proceeds

## 💡 **Why This Happens**

Azure AD requires **exact matches** between:
- The redirect URI in your application code
- The redirect URIs configured in Azure App Registration

**Security Feature:** This prevents malicious apps from intercepting your authentication tokens.

## 🚀 **Quick Fix Commands**

```bash
# Stop backend
Ctrl+C

# Edit configuration
# Change onedrive.redirect.uri in application.properties

# Restart backend
cd backend
mvn spring-boot:run

# Test in browser
# Go to http://localhost:3060 and try OneDrive button
```

## 📞 **Need Help?**

If you're still getting the error after trying all options:

1. **Check the exact error message** - it will tell you which redirect URI is being rejected
2. **Try the testing tools** - they will show you which URIs work
3. **Contact your Azure administrator** - they can add the correct redirect URI

**The fix is simple once the redirect URI is added to Azure!** 🎉
