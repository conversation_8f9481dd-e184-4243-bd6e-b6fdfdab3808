import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert<PERSON>ircle, CheckCircle, Copy, ExternalLink, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { oneDriveService } from '@/services/oneDriveService';

interface OneDriveAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (accessToken: string) => void;
  onError: (error: string) => void;
}

const OneDriveAuthModal: React.FC<OneDriveAuthModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError
}) => {
  const [authMethod, setAuthMethod] = useState<'device' | 'popup' | null>(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [deviceCode, setDeviceCode] = useState<string | null>(null);
  const [userCode, setUserCode] = useState<string | null>(null);
  const [verificationUri, setVerificationUri] = useState<string | null>(null);

  const handleDeviceAuth = async () => {
    setAuthMethod('device');
    setIsAuthenticating(true);
    
    try {
      const result = await oneDriveService.authenticateWithDeviceCode();
      
      if (result.success && result.accessToken) {
        toast.success('Successfully authenticated with OneDrive!');
        onSuccess(result.accessToken);
        onClose();
      } else {
        toast.error(result.error || 'Device authentication failed');
        onError(result.error || 'Device authentication failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Device authentication failed';
      toast.error(errorMessage);
      onError(errorMessage);
    } finally {
      setIsAuthenticating(false);
      setAuthMethod(null);
    }
  };

  const handlePopupAuth = async () => {
    setAuthMethod('popup');
    setIsAuthenticating(true);
    
    try {
      const result = await oneDriveService.authenticateWithPopup();
      
      if (result.success && result.accessToken) {
        toast.success('Successfully authenticated with OneDrive!');
        onSuccess(result.accessToken);
        onClose();
      } else {
        toast.error(result.error || 'Popup authentication failed');
        onError(result.error || 'Popup authentication failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Popup authentication failed';
      toast.error(errorMessage);
      onError(errorMessage);
    } finally {
      setIsAuthenticating(false);
      setAuthMethod(null);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      toast.success('Copied to clipboard!');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="h-5 w-5" />
            OneDrive Authentication
          </CardTitle>
          <CardDescription>
            Choose your preferred authentication method to connect to OneDrive
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!authMethod && !isAuthenticating && (
            <>
              <div className="space-y-3">
                <Button
                  onClick={handleDeviceAuth}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Device Code Authentication
                  <Badge variant="secondary" className="ml-auto">
                    Recommended
                  </Badge>
                </Button>
                <p className="text-sm text-muted-foreground px-2">
                  Most reliable method. Works even if popups are blocked.
                </p>
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handlePopupAuth}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Popup Authentication
                </Button>
                <p className="text-sm text-muted-foreground px-2">
                  Quick authentication using a popup window. May be blocked by browser.
                </p>
              </div>

              <div className="flex items-start gap-2 p-3 bg-amber-50 rounded-lg border border-amber-200">
                <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-amber-800">
                  <p className="font-medium">Having trouble?</p>
                  <p>Try the device code method if popups are blocked or authentication fails.</p>
                </div>
              </div>
            </>
          )}

          {isAuthenticating && (
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto" />
              <p className="text-sm text-muted-foreground">
                {authMethod === 'device' 
                  ? 'Starting device authentication...' 
                  : 'Opening authentication popup...'}
              </p>
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1"
              disabled={isAuthenticating}
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OneDriveAuthModal;
