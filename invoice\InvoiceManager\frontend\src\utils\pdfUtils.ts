import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types/invoice';

/**
 * Generate PDF blob from invoice data for OneDrive upload
 */
export const generateInvoicePdfBlob = async (invoice: Invoice): Promise<Blob> => {
  let tempDiv: HTMLElement | null = null;

  try {
    // Validate invoice data
    if (!invoice || !invoice.id) {
      throw new Error('Invalid invoice data provided');
    }

    console.log('Starting client-side PDF generation for invoice:', invoice.id);

    // Create a temporary div to render the invoice template
    tempDiv = document.createElement('div');
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    tempDiv.style.width = '800px';
    tempDiv.style.backgroundColor = 'white';
    tempDiv.style.padding = '20px';
    tempDiv.style.fontFamily = 'Arial, sans-serif';
    tempDiv.style.fontSize = '12px';
    tempDiv.style.lineHeight = '1.4';
    tempDiv.style.color = '#333';
    document.body.appendChild(tempDiv);

    // Generate HTML content directly instead of using React
    console.log('Generating invoice HTML...');
    tempDiv.innerHTML = generateInvoiceHTML(invoice);

    // Wait for any fonts or images to load
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('Capturing template with html2canvas...');
    // Use html2canvas to capture the rendered template
    const canvas = await html2canvas(tempDiv, {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
      allowTaint: true,
      foreignObjectRendering: true,
      width: 800,
      height: 1200
    });

    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('Failed to capture template - canvas is empty');
    }

    console.log(`Canvas captured: ${canvas.width}x${canvas.height}`);

    // Create PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgWidth = 210; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    const imgData = canvas.toDataURL('image/png');

    if (!imgData || imgData === 'data:,') {
      throw new Error('Failed to convert canvas to image data');
    }

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

    // Convert to blob
    const pdfBlob = pdf.output('blob');

    if (!pdfBlob || pdfBlob.size === 0) {
      throw new Error('Generated PDF is empty');
    }

    console.log(`PDF generated successfully, size: ${pdfBlob.size} bytes`);
    return pdfBlob;
  } catch (error) {
    console.error('Error in client-side PDF generation:', error);
    throw new Error('Failed to generate PDF: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    // Clean up
    if (tempDiv && document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }
  }
};

/**
 * Generate PDF base64 string from invoice data for OneDrive upload
 */
export const generateInvoicePdfBase64 = async (invoice: Invoice): Promise<string> => {
  try {
    const pdfBlob = await generateInvoicePdfBlob(invoice);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error generating PDF base64:', error);
    throw new Error('Failed to generate PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to blob for OneDrive upload
 */
export const fetchInvoicePdfBlob = async (invoiceId: string | number): Promise<Blob> => {
  try {
    // Try multiple API endpoints with better error handling
    const apiUrls = [
      `/api/invoice-generation/public/pdf/${invoiceId}`,
      `/api/invoice-generation/public/pdf/by-number/${invoiceId}`,
      `/api/invoice-generation/pdf/${invoiceId}`
    ];

    let lastError: Error | null = null;
    const errors: string[] = [];

    for (const url of apiUrls) {
      try {
        console.log(`Attempting to fetch PDF from: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          // Add timeout
          signal: AbortSignal.timeout(30000) // 30 seconds timeout
        });

        console.log(`Response status: ${response.status} for ${url}`);

        if (response.ok) {
          const blob = await response.blob();
          console.log(`Blob size: ${blob.size} bytes, type: ${blob.type}`);

          if (blob.size > 0 && blob.type === 'application/pdf') {
            console.log(`Successfully fetched valid PDF from: ${url}`);
            return blob;
          } else {
            errors.push(`${url}: Invalid PDF (size: ${blob.size}, type: ${blob.type})`);
          }
        } else {
          const errorText = await response.text();
          errors.push(`${url}: HTTP ${response.status} - ${errorText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to fetch from ${url}:`, errorMessage);
        errors.push(`${url}: ${errorMessage}`);
        lastError = error instanceof Error ? error : new Error(errorMessage);
      }
    }

    // Create detailed error message
    const errorDetails = errors.join('; ');
    throw new Error(`Failed to fetch PDF from all endpoints: ${errorDetails}`);
  } catch (error) {
    console.error('Error fetching PDF blob:', error);
    throw new Error('Unable to fetch PDF from backend: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to base64 for OneDrive upload
 */
export const fetchInvoicePdfBase64 = async (invoiceId: string | number): Promise<string> => {
  try {
    const pdfBlob = await fetchInvoicePdfBlob(invoiceId);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error fetching PDF base64:', error);
    throw new Error('Failed to fetch PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Download PDF blob as file
 */
export const downloadPdfBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Import React for createElement
import React from 'react';

/**
 * Generate HTML content for invoice PDF
 */
const generateInvoiceHTML = (invoice: Invoice): string => {
  // Ensure all required fields are present with enhanced data
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "saurabh",
    project: invoice.project || "hadapsar",
    candidate: invoice.candidate || "Prathamesh Kadam",
    invoiceType: invoice.invoiceType || "Services",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹50,000.00",
    tax: invoice.tax || "₹9,000.00",
    total: invoice.total || "₹59,000.00",
    issueDate: invoice.issueDate || "2025-01-21",
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || "",
    // Enhanced fields with defaults
    employeeName: invoice.employeeName || invoice.candidate || "Prathamesh Kadam",
    employeeEngagementCode: invoice.employeeEngagementCode || "ENG-0020",
    joiningDate: invoice.joiningDate || "2025-01-21",
    rate: invoice.rate || "₹50,000.00",
    billAmount: invoice.billAmount || "₹50,000.00",
    cgst: invoice.cgst || "9%",
    sgst: invoice.sgst || "9%",
    igst: invoice.igst || "18%",
    netPayable: invoice.netPayable || "₹59,000.00",
    bankName: invoice.bankName || "HDFC Bank",
    branchName: invoice.branchName || "MG Road Branch",
    accountName: invoice.accountName || "Acme Corporation Pvt Ltd",
    accountNo: invoice.accountNo || "***********",
    ifscCode: invoice.ifscCode || "HDFC0001234",
    accountType: invoice.accountType || "Current",
    gstin: invoice.gstin || "29**********2Z5",
    cin: invoice.cin || "U12345KA2020PTC012345",
    panNo: invoice.panNo || "**********",
    attendanceDays: invoice.attendanceDays || 20,
    hsnCode: invoice.hsnCode || "465757"
  };

  // Calculate amounts based on attendance if available
  const dailyRate = parseFloat(safeInvoice.rate?.replace(/[₹,]/g, '') || '50000');
  const days = safeInvoice.attendanceDays || 20;
  const baseAmount = dailyRate;
  const cgstAmount = baseAmount * 0.09; // 9%
  const sgstAmount = baseAmount * 0.09; // 9%
  const totalAmount = baseAmount + cgstAmount + sgstAmount;

  const amounts = {
    baseAmount: `₹${baseAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
    cgstAmount: `₹${cgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
    sgstAmount: `₹${sgstAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`,
    totalAmount: `₹${totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2 })}`
  };

  // Format invoice number
  const formatInvoiceNumber = (invoiceId: string, invoiceDate: string): string => {
    try {
      let numericPart = invoiceId.replace(/[^\d]/g, '');
      if (numericPart.length < 3) {
        numericPart = numericPart.padStart(3, '0');
      } else if (numericPart.length > 3) {
        numericPart = numericPart.slice(-3);
      }

      const date = new Date(invoiceDate);
      const month = date.getMonth() + 1;
      const year = date.getFullYear();

      let fyStart, fyEnd;
      if (month >= 4) {
        fyStart = year;
        fyEnd = year + 1;
      } else {
        fyStart = year - 1;
        fyEnd = year;
      }

      const fyStartShort = fyStart.toString().slice(-2);
      const fyEndShort = fyEnd.toString().slice(-2);

      return `RB/${fyStartShort}-${fyEndShort}/${numericPart}`;
    } catch (error) {
      return invoiceId;
    }
  };

  const formatDate = (dateStr: string): string => {
    try {
      return new Date(dateStr).toLocaleDateString('en-GB');
    } catch {
      return dateStr;
    }
  };

  const formatMonth = (dateStr: string): string => {
    try {
      return new Date(dateStr).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    } catch {
      return dateStr;
    }
  };

  return `
    <div style="font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; background: white; color: #333; font-size: 12px; line-height: 1.4;">
      <!-- Header with Logo -->
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; padding-bottom: 20px;">
        <div style="flex: 1;">
          <!-- RedBeryl Logo SVG -->
          <svg width="320" height="100" viewBox="0 0 800 250" style="margin-bottom: 10px;">
            <rect width="800" height="250" fill="white"/>
            <g transform="translate(20, 50)">
              <path d="M20 80 C20 40, 50 10, 90 10 C100 10, 110 12, 118 16 C125 8, 135 4, 146 4 C170 4, 190 24, 190 48 C190 52, 189 56, 188 60 C195 65, 200 73, 200 82 C200 95, 190 106, 177 106 L45 106 C30 106, 18 94, 18 79 C18 79, 19 79, 20 80 Z" fill="#1E88E5" stroke="#1565C0" stroke-width="2"/>
              <circle cx="80" cy="60" r="8" fill="#2196F3"/>
            </g>
            <g transform="translate(160, 30)">
              <path d="M30 60 C30 35, 50 15, 75 15 C85 15, 94 18, 101 23 C108 18, 117 15, 127 15 C152 15, 172 35, 172 60 C172 63, 171 66, 170 69 C177 73, 182 80, 182 88 C182 98, 174 106, 164 106 L46 106 C34 106, 25 97, 25 86 C25 75, 32 66, 41 63 C35 59, 30 54, 30 60 Z" fill="#E91E63" stroke="#C2185B" stroke-width="2"/>
              <circle cx="90" cy="50" r="6" fill="#F06292"/>
            </g>
            <g transform="translate(120, 80)">
              <circle cx="0" cy="0" r="4" fill="#9C27B0"/>
              <circle cx="20" cy="10" r="3" fill="#673AB7"/>
              <circle cx="40" cy="5" r="2" fill="#3F51B5"/>
            </g>
            <g transform="translate(350, 80)">
              <text x="0" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#E91E63">Red</text>
              <text x="140" y="50" font-family="Arial, sans-serif" font-size="60" font-weight="bold" fill="#1565C0">Beryl</text>
              <text x="0" y="85" font-family="Arial, sans-serif" font-size="20" font-weight="600" fill="#424242" letter-spacing="4px">TECH SOLUTIONS</text>
              <text x="0" y="110" font-family="Arial, sans-serif" font-size="16" fill="#666666" font-style="italic">Integrates Business With Technology</text>
            </g>
          </svg>
        </div>
        <div style="text-align: center; flex: 1;">
          <h1 style="font-size: 32px; font-weight: bold; margin: 0; color: #333; text-decoration: underline; letter-spacing: 2px;">INVOICE</h1>
        </div>
        <div style="flex: 1;"></div>
      </div>

      <!-- Invoice Details and Billed To -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-bottom: 30px;">
        <div>
          <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333;">Invoice Details :-</h3>
          <div style="margin-bottom: 5px;"><strong>Invoice Date:</strong> ${formatDate(safeInvoice.issueDate)}</div>
          <div style="margin-bottom: 5px;"><strong>Invoice No.:</strong> ${formatInvoiceNumber(safeInvoice.id, safeInvoice.issueDate)}</div>
          <div style="margin-bottom: 5px;"><strong>Invoice Month:</strong> ${formatMonth(safeInvoice.issueDate)}</div>
          <div style="margin-bottom: 5px;"><strong>Invoice For:</strong> ${safeInvoice.invoiceType}</div>
          <div style="margin-bottom: 5px;"><strong>HSN No.:</strong> ${safeInvoice.hsnCode}</div>
          <div style="margin-bottom: 5px;"><strong>Employee Name:</strong> ${safeInvoice.employeeName}</div>
          <div style="margin-bottom: 5px;"><strong>Employee Engagement Code:</strong> ${safeInvoice.employeeEngagementCode}</div>
        </div>
        <div>
          <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333;">Billed To :-</h3>
          <div style="margin-bottom: 5px;"><strong>${safeInvoice.client}</strong></div>
          <div style="margin-bottom: 5px;">${safeInvoice.project}</div>
          <div style="margin-bottom: 5px;"><strong>GST No:</strong></div>
        </div>
      </div>

      <!-- Billing Table -->
      <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; border: 1px solid #333;">
        <thead>
          <tr>
            <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Employee Name</th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Joining Date</th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Rate</th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Bill Amount</th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">GST</th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 8px; border: 1px solid #333; font-size: 12px; font-weight: bold;">Total Bill Amount</th>
          </tr>
          <tr>
            <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 2px; border: 1px solid #333; font-size: 9px;">
              <div style="display: flex; justify-content: space-around;">
                <span>CGST @9%</span>
                <span>SGST @9%</span>
                <span>IGST @18%</span>
              </div>
            </th>
            <th style="background-color: #f8f9fa; text-align: center; padding: 4px; border: 1px solid #333; font-size: 10px;"></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${safeInvoice.employeeName}</td>
            <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${formatDate(safeInvoice.joiningDate)}</td>
            <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${amounts.baseAmount}</td>
            <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">${amounts.baseAmount}</td>
            <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px;">
              <div style="display: flex; justify-content: space-around; font-size: 10px;">
                <span>${amounts.cgstAmount}</span>
                <span>${amounts.sgstAmount}</span>
                <span>₹0.00</span>
              </div>
            </td>
            <td style="padding: 8px; border: 1px solid #333; text-align: center; font-size: 11px; font-weight: bold;">${amounts.totalAmount}</td>
          </tr>
        </tbody>
      </table>

      <!-- GST Type Information -->
      <div style="font-size: 12px; margin-bottom: 10px; text-align: left; color: #1565C0; font-weight: bold;">
        GST Type: Intra State (Maharashtra) - CGST (9%) + SGST (9%) = 18%
      </div>

      <!-- Net Payable -->
      <div style="font-size: 14px; font-weight: bold; margin-bottom: 20px; text-align: left;">
        <strong>Net Payable: ${amounts.totalAmount}</strong> /- (Fifty Nine Thousand Only)
      </div>

      <!-- Payment Information and Authorized Signatory -->
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
        <div>
          <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333; background-color: #f8f9fa; padding: 5px; text-align: center; border: 1px solid #333;">Payment Information</h3>
          <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
            <tbody>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Bank Name:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.bankName}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Branch Name:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.branchName}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Account Name:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.accountName}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Account No:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.accountNo}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">IFSC Code:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.ifscCode}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">Account Type:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.accountType}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">GSTIN:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.gstin}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">CIN:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.cin}</td></tr>
              <tr><td style="padding: 3px; border: 1px solid #333; font-weight: bold;">PAN No:</td><td style="padding: 3px; border: 1px solid #333;">${safeInvoice.panNo}</td></tr>
            </tbody>
          </table>
        </div>
        <div>
          <h3 style="font-size: 14px; font-weight: bold; margin-bottom: 10px; color: #333; background-color: #f8f9fa; padding: 5px; text-align: center; border: 1px solid #333;">Authorized Signatory</h3>
          <div style="height: 100px; border: 1px solid #333; display: flex; align-items: flex-end; justify-content: center; padding: 10px; font-size: 12px;">
            <div style="text-align: center;">
              <div style="margin-bottom: 40px;"></div>
              <div>For RedBeryl Tech Solutions Pvt. Ltd.</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div style="text-align: center; font-size: 12px; color: #333; border-top: 1px solid #333; padding-top: 10px; font-style: italic;">
        Thank you for doing business with us.
      </div>
    </div>
  `;
};
