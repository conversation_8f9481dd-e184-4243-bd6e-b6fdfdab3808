import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types/invoice';

/**
 * Generate PDF blob from invoice data for OneDrive upload
 */
export const generateInvoicePdfBlob = async (invoice: Invoice): Promise<Blob> => {
  let tempDiv: HTMLElement | null = null;

  try {
    // Validate invoice data
    if (!invoice || !invoice.id) {
      throw new Error('Invalid invoice data provided');
    }

    console.log('Starting client-side PDF generation for invoice:', invoice.id);

    // Create a temporary div to render the invoice template
    tempDiv = document.createElement('div');
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    tempDiv.style.width = '800px';
    tempDiv.style.backgroundColor = 'white';
    tempDiv.style.padding = '20px';
    document.body.appendChild(tempDiv);

    // Import and render the invoice template
    console.log('Loading invoice template...');
    const { default: InvoicePdfTemplate } = await import('@/components/invoices/InvoicePdfTemplate');
    const root = document.createElement('div');
    tempDiv.appendChild(root);

    // Use ReactDOM to render the template
    const ReactDOM = await import('react-dom/client');
    const reactRoot = ReactDOM.createRoot(root);
    reactRoot.render(React.createElement(InvoicePdfTemplate, { invoice }));

    // Wait for rendering to complete
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Wait for template to render
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('Capturing template with html2canvas...');
    // Use html2canvas to capture the rendered template
    const canvas = await html2canvas(root, {
      scale: 2,
      useCORS: true,
      logging: false,
      backgroundColor: '#ffffff',
      allowTaint: true,
      foreignObjectRendering: true
    });

    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('Failed to capture template - canvas is empty');
    }

    console.log(`Canvas captured: ${canvas.width}x${canvas.height}`);

    // Create PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgWidth = 210; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    const imgData = canvas.toDataURL('image/png');

    if (!imgData || imgData === 'data:,') {
      throw new Error('Failed to convert canvas to image data');
    }

    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

    // Convert to blob
    const pdfBlob = pdf.output('blob');

    if (!pdfBlob || pdfBlob.size === 0) {
      throw new Error('Generated PDF is empty');
    }

    console.log(`PDF generated successfully, size: ${pdfBlob.size} bytes`);
    return pdfBlob;
  } catch (error) {
    console.error('Error in client-side PDF generation:', error);
    throw new Error('Failed to generate PDF: ' + (error instanceof Error ? error.message : String(error)));
  } finally {
    // Clean up
    if (tempDiv && document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }
  }
};

/**
 * Generate PDF base64 string from invoice data for OneDrive upload
 */
export const generateInvoicePdfBase64 = async (invoice: Invoice): Promise<string> => {
  try {
    const pdfBlob = await generateInvoicePdfBlob(invoice);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error generating PDF base64:', error);
    throw new Error('Failed to generate PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to blob for OneDrive upload
 */
export const fetchInvoicePdfBlob = async (invoiceId: string | number): Promise<Blob> => {
  try {
    // Try multiple API endpoints with better error handling
    const apiUrls = [
      `/api/invoice-generation/public/pdf/${invoiceId}`,
      `/api/invoice-generation/public/pdf/by-number/${invoiceId}`,
      `/api/invoice-generation/pdf/${invoiceId}`
    ];

    let lastError: Error | null = null;
    const errors: string[] = [];

    for (const url of apiUrls) {
      try {
        console.log(`Attempting to fetch PDF from: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          // Add timeout
          signal: AbortSignal.timeout(30000) // 30 seconds timeout
        });

        console.log(`Response status: ${response.status} for ${url}`);

        if (response.ok) {
          const blob = await response.blob();
          console.log(`Blob size: ${blob.size} bytes, type: ${blob.type}`);

          if (blob.size > 0 && blob.type === 'application/pdf') {
            console.log(`Successfully fetched valid PDF from: ${url}`);
            return blob;
          } else {
            errors.push(`${url}: Invalid PDF (size: ${blob.size}, type: ${blob.type})`);
          }
        } else {
          const errorText = await response.text();
          errors.push(`${url}: HTTP ${response.status} - ${errorText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to fetch from ${url}:`, errorMessage);
        errors.push(`${url}: ${errorMessage}`);
        lastError = error instanceof Error ? error : new Error(errorMessage);
      }
    }

    // Create detailed error message
    const errorDetails = errors.join('; ');
    throw new Error(`Failed to fetch PDF from all endpoints: ${errorDetails}`);
  } catch (error) {
    console.error('Error fetching PDF blob:', error);
    throw new Error('Unable to fetch PDF from backend: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to base64 for OneDrive upload
 */
export const fetchInvoicePdfBase64 = async (invoiceId: string | number): Promise<string> => {
  try {
    const pdfBlob = await fetchInvoicePdfBlob(invoiceId);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error fetching PDF base64:', error);
    throw new Error('Failed to fetch PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Download PDF blob as file
 */
export const downloadPdfBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Import React for createElement
import React from 'react';
