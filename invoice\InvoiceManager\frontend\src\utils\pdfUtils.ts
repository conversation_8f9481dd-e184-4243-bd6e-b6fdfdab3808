import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types/invoice';

/**
 * Generate PDF blob from invoice data for OneDrive upload
 */
export const generateInvoicePdfBlob = async (invoice: Invoice): Promise<Blob> => {
  try {
    // Validate invoice data
    if (!invoice || !invoice.id) {
      throw new Error('Invalid invoice data provided');
    }

    console.log('Starting client-side PDF generation for invoice:', invoice.id);

    // Use jsPDF directly to create the PDF with text content
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Add content directly to PDF
    addInvoiceContentToPdf(pdf, invoice);

    // Convert to blob
    const pdfBlob = pdf.output('blob');

    if (!pdfBlob || pdfBlob.size === 0) {
      throw new Error('Generated PDF is empty');
    }

    console.log(`PDF generated successfully, size: ${pdfBlob.size} bytes`);
    return pdfBlob;
  } catch (error) {
    console.error('Error in client-side PDF generation:', error);
    throw new Error('Failed to generate PDF: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Generate PDF base64 string from invoice data for OneDrive upload
 */
export const generateInvoicePdfBase64 = async (invoice: Invoice): Promise<string> => {
  try {
    const pdfBlob = await generateInvoicePdfBlob(invoice);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error generating PDF base64:', error);
    throw new Error('Failed to generate PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to blob for OneDrive upload
 */
export const fetchInvoicePdfBlob = async (invoiceId: string | number): Promise<Blob> => {
  try {
    // Try multiple API endpoints with better error handling
    const apiUrls = [
      `/api/invoice-generation/public/pdf/${invoiceId}`,
      `/api/invoice-generation/public/pdf/by-number/${invoiceId}`,
      `/api/invoice-generation/pdf/${invoiceId}`
    ];

    let lastError: Error | null = null;
    const errors: string[] = [];

    for (const url of apiUrls) {
      try {
        console.log(`Attempting to fetch PDF from: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          // Add timeout
          signal: AbortSignal.timeout(30000) // 30 seconds timeout
        });

        console.log(`Response status: ${response.status} for ${url}`);

        if (response.ok) {
          const blob = await response.blob();
          console.log(`Blob size: ${blob.size} bytes, type: ${blob.type}`);

          if (blob.size > 0 && blob.type === 'application/pdf') {
            console.log(`Successfully fetched valid PDF from: ${url}`);
            return blob;
          } else {
            errors.push(`${url}: Invalid PDF (size: ${blob.size}, type: ${blob.type})`);
          }
        } else {
          const errorText = await response.text();
          errors.push(`${url}: HTTP ${response.status} - ${errorText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to fetch from ${url}:`, errorMessage);
        errors.push(`${url}: ${errorMessage}`);
        lastError = error instanceof Error ? error : new Error(errorMessage);
      }
    }

    // Create detailed error message
    const errorDetails = errors.join('; ');
    throw new Error(`Failed to fetch PDF from all endpoints: ${errorDetails}`);
  } catch (error) {
    console.error('Error fetching PDF blob:', error);
    throw new Error('Unable to fetch PDF from backend: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to base64 for OneDrive upload
 */
export const fetchInvoicePdfBase64 = async (invoiceId: string | number): Promise<string> => {
  try {
    const pdfBlob = await fetchInvoicePdfBlob(invoiceId);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error fetching PDF base64:', error);
    throw new Error('Failed to fetch PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Download PDF blob as file
 */
export const downloadPdfBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * Add invoice content directly to PDF using jsPDF
 */
const addInvoiceContentToPdf = (pdf: jsPDF, invoice: Invoice): void => {
  // Set up fonts and colors
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(12);
  pdf.setTextColor(0, 0, 0);

  // Add header
  pdf.setFontSize(24);
  pdf.setFont('helvetica', 'bold');
  pdf.text('INVOICE', 105, 30, { align: 'center' });

  // Add company info (simplified)
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('RedBeryl Tech Solutions', 20, 50);
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Integrates Business With Technology', 20, 58);

  // Invoice details
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Invoice Details:', 20, 80);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(10);

  const invoiceDate = new Date(invoice.issueDate).toLocaleDateString('en-GB');
  const invoiceNumber = `RB/24-25/${invoice.id.toString().padStart(3, '0')}`;
  const invoiceMonth = new Date(invoice.issueDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

  pdf.text(`Invoice Date: ${invoiceDate}`, 20, 90);
  pdf.text(`Invoice No.: ${invoiceNumber}`, 20, 98);
  pdf.text(`Invoice Month: ${invoiceMonth}`, 20, 106);
  pdf.text(`Invoice For: ${invoice.invoiceType || 'Services'}`, 20, 114);
  pdf.text(`HSN No.: ${invoice.hsnCode || '465757'}`, 20, 122);
  pdf.text(`Employee Name: ${invoice.candidate || 'Prathamesh Kadam'}`, 20, 130);
  pdf.text(`Employee Engagement Code: ENG-0020`, 20, 138);

  // Billed To
  pdf.setFont('helvetica', 'bold');
  pdf.text('Billed To:', 120, 80);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`${invoice.client || 'saurabh'}`, 120, 90);
  pdf.text(`${invoice.project || 'hadapsar'}`, 120, 98);
  pdf.text('GST No:', 120, 106);

  // Calculate amounts
  const baseAmount = parseFloat(invoice.amount?.replace(/[₹,]/g, '') || '50000');
  const cgstAmount = baseAmount * 0.09;
  const sgstAmount = baseAmount * 0.09;
  const totalAmount = baseAmount + cgstAmount + sgstAmount;

  // Billing table header
  let yPos = 160;
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(9);

  // Table headers
  pdf.rect(20, yPos, 170, 10);
  pdf.text('Employee Name', 25, yPos + 7);
  pdf.text('Joining Date', 60, yPos + 7);
  pdf.text('Rate', 90, yPos + 7);
  pdf.text('Bill Amount', 110, yPos + 7);
  pdf.text('GST', 140, yPos + 7);
  pdf.text('Total', 165, yPos + 7);

  // GST sub-headers
  yPos += 10;
  pdf.rect(20, yPos, 170, 8);
  pdf.setFontSize(7);
  pdf.text('CGST @9%', 135, yPos + 5);
  pdf.text('SGST @9%', 150, yPos + 5);
  pdf.text('IGST @18%', 165, yPos + 5);

  // Table data
  yPos += 8;
  pdf.rect(20, yPos, 170, 12);
  pdf.setFontSize(9);
  pdf.setFont('helvetica', 'normal');

  const joiningDate = new Date(invoice.joiningDate || invoice.issueDate).toLocaleDateString('en-GB');

  pdf.text(invoice.candidate || 'Prathamesh Kadam', 25, yPos + 8);
  pdf.text(joiningDate, 60, yPos + 8);
  pdf.text(`₹${baseAmount.toLocaleString('en-IN')}`, 90, yPos + 8);
  pdf.text(`₹${baseAmount.toLocaleString('en-IN')}`, 110, yPos + 8);
  pdf.text(`₹${cgstAmount.toLocaleString('en-IN')}`, 135, yPos + 8);
  pdf.text(`₹${sgstAmount.toLocaleString('en-IN')}`, 150, yPos + 8);
  pdf.text('₹0', 165, yPos + 8);
  pdf.text(`₹${totalAmount.toLocaleString('en-IN')}`, 175, yPos + 8);

  // GST Type
  yPos += 20;
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(21, 101, 192); // Blue color
  pdf.text('GST Type: Intra State (Maharashtra) - CGST (9%) + SGST (9%) = 18%', 20, yPos);

  // Net Payable
  yPos += 10;
  pdf.setTextColor(0, 0, 0);
  pdf.setFontSize(12);
  pdf.text(`Net Payable: ₹${totalAmount.toLocaleString('en-IN')} /- (Fifty Nine Thousand Only)`, 20, yPos);

  // Payment Information
  yPos += 20;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Payment Information', 20, yPos);

  pdf.setFont('helvetica', 'normal');
  yPos += 8;
  pdf.text('Bank Name: HDFC Bank', 20, yPos);
  yPos += 6;
  pdf.text('Branch Name: MG Road Branch', 20, yPos);
  yPos += 6;
  pdf.text('Account Name: Acme Corporation Pvt Ltd', 20, yPos);
  yPos += 6;
  pdf.text('Account No: ***********', 20, yPos);
  yPos += 6;
  pdf.text('IFSC Code: HDFC0001234', 20, yPos);
  yPos += 6;
  pdf.text('Account Type: Current', 20, yPos);
  yPos += 6;
  pdf.text('GSTIN: 29**********2Z5', 20, yPos);
  yPos += 6;
  pdf.text('CIN: U12345KA2020PTC012345', 20, yPos);
  yPos += 6;
  pdf.text('PAN No: **********', 20, yPos);

  // Authorized Signatory
  pdf.setFont('helvetica', 'bold');
  pdf.text('Authorized Signatory', 120, yPos - 48);
  pdf.setFont('helvetica', 'normal');
  pdf.text('For RedBeryl Tech Solutions Pvt. Ltd.', 120, yPos - 10);

  // Footer
  yPos += 20;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'italic');
  pdf.text('Thank you for doing business with us.', 105, yPos, { align: 'center' });
};



