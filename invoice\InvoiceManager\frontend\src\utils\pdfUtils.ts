import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types/invoice';

/**
 * Generate PDF blob from invoice data for OneDrive upload
 */
export const generateInvoicePdfBlob = async (invoice: Invoice): Promise<Blob> => {
  try {
    // Validate invoice data
    if (!invoice || !invoice.id) {
      throw new Error('Invalid invoice data provided');
    }

    console.log('Starting client-side PDF generation for invoice:', invoice.id);

    // Use jsPDF directly to create the PDF with text content
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // Add content directly to PDF
    addInvoiceContentToPdf(pdf, invoice);

    // Convert to blob
    const pdfBlob = pdf.output('blob');

    if (!pdfBlob || pdfBlob.size === 0) {
      throw new Error('Generated PDF is empty');
    }

    console.log(`PDF generated successfully, size: ${pdfBlob.size} bytes`);
    return pdfBlob;
  } catch (error) {
    console.error('Error in client-side PDF generation:', error);
    throw new Error('Failed to generate PDF: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Generate PDF base64 string from invoice data for OneDrive upload
 */
export const generateInvoicePdfBase64 = async (invoice: Invoice): Promise<string> => {
  try {
    const pdfBlob = await generateInvoicePdfBlob(invoice);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error generating PDF base64:', error);
    throw new Error('Failed to generate PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to blob for OneDrive upload
 */
export const fetchInvoicePdfBlob = async (invoiceId: string | number): Promise<Blob> => {
  try {
    // Try multiple API endpoints with better error handling
    const apiUrls = [
      `/api/invoice-generation/public/pdf/${invoiceId}`,
      `/api/invoice-generation/public/pdf/by-number/${invoiceId}`,
      `/api/invoice-generation/pdf/${invoiceId}`
    ];

    let lastError: Error | null = null;
    const errors: string[] = [];

    for (const url of apiUrls) {
      try {
        console.log(`Attempting to fetch PDF from: ${url}`);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
            'Cache-Control': 'no-cache'
          },
          // Add timeout
          signal: AbortSignal.timeout(30000) // 30 seconds timeout
        });

        console.log(`Response status: ${response.status} for ${url}`);

        if (response.ok) {
          const blob = await response.blob();
          console.log(`Blob size: ${blob.size} bytes, type: ${blob.type}`);

          if (blob.size > 0 && blob.type === 'application/pdf') {
            console.log(`Successfully fetched valid PDF from: ${url}`);
            return blob;
          } else {
            errors.push(`${url}: Invalid PDF (size: ${blob.size}, type: ${blob.type})`);
          }
        } else {
          const errorText = await response.text();
          errors.push(`${url}: HTTP ${response.status} - ${errorText}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to fetch from ${url}:`, errorMessage);
        errors.push(`${url}: ${errorMessage}`);
        lastError = error instanceof Error ? error : new Error(errorMessage);
      }
    }

    // Create detailed error message
    const errorDetails = errors.join('; ');
    throw new Error(`Failed to fetch PDF from all endpoints: ${errorDetails}`);
  } catch (error) {
    console.error('Error fetching PDF blob:', error);
    throw new Error('Unable to fetch PDF from backend: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to base64 for OneDrive upload
 */
export const fetchInvoicePdfBase64 = async (invoiceId: string | number): Promise<string> => {
  try {
    const pdfBlob = await fetchInvoicePdfBlob(invoiceId);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error fetching PDF base64:', error);
    throw new Error('Failed to fetch PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Download PDF blob as file
 */
export const downloadPdfBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * Add invoice content directly to PDF using jsPDF - matching the exact format from screenshot
 */
const addInvoiceContentToPdf = (pdf: jsPDF, invoice: Invoice): void => {
  // Calculate amounts first
  const baseAmount = parseFloat(invoice.amount?.replace(/[₹,]/g, '') || '50000');
  const cgstAmount = baseAmount * 0.09;
  const sgstAmount = baseAmount * 0.09;
  const totalAmount = baseAmount + cgstAmount + sgstAmount;

  // Format dates
  const invoiceDate = new Date(invoice.issueDate).toLocaleDateString('en-GB');
  const invoiceNumber = `RB/24-25/${invoice.id.toString().padStart(3, '0')}`;
  const invoiceMonth = new Date(invoice.issueDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  const joiningDate = new Date(invoice.joiningDate || invoice.issueDate).toLocaleDateString('en-GB');

  // Add RedBeryl logo (simplified representation)
  addRedBerylLogo(pdf);

  // INVOICE header - centered and underlined
  pdf.setFontSize(28);
  pdf.setFont('helvetica', 'bold');
  pdf.text('INVOICE', 105, 40, { align: 'center' });

  // Add underline for INVOICE
  pdf.setLineWidth(0.5);
  pdf.line(85, 42, 125, 42);

  // Two-column layout for Invoice Details and Billed To
  let yStart = 60;

  // Left column - Invoice Details
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Invoice Details :-', 20, yStart);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(10);

  let yPos = yStart + 10;
  pdf.text(`Invoice Date: ${invoiceDate}`, 20, yPos);
  yPos += 8;
  pdf.text(`Invoice No.: ${invoiceNumber}`, 20, yPos);
  yPos += 8;
  pdf.text(`Invoice Month: ${invoiceMonth}`, 20, yPos);
  yPos += 8;
  pdf.text(`Invoice For: ${invoice.invoiceType || 'Services'}`, 20, yPos);
  yPos += 8;
  pdf.text(`HSN No.: ${invoice.hsnCode || '465757'}`, 20, yPos);
  yPos += 8;
  pdf.text(`Employee Name: ${invoice.candidate || 'Prathamesh Kadam'}`, 20, yPos);
  yPos += 8;
  pdf.text(`Employee Engagement Code: ENG-0020`, 20, yPos);

  // Right column - Billed To
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.text('Billed To :-', 120, yStart);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(10);
  yPos = yStart + 10;
  pdf.text(`${invoice.client || 'saurabh'}`, 120, yPos);
  yPos += 8;
  pdf.text(`${invoice.project || 'hadapsar'}`, 120, yPos);
  yPos += 8;
  pdf.text('GST No:', 120, yPos);

  // Billing table - exact format from screenshot
  yPos = 140;
  addBillingTable(pdf, invoice, yPos, baseAmount, cgstAmount, sgstAmount, totalAmount, joiningDate);

  // GST Type information
  yPos = 200;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(21, 101, 192); // Blue color
  pdf.text('GST Type: Intra State (Maharashtra) - CGST (9%) + SGST (9%) = 18%', 20, yPos);

  // Net Payable
  yPos += 12;
  pdf.setTextColor(0, 0, 0);
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text(`Net Payable: ₹${totalAmount.toLocaleString('en-IN')}.00 /- (Fifty Nine Thousand Only)`, 20, yPos);

  // Payment Information and Authorized Signatory tables
  yPos += 20;
  addPaymentAndSignatureSection(pdf, yPos);

  // Footer
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'italic');
  pdf.setTextColor(0, 0, 0);
  pdf.text('Thank you for doing business with us.', 105, 280, { align: 'center' });
};

/**
 * Add RedBeryl logo to PDF
 */
const addRedBerylLogo = (pdf: jsPDF): void => {
  // Logo position - top right
  const logoX = 150;
  const logoY = 15;

  // Draw simplified logo representation
  pdf.setFillColor(233, 30, 99); // Pink color for "Red"
  pdf.circle(logoX, logoY, 3, 'F');

  pdf.setFillColor(21, 101, 192); // Blue color for "Beryl"
  pdf.circle(logoX + 8, logoY, 3, 'F');

  // Company name
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(233, 30, 99);
  pdf.text('Red', logoX + 15, logoY + 2);

  pdf.setTextColor(21, 101, 192);
  pdf.text('Beryl', logoX + 32, logoY + 2);

  // Tagline
  pdf.setFontSize(8);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(66, 66, 66);
  pdf.text('TECH SOLUTIONS', logoX + 15, logoY + 8);

  pdf.setFontSize(7);
  pdf.setFont('helvetica', 'italic');
  pdf.setTextColor(102, 102, 102);
  pdf.text('Integrates Business With Technology', logoX + 15, logoY + 13);

  // Reset color
  pdf.setTextColor(0, 0, 0);
};

/**
 * Add billing table with exact format from screenshot
 */
const addBillingTable = (
  pdf: jsPDF,
  invoice: Invoice,
  yPos: number,
  baseAmount: number,
  cgstAmount: number,
  sgstAmount: number,
  totalAmount: number,
  joiningDate: string
): void => {
  // Table dimensions
  const tableX = 20;
  const tableWidth = 170;
  const colWidths = [35, 25, 25, 25, 30, 30]; // Employee, Joining, Rate, Bill, GST, Total

  // Main header row
  pdf.setFillColor(240, 240, 240);
  pdf.rect(tableX, yPos, tableWidth, 12, 'F');
  pdf.rect(tableX, yPos, tableWidth, 12);

  // Draw column separators for main header
  let xPos = tableX;
  for (let i = 0; i < colWidths.length - 1; i++) {
    xPos += colWidths[i];
    pdf.line(xPos, yPos, xPos, yPos + 12);
  }

  // Header text
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(9);
  pdf.text('Employee Name', tableX + 2, yPos + 8);
  pdf.text('Joining Date', tableX + 37, yPos + 8);
  pdf.text('Rate', tableX + 67, yPos + 8);
  pdf.text('Bill Amount', tableX + 87, yPos + 8);
  pdf.text('GST', tableX + 127, yPos + 8);
  pdf.text('Total Bill Amount', tableX + 142, yPos + 8);

  // GST sub-header row
  yPos += 12;
  pdf.setFillColor(250, 250, 250);
  pdf.rect(tableX + 112, yPos, 58, 8, 'F');
  pdf.rect(tableX + 112, yPos, 58, 8);

  // GST column separators
  pdf.line(tableX + 127, yPos, tableX + 127, yPos + 8);
  pdf.line(tableX + 142, yPos, tableX + 142, yPos + 8);
  pdf.line(tableX + 157, yPos, tableX + 157, yPos + 8);

  pdf.setFontSize(7);
  pdf.text('CGST @9%', tableX + 114, yPos + 5);
  pdf.text('SGST @9%', tableX + 129, yPos + 5);
  pdf.text('IGST @18%', tableX + 144, yPos + 5);

  // Data row
  yPos += 8;
  pdf.rect(tableX, yPos, tableWidth, 12);

  // Draw column separators for data row
  xPos = tableX;
  for (let i = 0; i < colWidths.length - 1; i++) {
    xPos += colWidths[i];
    pdf.line(xPos, yPos, xPos, yPos + 12);
  }

  // Additional GST column separators
  pdf.line(tableX + 127, yPos, tableX + 127, yPos + 12);
  pdf.line(tableX + 142, yPos, tableX + 142, yPos + 12);
  pdf.line(tableX + 157, yPos, tableX + 157, yPos + 12);

  // Data text
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);
  pdf.text(invoice.candidate || 'Prathamesh Kadam', tableX + 2, yPos + 8);
  pdf.text(joiningDate, tableX + 37, yPos + 8);
  pdf.text(`₹${baseAmount.toLocaleString('en-IN')}.00`, tableX + 47, yPos + 8);
  pdf.text(`₹${baseAmount.toLocaleString('en-IN')}.00`, tableX + 72, yPos + 8);
  pdf.text(`₹${cgstAmount.toLocaleString('en-IN')}.00`, tableX + 114, yPos + 8);
  pdf.text(`₹${sgstAmount.toLocaleString('en-IN')}.00`, tableX + 129, yPos + 8);
  pdf.text('₹0.00', tableX + 144, yPos + 8);
  pdf.text(`₹${totalAmount.toLocaleString('en-IN')}.00`, tableX + 157, yPos + 8);
};

/**
 * Add payment information and authorized signatory section
 */
const addPaymentAndSignatureSection = (pdf: jsPDF, yPos: number): void => {
  const tableX = 20;
  const tableWidth = 170;
  const leftColWidth = 85;
  const rightColWidth = 85;

  // Main table border
  pdf.rect(tableX, yPos, tableWidth, 60);

  // Vertical separator
  pdf.line(tableX + leftColWidth, yPos, tableX + leftColWidth, yPos + 60);

  // Left column header
  pdf.setFillColor(240, 240, 240);
  pdf.rect(tableX, yPos, leftColWidth, 12, 'F');
  pdf.rect(tableX, yPos, leftColWidth, 12);

  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(10);
  pdf.text('Payment Information', tableX + leftColWidth/2, yPos + 8, { align: 'center' });

  // Right column header
  pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12, 'F');
  pdf.rect(tableX + leftColWidth, yPos, rightColWidth, 12);
  pdf.text('Authorized Signatory', tableX + leftColWidth + rightColWidth/2, yPos + 8, { align: 'center' });

  // Payment information content
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(9);
  let leftYPos = yPos + 20;

  const paymentInfo = [
    'Bank Name: HDFC Bank',
    'Branch Name: MG Road Branch',
    'Account Name: Acme Corporation Pvt Ltd',
    'Account No: ***********',
    'IFSC Code: HDFC0001234',
    'Account Type: Current',
    'GSTIN: 29**********2Z5',
    'CIN: U12345KA2020PTC012345',
    'PAN No: **********'
  ];

  paymentInfo.forEach((info, index) => {
    pdf.text(info, tableX + 2, leftYPos + (index * 5));
  });

  // Authorized signatory content
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(10);
  pdf.text('For RedBeryl Tech Solutions Pvt. Ltd.', tableX + leftColWidth + 5, yPos + 50);
};



