package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DealDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.DealService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

import java.util.ArrayList;
import java.util.List;

/**
 * Public controller for deals that doesn't require authentication
 * This is useful for testing and development purposes
 */
@RestController
@RequestMapping("/api/deals/public")
@Tag(name = "Public Deal API", description = "Public API for deals (no authentication required)")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS, RequestMethod.HEAD, RequestMethod.PATCH}, maxAge = 3600)
public class PublicDealController {

    @Autowired
    private DealService dealService;

    @GetMapping
    @Operation(summary = "Get all deals (public)", description = "Get all deals without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deals found"),
            @ApiResponse(responseCode = "204", description = "No deals found", content = @Content)
    })
    public ResponseEntity<List<DealDto>> getAllDeals() {
        try {
            List<DealDto> deals = dealService.getAllDeals();

            // If no deals found, return empty list
            if (deals == null || deals.isEmpty()) {
                System.out.println("No deals found in database, returning empty list");
                return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
            }

            return new ResponseEntity<>(deals, HttpStatus.OK);
        } catch (NoContentException e) {
            System.out.println("NoContentException: " + e.getMessage());
            // Return empty list instead of 204 No Content
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        } catch (Exception e) {
            System.out.println("Exception in getAllDeals: " + e.getMessage());
            e.printStackTrace();
            // Return empty list instead of sample data
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get deal by ID (public)", description = "Get deal by ID without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deal found"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<DealDto> getDealById(@PathVariable Long id) {
        try {
            DealDto deal = dealService.getDealById(id);
            return new ResponseEntity<>(deal, HttpStatus.OK);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get deals by status (public)", description = "Get deals by status without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deals found"),
            @ApiResponse(responseCode = "204", description = "No deals found with this status"),
            @ApiResponse(responseCode = "400", description = "Invalid status supplied")
    })
    public ResponseEntity<List<DealDto>> getDealsByStatus(@PathVariable String status) {
        try {
            List<DealDto> deals = dealService.getDealsByStatus(status);
            return new ResponseEntity<>(deals, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            // Return empty list instead of error for better user experience
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/sample")
    @Operation(summary = "Get sample deals", description = "Get sample deals for testing")
    public ResponseEntity<List<DealDto>> getSampleDeals() {
        // Return empty list instead of sample data
        System.out.println("Sample deals endpoint called, returning empty list");
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
    }

    @GetMapping("/create-test-data")
    @Operation(summary = "Create test deals", description = "This endpoint no longer creates test data")
    public ResponseEntity<List<DealDto>> createTestDeals() {
        System.out.println("Create test data endpoint called, but sample data generation has been disabled");

        try {
            // Check if we already have deals in the database
            try {
                List<DealDto> existingDeals = dealService.getAllDeals();
                if (existingDeals != null && !existingDeals.isEmpty()) {
                    System.out.println("Returning existing deals from database");
                    return new ResponseEntity<>(existingDeals, HttpStatus.OK);
                }
            } catch (Exception e) {
                System.out.println("No existing deals found");
            }

            // Return empty list
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("Error in createTestDeals: " + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @PostMapping
    @Operation(summary = "Create deal (public)", description = "Create a new deal without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Deal created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation")
    })
    public ResponseEntity<DealDto> createDeal(@Valid @RequestBody DealDto dealDto) {
        try {
            DealDto createdDeal = dealService.createDeal(dealDto);
            return new ResponseEntity<>(createdDeal, HttpStatus.CREATED);
        } catch (Exception e) {
            // Log the error
            System.err.println("Error creating deal: " + e.getMessage());
            e.printStackTrace();

            // Create a simple response with the error
            DealDto errorResponse = new DealDto();
            errorResponse.setProjectName("Error: " + e.getMessage());
            errorResponse.setStatus("error");
            return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update deal (public)", description = "Update an existing deal without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deal updated successfully"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation")
    })
    @CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowedHeaders = "*", methods = {RequestMethod.PUT, RequestMethod.OPTIONS})
    public ResponseEntity<DealDto> updateDeal(@PathVariable Long id, @Valid @RequestBody DealDto dealDto) {
        try {
            DealDto updatedDeal = dealService.updateDeal(id, dealDto);
            return new ResponseEntity<>(updatedDeal, HttpStatus.OK);
        } catch (Exception e) {
            // Log the error
            System.err.println("Error updating deal: " + e.getMessage());
            e.printStackTrace();

            // Create a simple response with the error
            DealDto errorResponse = new DealDto();
            errorResponse.setProjectName("Error: " + e.getMessage());
            errorResponse.setStatus("error");
            return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete deal (public)", description = "Delete a deal by its ID without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Deal deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<Void> deleteDeal(@PathVariable Long id) {
        try {
            dealService.deleteDeal(id);
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        } catch (Exception e) {
            // Log the error
            System.err.println("Error deleting deal: " + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }
}
