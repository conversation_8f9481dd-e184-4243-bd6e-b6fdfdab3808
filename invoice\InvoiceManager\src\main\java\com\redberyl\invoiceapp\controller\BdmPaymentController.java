package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.BdmPaymentDto;
import com.redberyl.invoiceapp.service.BdmPaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/bdm-payments")
@Tag(name = "BDM Payment", description = "BDM Payment management API")
public class BdmPaymentController {

    @Autowired
    private BdmPaymentService bdmPaymentService;

    @GetMapping
    @Operation(summary = "Get all BDM payments", description = "Retrieve a list of all BDM payments")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmPaymentDto>> getAllBdmPayments() {
        List<BdmPaymentDto> bdmPayments = bdmPaymentService.getAllBdmPayments();
        return new ResponseEntity<>(bdmPayments, HttpStatus.OK);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get BDM payment by ID", description = "Retrieve a BDM payment by its ID")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmPaymentDto> getBdmPaymentById(@PathVariable Long id) {
        BdmPaymentDto bdmPayment = bdmPaymentService.getBdmPaymentById(id);
        return new ResponseEntity<>(bdmPayment, HttpStatus.OK);
    }

    @GetMapping("/bdm/{bdmId}")
    @Operation(summary = "Get BDM payments by BDM ID", description = "Retrieve all BDM payments for a specific BDM")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmPaymentDto>> getBdmPaymentsByBdmId(@PathVariable Long bdmId) {
        List<BdmPaymentDto> bdmPayments = bdmPaymentService.getBdmPaymentsByBdmId(bdmId);
        return new ResponseEntity<>(bdmPayments, HttpStatus.OK);
    }

    @GetMapping("/invoice/{invoiceId}")
    @Operation(summary = "Get BDM payments by invoice ID", description = "Retrieve all BDM payments for a specific invoice")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<BdmPaymentDto>> getBdmPaymentsByInvoiceId(@PathVariable Long invoiceId) {
        List<BdmPaymentDto> bdmPayments = bdmPaymentService.getBdmPaymentsByInvoiceId(invoiceId);
        return new ResponseEntity<>(bdmPayments, HttpStatus.OK);
    }

    @PostMapping
    @Operation(summary = "Create BDM payment", description = "Create a new BDM payment")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmPaymentDto> createBdmPayment(@Valid @RequestBody BdmPaymentDto bdmPaymentDto) {
        BdmPaymentDto createdBdmPayment = bdmPaymentService.createBdmPayment(bdmPaymentDto);
        return new ResponseEntity<>(createdBdmPayment, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update BDM payment", description = "Update an existing BDM payment")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<BdmPaymentDto> updateBdmPayment(@PathVariable Long id, @Valid @RequestBody BdmPaymentDto bdmPaymentDto) {
        BdmPaymentDto updatedBdmPayment = bdmPaymentService.updateBdmPayment(id, bdmPaymentDto);
        return new ResponseEntity<>(updatedBdmPayment, HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete BDM payment", description = "Delete a BDM payment by its ID")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteBdmPayment(@PathVariable Long id) {
        bdmPaymentService.deleteBdmPayment(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
