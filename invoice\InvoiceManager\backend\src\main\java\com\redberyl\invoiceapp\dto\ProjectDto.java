package com.redberyl.invoiceapp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.redberyl.invoiceapp.util.IdConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Project data transfer object")
public class ProjectDto extends BaseDto {
    @Schema(description = "Project ID", example = "1")
    private Long id;

    @NotNull(message = "Client ID is required")
    @Schema(description = "Client ID", example = "1", required = true)
    private Long clientId;

    @Schema(description = "Client details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private ClientDto client;

    @NotBlank(message = "Project name is required")
    @Schema(description = "Project name", example = "Website Development", required = true)
    private String name;

    @Schema(description = "HSN Code ID", example = "1")
    private Long hsnCodeId;

    @Schema(description = "HSN Code details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private HsnCodeDto hsnCode;

    @Schema(description = "Project description", example = "Development of a responsive website with e-commerce functionality")
    private String description;

    @Email(message = "Invalid email format")
    @Schema(description = "Project email", example = "<EMAIL>")
    private String email;

    @Schema(description = "Project phone number", example = "1234567890")
    private String phone;

    @Schema(description = "GST number", example = "29ABCDE1234F1Z5")
    private String gstNumber;

    @Schema(description = "Billing address", example = "123 Main St, Bangalore, Karnataka")
    private String billingAddress;

    @Schema(description = "Shipping address", example = "456 Business Park, Mumbai, Maharashtra")
    private String shippingAddress;

    @Schema(description = "Project state", example = "Maharashtra")
    private String state;

    @Schema(description = "Engagement code", example = "ENG-2023-001")
    private String engagementCode;

    @Schema(description = "Client partner name", example = "John Doe")
    private String clientPartnerName;

    @Email(message = "Invalid client partner email format")
    @Schema(description = "Client partner email", example = "<EMAIL>")
    private String clientPartnerEmail;

    @Schema(description = "Client partner phone", example = "9876543210")
    private String clientPartnerPhone;

    @Schema(description = "BDM ID", example = "1")
    private Long bdmId;

    @Schema(description = "BDM details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private BdmDto bdm;

    @Schema(description = "Commission percentage", example = "10.5")
    private BigDecimal commissionPercentage;

    @Schema(description = "Commission amount", example = "5000.00")
    private BigDecimal commissionAmount;

    @Schema(description = "Project start date", example = "2023-01-01")
    private LocalDate startDate;

    @Schema(description = "Project end date", example = "2023-12-31")
    private LocalDate endDate;

    @Schema(description = "Project status", example = "In Progress", allowableValues = {"Not Started", "In Progress", "On Hold", "Completed", "Cancelled"})
    private String status;

    @Schema(description = "Project value", example = "50000.00")
    private BigDecimal value;

    @Schema(description = "Manager SPOC ID", example = "1")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long managerSpocId;

    @Schema(description = "Manager SPOC details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SpocDto managerSpoc;

    @Schema(description = "Account Head SPOC ID", example = "2")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long accountHeadSpocId;

    @Schema(description = "Account Head SPOC details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SpocDto accountHeadSpoc;

    @Schema(description = "Business Head SPOC ID", example = "3")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long businessHeadSpocId;

    @Schema(description = "Business Head SPOC details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SpocDto businessHeadSpoc;

    @Schema(description = "HR SPOC ID", example = "4")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long hrSpocId;

    @Schema(description = "HR SPOC details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SpocDto hrSpoc;

    @Schema(description = "Finance SPOC ID", example = "5")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Long financeSpocId;

    @Schema(description = "Finance SPOC details", hidden = true)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SpocDto financeSpoc;
}
